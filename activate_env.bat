@echo off
echo ================================================
echo    AI桩基完整性分析系统 - 虚拟环境
echo ================================================
echo.
echo 正在激活虚拟环境...
call F:\2025\AIpile\AIpiles_final\ai_pile_env\Scripts\activate.bat
echo.
echo ✓ 虚拟环境已激活！
echo.
echo 当前Python路径：
where python
echo.
echo ================================================
echo 可用命令：
echo ================================================
echo   python ai_pile_integrity_analyzer.py  - 运行主程序
echo   python auto_train_and_classify.py     - 运行自动训练程序
echo   python test_dependencies.py           - 测试依赖安装
echo   deactivate                            - 退出虚拟环境
echo   exit                                  - 关闭窗口
echo.
echo 提示：直接输入程序名即可运行，例如：
echo   python ai_pile_integrity_analyzer.py
echo.
echo ================================================
cmd /k
