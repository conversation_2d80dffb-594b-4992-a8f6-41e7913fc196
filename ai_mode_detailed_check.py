#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI模式详细检查脚本
专门检查AI模式的具体问题
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import traceback

# 强制清除模块缓存
modules_to_clear = [k for k in sys.modules.keys() if 'Pile_analyze_GZ_gui' in k]
for module in modules_to_clear:
    del sys.modules[module]

def check_classifier_issue():
    """检查classifier属性问题"""
    print("🔍 检查classifier属性问题")
    print("=" * 80)
    
    try:
        from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
        
        # 创建分析器
        analyzer = BuiltInAIAnalyzer()
        
        print(f"📋 初始化后的属性:")
        print(f"  - classifier_model: {type(analyzer.classifier_model) if analyzer.classifier_model else 'None'}")
        print(f"  - anomaly_detector: {type(analyzer.anomaly_detector) if analyzer.anomaly_detector else 'None'}")
        print(f"  - scaler: {type(analyzer.scaler) if analyzer.scaler else 'None'}")
        
        # 检查是否有classifier属性（诊断中显示False的问题）
        print(f"  - hasattr(analyzer, 'classifier'): {hasattr(analyzer, 'classifier')}")
        print(f"  - hasattr(analyzer, 'classifier_model'): {hasattr(analyzer, 'classifier_model')}")
        
        # 检查模型是否已训练
        if analyzer.classifier_model:
            print(f"  - classifier_model已训练: {hasattr(analyzer.classifier_model, 'classes_')}")
            if hasattr(analyzer.classifier_model, 'classes_'):
                print(f"  - 类别: {analyzer.classifier_model.classes_}")
        
        return analyzer
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        traceback.print_exc()
        return None

def check_model_loading_issue(analyzer):
    """检查模型加载后classifier变为None的问题"""
    print("\n🔍 检查模型加载后classifier变为None的问题")
    print("=" * 80)
    
    if analyzer is None:
        print("❌ 分析器不可用")
        return
    
    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
    
    print(f"📋 加载前状态:")
    print(f"  - classifier_model: {type(analyzer.classifier_model) if analyzer.classifier_model else 'None'}")
    
    try:
        # 直接检查模型文件内容
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        print(f"📋 模型文件内容:")
        print(f"  - 类型: {type(model_data)}")
        print(f"  - 是否有predict方法: {hasattr(model_data, 'predict')}")
        print(f"  - 是否有classes_: {hasattr(model_data, 'classes_')}")
        
        if hasattr(model_data, 'classes_'):
            print(f"  - 类别: {model_data.classes_}")
        
        # 加载模型
        success = analyzer.load_models(model_path)
        
        print(f"📋 加载后状态:")
        print(f"  - 加载成功: {success}")
        print(f"  - classifier_model: {type(analyzer.classifier_model) if analyzer.classifier_model else 'None'}")
        
        # 检查为什么classifier_model变为None
        if analyzer.classifier_model is None:
            print("⚠️ classifier_model变为None，检查load_models方法逻辑...")
            
            # 重新手动设置
            analyzer.classifier_model = model_data
            print(f"  - 手动设置后: {type(analyzer.classifier_model) if analyzer.classifier_model else 'None'}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        traceback.print_exc()

def check_prediction_accuracy(analyzer):
    """检查预测准确性问题"""
    print("\n🔍 检查预测准确性问题")
    print("=" * 80)
    
    if analyzer is None:
        print("❌ 分析器不可用")
        return
    
    # 测试多个已知类别的样本
    test_cases = [
        ("F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt", "I类桩", 0),
        ("F:/2025/AIpile/AIpiles_final/training_data/II/KBZ1-67.txt", "II类桩", 1),
        ("F:/2025/AIpile/AIpiles_final/training_data/III/1-2.txt", "III类桩", 2),
        ("F:/2025/AIpile/AIpiles_final/training_data/IV/KBZ1-40.txt", "IV类桩", 3),
    ]
    
    correct_predictions = 0
    total_predictions = 0
    
    for file_path, expected_class, expected_num in test_cases:
        if not os.path.exists(file_path):
            continue
        
        filename = os.path.basename(file_path)
        
        try:
            # 读取数据
            df = pd.read_csv(file_path, sep='\t')
            
            # 提取特征
            features, _ = analyzer.extract_features(df)
            
            # 进行预测
            result = analyzer.predict(features)
            
            if result:
                predicted_num = result['完整性类别']
                confidence = result['ai_confidence']
                
                is_correct = predicted_num == expected_num
                if is_correct:
                    correct_predictions += 1
                total_predictions += 1
                
                status = "✅" if is_correct else "❌"
                print(f"{status} {filename:20s}")
                print(f"   期望: {expected_class:8s} ({expected_num}) | 预测: {predicted_num} | 置信度: {confidence:.2%}")
                
                # 显示类别概率
                class_probs = result.get('class_probabilities', {})
                prob_str = " | ".join([f"{k}:{v:.1%}" for k, v in class_probs.items()])
                print(f"   概率分布: {prob_str}")
                print()
                
        except Exception as e:
            print(f"❌ {filename}: 处理失败 - {e}")
    
    if total_predictions > 0:
        accuracy = correct_predictions / total_predictions
        print(f"📊 预测准确率: {correct_predictions}/{total_predictions} = {accuracy:.2%}")
        
        if accuracy < 0.5:
            print("⚠️ 预测准确率过低，可能存在以下问题:")
            print("   1. 模型训练数据不足或质量差")
            print("   2. 特征提取方法不当")
            print("   3. 模型参数需要调优")
            print("   4. 数据预处理问题")

def check_gui_ai_integration():
    """检查GUI中AI集成问题"""
    print("\n🔍 检查GUI中AI集成问题")
    print("=" * 80)
    
    try:
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        # 检查AI相关方法
        ai_methods = [
            'run_ai_analysis',
            'display_ai_result',
            'create_comparison_text',
            'load_ai_model',
            'save_ai_model'
        ]
        
        for method in ai_methods:
            if hasattr(PileAnalyzerGZGUI, method):
                print(f"✅ GUI方法存在: {method}")
            else:
                print(f"❌ GUI方法缺失: {method}")
        
        # 检查AI分析器初始化
        print(f"\n📋 检查GUI中AI分析器初始化:")
        
        # 模拟GUI初始化（不创建实际窗口）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        try:
            gui = PileAnalyzerGZGUI()
            
            if hasattr(gui, 'ai_analyzer'):
                print(f"✅ GUI中ai_analyzer存在: {type(gui.ai_analyzer)}")
                
                # 检查ai_analyzer的状态
                ai_analyzer = gui.ai_analyzer
                print(f"  - classifier_model: {type(ai_analyzer.classifier_model) if ai_analyzer.classifier_model else 'None'}")
                print(f"  - anomaly_detector: {type(ai_analyzer.anomaly_detector) if ai_analyzer.anomaly_detector else 'None'}")
                
            else:
                print(f"❌ GUI中ai_analyzer不存在")
            
            root.destroy()
            
        except Exception as e:
            print(f"❌ GUI初始化失败: {e}")
            root.destroy()
        
    except Exception as e:
        print(f"❌ GUI集成检查失败: {e}")
        traceback.print_exc()

def check_script_execution():
    """检查脚本执行AI模式的问题"""
    print("\n🔍 检查脚本执行AI模式的问题")
    print("=" * 80)
    
    try:
        from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
        
        # 模拟完整的AI分析流程
        analyzer = BuiltInAIAnalyzer()
        
        # 加载外部模型
        model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
        if os.path.exists(model_path):
            success = analyzer.load_models(model_path)
            print(f"✅ 模型加载: {'成功' if success else '失败'}")
        
        # 测试数据文件
        test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"
        
        if os.path.exists(test_file):
            # 读取数据
            df = pd.read_csv(test_file, sep='\t')
            print(f"✅ 数据读取成功: {df.shape}")
            
            # 特征提取
            features, feature_names = analyzer.extract_features(df)
            print(f"✅ 特征提取成功: {features.shape}")
            
            # AI预测
            result = analyzer.predict(features)
            
            if result:
                print(f"✅ AI预测成功")
                
                # 检查结果格式
                expected_keys = ['完整性类别', 'ai_confidence', 'anomaly_score', 'class_probabilities', 'overall_reasoning']
                for key in expected_keys:
                    if key in result:
                        print(f"  ✅ 结果包含: {key}")
                    else:
                        print(f"  ❌ 结果缺失: {key}")
                
                # 检查类别映射
                category = result.get('完整性类别')
                if isinstance(category, (int, float, np.integer, np.floating)):
                    category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                    mapped_category = category_mapping.get(int(category), f'未知类别({category})')
                    print(f"  ✅ 类别映射: {category} -> {mapped_category}")
                
                # 检查推理结果
                reasoning = result.get('overall_reasoning', '')
                if '类桩' in reasoning:
                    print(f"  ✅ 推理包含中文类别名称")
                else:
                    print(f"  ❌ 推理不包含中文类别名称")
                
            else:
                print(f"❌ AI预测失败")
        
    except Exception as e:
        print(f"❌ 脚本执行检查失败: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    print("🔬 AI模式详细检查")
    print("=" * 100)
    
    # 1. 检查classifier属性问题
    analyzer = check_classifier_issue()
    
    # 2. 检查模型加载问题
    check_model_loading_issue(analyzer)
    
    # 3. 检查预测准确性
    check_prediction_accuracy(analyzer)
    
    # 4. 检查GUI集成
    check_gui_ai_integration()
    
    # 5. 检查脚本执行
    check_script_execution()
    
    print("\n📋 AI模式详细检查完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
