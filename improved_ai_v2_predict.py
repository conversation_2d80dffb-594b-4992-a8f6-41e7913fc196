
    def predict_with_auto_features(self, df):
        """带自动特征匹配的预测"""
        try:
            print("🔍 开始自动特征匹配预测...")
            
            # 获取当前模型
            current_model = self.model_manager.get_current_model()
            current_model_info = self.model_manager.get_current_model_info()
            
            if current_model is None:
                raise ValueError("No model loaded")
            
            print(f"🎯 使用模型: {current_model_info.name}")
            
            # 自动匹配特征
            features, feature_names, extractor_used = self.auto_match_features_for_model(current_model, df)
            
            if features is None:
                raise ValueError("Failed to match features for model")
            
            print(f"✅ 特征匹配成功: {features.shape[1]} 特征, 使用 {extractor_used}")
            
            # 进行预测
            if current_model_info.model_type == 'optimized':
                result = self._predict_optimized(features, current_model, current_model_info)
            else:
                result = self._predict_standard(features, current_model, current_model_info)
            
            # 添加类别名称
            class_names = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
            result['类别名称'] = class_names[result['完整性类别']]
            
            return result
            
        except Exception as e:
            print(f"❌ 自动特征匹配预测失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def auto_match_features_for_model(self, model_data, df):
        """自动为模型匹配正确的特征"""
        try:
            # 获取模型期望的特征数量
            classifier = model_data.get('classifier_model') or model_data.get('model')
            
            if not classifier:
                raise ValueError("No classifier found in model")
            
            expected_features = None
            
            # 尝试获取期望特征数
            if hasattr(classifier, 'n_features_in_'):
                expected_features = classifier.n_features_in_
            elif hasattr(classifier, 'estimators_'):
                for estimator in classifier.estimators_:
                    if hasattr(estimator, 'n_features_in_'):
                        expected_features = estimator.n_features_in_
                        break
            
            if expected_features is None:
                print("⚠️ 无法确定模型期望的特征数量，尝试使用高精度特征")
                # 默认使用高精度特征提取器
                self.set_feature_extractor('advanced')
                features, feature_names = self.extract_features(df)
                return features, feature_names, 'advanced'
            
            print(f"🎯 模型期望特征数: {expected_features}")
            
            # 根据期望特征数选择合适的特征提取器
            extractors = self.feature_manager.get_available_extractors()
            
            for extractor_name in extractors:
                try:
                    self.set_feature_extractor(extractor_name)
                    features, feature_names = self.extract_features(df)
                    feature_count = features.shape[1] if features.ndim > 1 else len(features)
                    
                    print(f"🔍 {extractor_name}: {feature_count} 特征")
                    
                    # 如果特征数量匹配
                    if feature_count == expected_features:
                        print(f"✅ 找到匹配的特征提取器: {extractor_name}")
                        return features, feature_names, extractor_name
                        
                except Exception as e:
                    print(f"⚠️ {extractor_name} 测试失败: {e}")
            
            # 如果没有完全匹配的，尝试特征选择
            if 'feature_selector' in model_data:
                print("🔧 尝试使用特征选择器...")
                
                # 使用高精度特征提取器
                self.set_feature_extractor('advanced')
                features, feature_names = self.extract_features(df)
                
                # 应用特征选择器
                feature_selector = model_data['feature_selector']
                
                if hasattr(feature_selector, 'transform'):
                    selected_features = feature_selector.transform(features.reshape(1, -1))
                    
                    if selected_features.shape[1] == expected_features:
                        print(f"✅ 特征选择后匹配: {selected_features.shape[1]} 特征")
                        return selected_features, [f"selected_feature_{i}" for i in range(selected_features.shape[1])], 'advanced_with_selection'
            
            # 如果还是不匹配，抛出错误
            raise ValueError(f"无法为模型匹配合适的特征数量。期望: {expected_features}")
            
        except Exception as e:
            print(f"❌ 自动特征匹配失败: {e}")
            raise
