#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能合成数据生成器 - GUI版本
基于机器学习和GZ传统方法的智能桩身完整性数据生成器
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import threading
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import seaborn as sns
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class IntelligentSyntheticDataGenerator:
    """智能合成数据生成器核心类"""

    def __init__(self):
        """初始化生成器"""
        self.learned_distributions = {}
        self.feature_scalers = {}
        self.gz_calculator = GZMethodCalculator()
        self.data_stats = {}

    def learn_from_existing_data(self, data_directory: str, progress_callback=None) -> Dict:
        """从现有数据中学习特征分布"""
        learning_results = {
            'total_samples': 0,
            'class_distributions': {},
            'feature_statistics': {},
            'learning_status': 'success'
        }

        try:
            pile_classes = ['I', 'II', 'III', 'IV']
            total_files = 0
            processed_files = 0

            # 统计总文件数
            for pile_class in pile_classes:
                class_dir = os.path.join(data_directory, pile_class)
                if os.path.exists(class_dir):
                    total_files += len([f for f in os.listdir(class_dir) if f.endswith('.txt')])

            for pile_class in pile_classes:
                class_dir = os.path.join(data_directory, pile_class)
                if not os.path.exists(class_dir):
                    continue

                class_data = []
                class_files = [f for f in os.listdir(class_dir) if f.endswith('.txt')]

                for filename in class_files:
                    try:
                        file_path = os.path.join(class_dir, filename)
                        df = pd.read_csv(file_path, sep='\t')

                        # 标准化列名
                        if len(df.columns) >= 7:
                            df.columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
                            class_data.append(df)

                        processed_files += 1
                        if progress_callback:
                            progress = (processed_files / total_files) * 50  # 学习阶段占50%
                            progress_callback(progress, f"学习 {pile_class}类桩数据: {filename}")

                    except Exception as e:
                        print(f"读取文件 {filename} 失败: {e}")
                        continue

                if class_data:
                    # 合并所有数据
                    combined_data = pd.concat(class_data, ignore_index=True)

                    # 提取特征
                    features = self._extract_features(combined_data)

                    # 学习分布
                    self._learn_class_distribution(f"{pile_class}类桩", features)

                    # 统计信息
                    learning_results['class_distributions'][f"{pile_class}类桩"] = {
                        'sample_count': len(class_data),
                        'total_points': len(combined_data),
                        'feature_means': features.mean().to_dict(),
                        'feature_stds': features.std().to_dict()
                    }

                    learning_results['total_samples'] += len(class_data)

            return learning_results

        except Exception as e:
            learning_results['learning_status'] = f'error: {str(e)}'
            return learning_results

    def _extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """从原始数据中提取特征"""
        features = []

        for _, row in df.iterrows():
            feature_row = {
                # 原始特征
                'S1': row['S1'], 'A1': row['A1'],
                'S2': row['S2'], 'A2': row['A2'],
                'S3': row['S3'], 'A3': row['A3'],
                'Depth': row['Depth'],

                # 统计特征
                'Speed_Mean': np.mean([row['S1'], row['S2'], row['S3']]),
                'Speed_Std': np.std([row['S1'], row['S2'], row['S3']]),
                'Amp_Mean': np.mean([row['A1'], row['A2'], row['A3']]),
                'Amp_Std': np.std([row['A1'], row['A2'], row['A3']]),

                # 差异特征
                'Speed_Range': max(row['S1'], row['S2'], row['S3']) - min(row['S1'], row['S2'], row['S3']),
                'Amp_Range': max(row['A1'], row['A2'], row['A3']) - min(row['A1'], row['A2'], row['A3']),
            }
            features.append(feature_row)

        return pd.DataFrame(features)

    def _learn_class_distribution(self, pile_class: str, features: pd.DataFrame):
        """学习特定类别的特征分布"""
        # 使用高斯混合模型学习分布
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)

        # 选择最佳组件数
        n_components = min(3, len(features) // 10 + 1)
        gmm = GaussianMixture(n_components=n_components, random_state=42)
        gmm.fit(scaled_features)

        self.learned_distributions[pile_class] = gmm
        self.feature_scalers[pile_class] = scaler
        self.data_stats[pile_class] = {
            'feature_names': features.columns.tolist(),
            'original_stats': features.describe().to_dict()
        }

    def generate_intelligent_data(self, pile_class: str, num_samples: int,
                                progress_callback=None) -> pd.DataFrame:
        """生成智能合成数据"""
        if pile_class not in self.learned_distributions:
            raise ValueError(f"未学习到 {pile_class} 的分布信息")

        gmm = self.learned_distributions[pile_class]
        scaler = self.feature_scalers[pile_class]
        feature_names = self.data_stats[pile_class]['feature_names']

        generated_data = []

        for i in range(num_samples):
            # 从学习到的分布中采样
            sample_scaled, _ = gmm.sample(1)
            sample = scaler.inverse_transform(sample_scaled)[0]

            # 创建特征字典
            feature_dict = dict(zip(feature_names, sample))

            # 应用GZ方法约束
            optimized_features = self._apply_gz_constraints(feature_dict, pile_class)

            # 生成深度序列数据
            depth_data = self._generate_depth_sequence(optimized_features, pile_class)
            generated_data.extend(depth_data)

            if progress_callback and i % 10 == 0:
                progress = 50 + (i / num_samples) * 50  # 生成阶段占50%
                progress_callback(progress, f"生成 {pile_class} 样本 {i+1}/{num_samples}")

        return pd.DataFrame(generated_data)

    def _apply_gz_constraints(self, features: Dict, pile_class: str) -> Dict:
        """应用GZ方法约束优化特征"""
        # 根据桩类调整参数范围
        gz_ranges = {
            'I类桩': {'sp_range': (95, 110), 'ad_range': (-2, 2)},
            'II类桩': {'sp_range': (85, 100), 'ad_range': (0, 6)},
            'III类桩': {'sp_range': (70, 85), 'ad_range': (4, 10)},
            'IV类桩': {'sp_range': (50, 75), 'ad_range': (8, 15)}
        }

        if pile_class in gz_ranges:
            ranges = gz_ranges[pile_class]

            # 调整速度特征
            for speed_key in ['S1', 'S2', 'S3']:
                if speed_key in features:
                    sp_min, sp_max = ranges['sp_range']
                    features[speed_key] = np.clip(features[speed_key], sp_min, sp_max)

            # 调整波幅特征
            for amp_key in ['A1', 'A2', 'A3']:
                if amp_key in features:
                    ad_min, ad_max = ranges['ad_range']
                    features[amp_key] = np.clip(features[amp_key], ad_min, ad_max)

        return features

    def _generate_depth_sequence(self, base_features: Dict, pile_class: str) -> List[Dict]:
        """基于基础特征生成深度序列数据"""
        num_depths = np.random.randint(80, 150)
        depths = np.linspace(10, 30, num_depths)

        sequence_data = []

        for depth in depths:
            # 添加深度相关的变化
            depth_factor = 1 + 0.1 * np.sin(depth * 0.5) * np.random.normal(0, 0.1)

            row = {
                'Depth': depth,
                'S1': base_features.get('S1', 100) * depth_factor + np.random.normal(0, 2),
                'A1': base_features.get('A1', 0) + np.random.normal(0, 0.5),
                'S2': base_features.get('S2', 100) * depth_factor + np.random.normal(0, 2),
                'A2': base_features.get('A2', 0) + np.random.normal(0, 0.5),
                'S3': base_features.get('S3', 100) * depth_factor + np.random.normal(0, 2),
                'A3': base_features.get('A3', 0) + np.random.normal(0, 0.5),
            }

            # 确保参数在合理范围内
            row['S1'] = np.clip(row['S1'], 30, 150)
            row['S2'] = np.clip(row['S2'], 30, 150)
            row['S3'] = np.clip(row['S3'], 30, 150)
            row['A1'] = np.clip(row['A1'], -5, 25)
            row['A2'] = np.clip(row['A2'], -5, 25)
            row['A3'] = np.clip(row['A3'], -5, 25)

            sequence_data.append(row)

        return sequence_data


class GZMethodCalculator:
    """GZ方法计算器"""

    def calculate_I_ji(self, Sp: float, Ad: float, Bi_ratio: float = 1.0) -> int:
        """计算I(j,i)值"""
        # I(j,i) = 1 (完整性最好)
        if Bi_ratio > 0.8:
            if (Sp >= 100 and Ad <= 0) or \
               (85 <= Sp < 100 and Ad <= 0) or \
               (Sp >= 100 and 0 < Ad <= 4):
                return 1

        # I(j,i) = 2 (轻微缺陷)
        if (0.5 < Bi_ratio <= 0.8 and 85 <= Sp < 100 and 0 < Ad <= 4) or \
           (Bi_ratio > 0.5 and 75 <= Sp < 85 and Ad <= 4) or \
           (Bi_ratio > 0.5 and Sp >= 85 and 4 < Ad <= 8):
            return 2

        # I(j,i) = 3 (明显缺陷)
        if (0.25 < Bi_ratio <= 0.5 and 75 <= Sp < 85 and 4 < Ad <= 8) or \
           (Bi_ratio > 0.25 and 65 <= Sp < 75 and Ad <= 8) or \
           (Bi_ratio > 0.25 and Sp >= 75 and 8 < Ad <= 12):
            return 3

        # I(j,i) = 4 (严重缺陷)
        return 4

    def verify_generated_data(self, df: pd.DataFrame) -> str:
        """验证生成的数据符合哪个桩类"""
        K_values = []

        for _, row in df.iterrows():
            I_ji_values = []

            for profile_idx in range(3):
                sp = row[f'S{profile_idx+1}']
                ad = row[f'A{profile_idx+1}']

                I_ji = self.calculate_I_ji(sp, ad)
                I_ji_values.append(I_ji)

            # 计算K(i)值
            if I_ji_values:
                sum_I_ji_sq = sum(i**2 for i in I_ji_values)
                sum_I_ji = sum(I_ji_values)
                K_i = int((sum_I_ji_sq / sum_I_ji) + 0.5) if sum_I_ji > 0 else 0
                K_values.append(K_i)

        # 根据K值分布确定桩类
        has_K4 = any(k == 4 for k in K_values)
        has_K3 = any(k == 3 for k in K_values)
        has_K2 = any(k == 2 for k in K_values)

        if has_K4:
            return "IV类桩"
        elif has_K3:
            return "III类桩"
        elif has_K2:
            return "II类桩"
        elif all(k == 1 for k in K_values):
            return "I类桩"
        else:
            return "未定类别"


class IntelligentDataGeneratorGUI:
    """智能合成数据生成器GUI界面"""

    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.generator = IntelligentSyntheticDataGenerator()
        self.data_directory = ""
        self.learning_results = {}

        self.setup_gui()
        self.setup_styles()

    def setup_gui(self):
        """设置GUI界面"""
        self.root.title("🧠 智能桩身完整性数据生成器 v2.0")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # 设置图标和样式
        try:
            self.root.iconbitmap('icon.ico')  # 如果有图标文件
        except:
            pass

        # 创建主框架
        self.create_main_frame()
        self.create_control_panel()
        self.create_progress_panel()
        self.create_results_panel()
        self.create_visualization_panel()

    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 自定义样式
        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2c3e50')
        self.style.configure('Subtitle.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        self.style.configure('Info.TLabel', font=('Arial', 10), foreground='#7f8c8d')
        self.style.configure('Success.TLabel', font=('Arial', 10), foreground='#27ae60')
        self.style.configure('Error.TLabel', font=('Arial', 10), foreground='#e74c3c')

        # 按钮样式
        self.style.configure('Primary.TButton', font=('Arial', 10, 'bold'))
        self.style.configure('Success.TButton', font=('Arial', 10, 'bold'))
        self.style.configure('Warning.TButton', font=('Arial', 10, 'bold'))

    def create_main_frame(self):
        """创建主框架"""
        # 标题框架
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🧠 智能桩身完整性数据生成器",
                              font=('Arial', 20, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        subtitle_label = tk.Label(title_frame, text="基于机器学习和GZ传统方法的智能数据生成",
                                 font=('Arial', 12), fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack()

        # 主内容框架
        self.main_frame = tk.Frame(self.root, bg='#f0f0f0')
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)

    def create_control_panel(self):
        """创建控制面板"""
        # 控制面板框架
        control_frame = tk.LabelFrame(self.main_frame, text="📁 数据源配置",
                                     font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        control_frame.pack(fill='x', pady=(0, 10))

        # 数据目录选择
        dir_frame = tk.Frame(control_frame, bg='#f0f0f0')
        dir_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(dir_frame, text="训练数据目录:", font=('Arial', 10, 'bold'),
                bg='#f0f0f0', fg='#2c3e50').pack(side='left')

        self.dir_var = tk.StringVar()
        self.dir_entry = tk.Entry(dir_frame, textvariable=self.dir_var, font=('Arial', 10),
                                 width=60, state='readonly')
        self.dir_entry.pack(side='left', padx=(10, 5), fill='x', expand=True)

        self.browse_btn = tk.Button(dir_frame, text="📂 浏览", font=('Arial', 10, 'bold'),
                                   bg='#3498db', fg='white', command=self.browse_directory,
                                   relief='flat', padx=15)
        self.browse_btn.pack(side='right', padx=(5, 0))

        # 学习按钮
        learn_frame = tk.Frame(control_frame, bg='#f0f0f0')
        learn_frame.pack(fill='x', padx=10, pady=(0, 10))

        self.learn_btn = tk.Button(learn_frame, text="🧠 开始学习数据分布",
                                  font=('Arial', 12, 'bold'), bg='#27ae60', fg='white',
                                  command=self.start_learning, relief='flat', padx=20, pady=8)
        self.learn_btn.pack(side='left')

        self.learn_status = tk.Label(learn_frame, text="请先选择数据目录",
                                    font=('Arial', 10), bg='#f0f0f0', fg='#7f8c8d')
        self.learn_status.pack(side='left', padx=(20, 0))

    def create_progress_panel(self):
        """创建进度面板"""
        progress_frame = tk.LabelFrame(self.main_frame, text="📊 处理进度",
                                      font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        progress_frame.pack(fill='x', pady=(0, 10))

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.pack(padx=10, pady=10)

        # 进度文本
        self.progress_text = tk.Label(progress_frame, text="等待开始...",
                                     font=('Arial', 10), bg='#f0f0f0', fg='#7f8c8d')
        self.progress_text.pack(pady=(0, 10))

    def create_results_panel(self):
        """创建结果面板"""
        results_frame = tk.LabelFrame(self.main_frame, text="📈 学习结果与数据生成",
                                     font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        results_frame.pack(fill='x', pady=(0, 10))

        # 学习结果显示
        results_info_frame = tk.Frame(results_frame, bg='#f0f0f0')
        results_info_frame.pack(fill='x', padx=10, pady=10)

        self.results_text = tk.Text(results_info_frame, height=6, width=80,
                                   font=('Consolas', 9), bg='#ffffff', fg='#2c3e50',
                                   relief='sunken', bd=1)
        self.results_text.pack(side='left', fill='both', expand=True)

        # 滚动条
        scrollbar = tk.Scrollbar(results_info_frame, orient='vertical', command=self.results_text.yview)
        scrollbar.pack(side='right', fill='y')
        self.results_text.config(yscrollcommand=scrollbar.set)

        # 数据生成控制
        generate_frame = tk.Frame(results_frame, bg='#f0f0f0')
        generate_frame.pack(fill='x', padx=10, pady=(0, 10))

        # 桩类选择
        tk.Label(generate_frame, text="桩类:", font=('Arial', 10, 'bold'),
                bg='#f0f0f0', fg='#2c3e50').pack(side='left')

        self.pile_class_var = tk.StringVar(value="I类桩")
        pile_class_combo = ttk.Combobox(generate_frame, textvariable=self.pile_class_var,
                                       values=["I类桩", "II类桩", "III类桩", "IV类桩"],
                                       state='readonly', width=10)
        pile_class_combo.pack(side='left', padx=(5, 20))

        # 样本数量
        tk.Label(generate_frame, text="样本数量:", font=('Arial', 10, 'bold'),
                bg='#f0f0f0', fg='#2c3e50').pack(side='left')

        self.sample_count_var = tk.StringVar(value="50")
        sample_count_entry = tk.Entry(generate_frame, textvariable=self.sample_count_var,
                                     width=10, font=('Arial', 10))
        sample_count_entry.pack(side='left', padx=(5, 20))

        # 生成按钮
        self.generate_btn = tk.Button(generate_frame, text="🚀 生成智能数据",
                                     font=('Arial', 11, 'bold'), bg='#e74c3c', fg='white',
                                     command=self.start_generation, relief='flat', padx=15, pady=5,
                                     state='disabled')
        self.generate_btn.pack(side='left', padx=(10, 0))

        # 生成所有类别按钮
        self.generate_all_btn = tk.Button(generate_frame, text="🎯 生成全部类别",
                                         font=('Arial', 11, 'bold'), bg='#9b59b6', fg='white',
                                         command=self.start_generation_all, relief='flat', padx=15, pady=5,
                                         state='disabled')
        self.generate_all_btn.pack(side='right')

    def create_visualization_panel(self):
        """创建可视化面板"""
        viz_frame = tk.LabelFrame(self.main_frame, text="📊 数据分布可视化",
                                 font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        viz_frame.pack(fill='both', expand=True)

        # 创建matplotlib图形
        self.fig, self.axes = plt.subplots(2, 2, figsize=(10, 6))
        self.fig.patch.set_facecolor('#f0f0f0')

        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, viz_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)

        # 初始化空图
        self.clear_plots()

    def clear_plots(self):
        """清空图表"""
        for ax in self.axes.flat:
            ax.clear()
            ax.set_title("等待数据...")
            ax.grid(True, alpha=0.3)
        self.canvas.draw()

    def browse_directory(self):
        """浏览目录"""
        directory = filedialog.askdirectory(title="选择包含I、II、III、IV类桩数据的目录")
        if directory:
            self.data_directory = directory
            self.dir_var.set(directory)
            self.learn_status.config(text="目录已选择，点击开始学习", fg='#27ae60')

            # 检查目录结构
            self.check_directory_structure()

    def check_directory_structure(self):
        """检查目录结构"""
        required_dirs = ['I', 'II', 'III', 'IV']
        missing_dirs = []

        for dir_name in required_dirs:
            dir_path = os.path.join(self.data_directory, dir_name)
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_name)

        if missing_dirs:
            self.learn_status.config(text=f"缺少目录: {', '.join(missing_dirs)}", fg='#e74c3c')
        else:
            self.learn_status.config(text="目录结构正确，可以开始学习", fg='#27ae60')

    def start_learning(self):
        """开始学习数据分布"""
        if not self.data_directory:
            messagebox.showerror("错误", "请先选择数据目录")
            return

        # 禁用按钮
        self.learn_btn.config(state='disabled')
        self.browse_btn.config(state='disabled')

        # 重置进度
        self.progress_var.set(0)
        self.progress_text.config(text="开始学习数据分布...")

        # 在新线程中执行学习
        thread = threading.Thread(target=self.learning_worker)
        thread.daemon = True
        thread.start()

    def learning_worker(self):
        """学习工作线程"""
        try:
            # 学习数据分布
            self.learning_results = self.generator.learn_from_existing_data(
                self.data_directory, self.update_progress)

            # 更新UI
            self.root.after(0, self.learning_completed)

        except Exception as e:
            self.root.after(0, lambda: self.learning_error(str(e)))

    def update_progress(self, progress: float, message: str):
        """更新进度"""
        self.root.after(0, lambda: self._update_progress_ui(progress, message))

    def _update_progress_ui(self, progress: float, message: str):
        """更新进度UI"""
        self.progress_var.set(progress)
        self.progress_text.config(text=message)
        self.root.update_idletasks()

    def learning_completed(self):
        """学习完成"""
        # 恢复按钮
        self.learn_btn.config(state='normal')
        self.browse_btn.config(state='normal')
        self.generate_btn.config(state='normal')
        self.generate_all_btn.config(state='normal')

        # 更新状态
        self.progress_var.set(100)
        self.progress_text.config(text="学习完成！可以开始生成数据", fg='#27ae60')

        # 显示学习结果
        self.display_learning_results()

        # 更新可视化
        self.update_visualization()

    def learning_error(self, error_message: str):
        """学习出错"""
        # 恢复按钮
        self.learn_btn.config(state='normal')
        self.browse_btn.config(state='normal')

        # 显示错误
        self.progress_text.config(text=f"学习失败: {error_message}", fg='#e74c3c')
        messagebox.showerror("学习失败", f"学习过程中出现错误:\n{error_message}")

    def display_learning_results(self):
        """显示学习结果"""
        self.results_text.delete(1.0, tk.END)

        if self.learning_results.get('learning_status') == 'success':
            results_text = f"🎉 学习完成！\n"
            results_text += f"📊 总样本数: {self.learning_results['total_samples']}\n\n"

            for pile_class, info in self.learning_results['class_distributions'].items():
                results_text += f"📋 {pile_class}:\n"
                results_text += f"   样本文件数: {info['sample_count']}\n"
                results_text += f"   数据点数: {info['total_points']}\n"
                results_text += f"   平均波速: {info['feature_means']['Speed_Mean']:.2f}%\n"
                results_text += f"   平均波幅: {info['feature_means']['Amp_Mean']:.2f}dB\n\n"
        else:
            results_text = f"❌ 学习失败: {self.learning_results.get('learning_status', '未知错误')}"

        self.results_text.insert(tk.END, results_text)

    def update_visualization(self):
        """更新可视化图表"""
        if not self.learning_results or self.learning_results.get('learning_status') != 'success':
            return

        # 清空图表
        for ax in self.axes.flat:
            ax.clear()

        # 准备数据
        pile_classes = list(self.learning_results['class_distributions'].keys())
        colors = ['#3498db', '#27ae60', '#f39c12', '#e74c3c']

        # 图1: 样本数量分布
        sample_counts = [self.learning_results['class_distributions'][pc]['sample_count']
                        for pc in pile_classes]
        self.axes[0, 0].bar(pile_classes, sample_counts, color=colors)
        self.axes[0, 0].set_title('各类桩样本数量分布', fontsize=12, fontweight='bold')
        self.axes[0, 0].set_ylabel('样本数量')

        # 图2: 数据点数量分布
        point_counts = [self.learning_results['class_distributions'][pc]['total_points']
                       for pc in pile_classes]
        self.axes[0, 1].bar(pile_classes, point_counts, color=colors)
        self.axes[0, 1].set_title('各类桩数据点数量分布', fontsize=12, fontweight='bold')
        self.axes[0, 1].set_ylabel('数据点数量')

        # 图3: 平均波速分布
        speed_means = [self.learning_results['class_distributions'][pc]['feature_means']['Speed_Mean']
                      for pc in pile_classes]
        self.axes[1, 0].bar(pile_classes, speed_means, color=colors)
        self.axes[1, 0].set_title('各类桩平均波速分布', fontsize=12, fontweight='bold')
        self.axes[1, 0].set_ylabel('平均波速 (%)')

        # 图4: 平均波幅分布
        amp_means = [self.learning_results['class_distributions'][pc]['feature_means']['Amp_Mean']
                    for pc in pile_classes]
        self.axes[1, 1].bar(pile_classes, amp_means, color=colors)
        self.axes[1, 1].set_title('各类桩平均波幅分布', fontsize=12, fontweight='bold')
        self.axes[1, 1].set_ylabel('平均波幅 (dB)')

        # 设置网格和样式
        for ax in self.axes.flat:
            ax.grid(True, alpha=0.3)
            ax.tick_params(axis='x', rotation=45)

        self.fig.tight_layout()
        self.canvas.draw()

    def start_generation(self):
        """开始生成单个类别数据"""
        try:
            pile_class = self.pile_class_var.get()
            sample_count = int(self.sample_count_var.get())

            if sample_count <= 0:
                messagebox.showerror("错误", "样本数量必须大于0")
                return

            # 禁用按钮
            self.generate_btn.config(state='disabled')
            self.generate_all_btn.config(state='disabled')

            # 重置进度
            self.progress_var.set(0)
            self.progress_text.config(text=f"开始生成 {pile_class} 数据...")

            # 在新线程中执行生成
            thread = threading.Thread(target=self.generation_worker,
                                     args=(pile_class, sample_count))
            thread.daemon = True
            thread.start()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的样本数量")

    def start_generation_all(self):
        """开始生成所有类别数据"""
        try:
            sample_count = int(self.sample_count_var.get())

            if sample_count <= 0:
                messagebox.showerror("错误", "样本数量必须大于0")
                return

            # 禁用按钮
            self.generate_btn.config(state='disabled')
            self.generate_all_btn.config(state='disabled')

            # 重置进度
            self.progress_var.set(0)
            self.progress_text.config(text="开始生成所有类别数据...")

            # 在新线程中执行生成
            thread = threading.Thread(target=self.generation_all_worker, args=(sample_count,))
            thread.daemon = True
            thread.start()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的样本数量")

    def generation_worker(self, pile_class: str, sample_count: int):
        """单个类别生成工作线程"""
        try:
            # 生成数据
            generated_data = self.generator.generate_intelligent_data(
                pile_class, sample_count, self.update_progress)

            # 保存数据
            self.save_generated_data(pile_class, generated_data, sample_count)

            # 更新UI
            self.root.after(0, lambda: self.generation_completed(pile_class, sample_count))

        except Exception as e:
            self.root.after(0, lambda: self.generation_error(str(e)))

    def generation_all_worker(self, sample_count: int):
        """所有类别生成工作线程"""
        try:
            pile_classes = ["I类桩", "II类桩", "III类桩", "IV类桩"]
            total_progress = 0

            for i, pile_class in enumerate(pile_classes):
                if pile_class in self.generator.learned_distributions:
                    # 生成数据
                    generated_data = self.generator.generate_intelligent_data(
                        pile_class, sample_count,
                        lambda p, m: self.update_progress(total_progress + p/4, f"{m} ({i+1}/4)"))

                    # 保存数据
                    self.save_generated_data(pile_class, generated_data, sample_count)

                total_progress += 25

            # 更新UI
            self.root.after(0, lambda: self.generation_all_completed(sample_count))

        except Exception as e:
            self.root.after(0, lambda: self.generation_error(str(e)))

    def save_generated_data(self, pile_class: str, data: pd.DataFrame, sample_count: int):
        """保存生成的数据"""
        # 确定保存目录
        class_short = pile_class.replace('类桩', '')
        save_dir = os.path.join(self.data_directory, class_short)
        os.makedirs(save_dir, exist_ok=True)

        # 按样本分组保存
        samples_per_file = len(data) // sample_count

        for i in range(sample_count):
            start_idx = i * samples_per_file
            end_idx = (i + 1) * samples_per_file if i < sample_count - 1 else len(data)

            sample_data = data.iloc[start_idx:end_idx]

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"intelligent_{pile_class}_{timestamp}_{i+1:03d}.txt"
            filepath = os.path.join(save_dir, filename)

            # 保存文件
            output_data = sample_data[['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']].copy()
            output_data.columns = ['Depth(m)', '1-2 Speed%', '1-2 Amp%', '1-3 Speed%', '1-3 Amp%', '2-3 Speed%', '2-3 Amp%']
            output_data.to_csv(filepath, sep='\t', index=False, float_format='%.2f')

    def generation_completed(self, pile_class: str, sample_count: int):
        """单个类别生成完成"""
        # 恢复按钮
        self.generate_btn.config(state='normal')
        self.generate_all_btn.config(state='normal')

        # 更新状态
        self.progress_var.set(100)
        self.progress_text.config(text=f"✅ {pile_class} 数据生成完成！({sample_count}个样本)", fg='#27ae60')

        # 显示成功消息
        messagebox.showinfo("生成完成", f"成功生成 {sample_count} 个 {pile_class} 样本！\n数据已保存到原始目录中。")

    def generation_all_completed(self, sample_count: int):
        """所有类别生成完成"""
        # 恢复按钮
        self.generate_btn.config(state='normal')
        self.generate_all_btn.config(state='normal')

        # 更新状态
        self.progress_var.set(100)
        self.progress_text.config(text=f"✅ 所有类别数据生成完成！(每类{sample_count}个样本)", fg='#27ae60')

        # 显示成功消息
        messagebox.showinfo("生成完成", f"成功生成所有类别数据！\n每类 {sample_count} 个样本，数据已保存到原始目录中。")

    def generation_error(self, error_message: str):
        """生成出错"""
        # 恢复按钮
        self.generate_btn.config(state='normal')
        self.generate_all_btn.config(state='normal')

        # 显示错误
        self.progress_text.config(text=f"生成失败: {error_message}", fg='#e74c3c')
        messagebox.showerror("生成失败", f"数据生成过程中出现错误:\n{error_message}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = IntelligentDataGeneratorGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
