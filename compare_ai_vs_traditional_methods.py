#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对比94%准确率AI模式与传统方法的桩基完整性判定准确率
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime

def load_training_data():
    """加载training_data目录中的所有数据"""
    print("📂 加载training_data目录中的数据")
    print("=" * 80)

    training_data_dir = Path("training_data")
    if not training_data_dir.exists():
        print(f"❌ 训练数据目录不存在: {training_data_dir}")
        return None, None

    all_data = []
    all_labels = []
    file_info = []

    # 类别映射
    class_mapping = {'I': 0, 'II': 1, 'III': 2, 'IV': 3}
    class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}

    for class_name in ['I', 'II', 'III', 'IV']:
        class_dir = training_data_dir / class_name
        if not class_dir.exists():
            print(f"⚠️ 类别目录不存在: {class_dir}")
            continue

        class_files = list(class_dir.glob("*.txt"))
        print(f"📁 {class_name}类桩: 找到 {len(class_files)} 个文件")

        for file_path in class_files:
            try:
                # 读取数据文件
                df = pd.read_csv(file_path, sep='\t', encoding='utf-8')

                # 检查数据格式
                if df.shape[1] < 7:
                    print(f"⚠️ 文件格式不正确: {file_path.name} (列数: {df.shape[1]})")
                    continue

                # 标准化列名
                expected_columns = ['Depth(m)', '1-2 Speed%', '1-2 Amp%', '1-3 Speed%', '1-3 Amp%', '2-3 Speed%', '2-3 Amp%']
                if len(df.columns) >= 7:
                    df.columns = expected_columns[:len(df.columns)]

                all_data.append(df)
                all_labels.append(class_mapping[class_name])
                file_info.append({
                    'file': file_path.name,
                    'class': class_name,
                    'class_name': class_names[class_mapping[class_name]],
                    'rows': len(df)
                })

            except Exception as e:
                print(f"❌ 读取文件失败: {file_path.name} - {e}")

    print(f"\n📊 数据加载统计:")
    for class_name in ['I', 'II', 'III', 'IV']:
        count = sum(1 for info in file_info if info['class'] == class_name)
        print(f"  {class_names[class_mapping[class_name]]}: {count} 个文件")

    print(f"总计: {len(all_data)} 个数据文件")

    return all_data, all_labels, file_info

def test_ai_v2_system(data_list, true_labels, file_info):
    """测试AI System V2.0 (94%准确率模式)"""
    print("\n🤖 测试AI System V2.0 (94%准确率模式)")
    print("=" * 80)

    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2

        # 获取AI分析器V2
        analyzer_v2 = get_ai_analyzer_v2()

        # 设置为高精度模型
        models = analyzer_v2.model_manager.get_available_models()
        optimized_model_key = None

        for key, model_info in models.items():
            if model_info.model_type == 'optimized' and model_info.accuracy > 0.9:
                optimized_model_key = key
                print(f"✅ 找到高精度模型: {model_info.name} (准确率: {model_info.accuracy:.1%})")
                break

        if optimized_model_key:
            analyzer_v2.set_model(optimized_model_key)
            analyzer_v2.set_feature_extractor('advanced')  # 使用高精度特征提取器
        else:
            print("⚠️ 未找到94%准确率模型，使用默认模型")

        # 进行预测
        ai_predictions = []
        ai_confidences = []
        prediction_times = []

        print(f"\n🔍 开始AI预测 ({len(data_list)} 个样本):")

        for i, (df, true_label, info) in enumerate(zip(data_list, true_labels, file_info)):
            try:
                start_time = datetime.now()
                result = analyzer_v2.predict(df)
                end_time = datetime.now()

                if result:
                    predicted_class = result.get('完整性类别', -1)
                    confidence = result.get('ai_confidence', 0.0)

                    ai_predictions.append(predicted_class)
                    ai_confidences.append(confidence)
                    prediction_times.append((end_time - start_time).total_seconds())

                    # 转换为中文类别名称
                    class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                    true_name = class_names.get(true_label, f'未知({true_label})')
                    pred_name = class_names.get(predicted_class, f'未知({predicted_class})')

                    status = "✅" if predicted_class == true_label else "❌"
                    print(f"  {i+1:2d}. {info['file']:<15} | 真实: {true_name} | 预测: {pred_name} | 置信度: {confidence:.2%} {status}")
                else:
                    print(f"  {i+1:2d}. {info['file']:<15} | ❌ 预测失败")
                    ai_predictions.append(-1)
                    ai_confidences.append(0.0)
                    prediction_times.append(0.0)

            except Exception as e:
                print(f"  {i+1:2d}. {info['file']:<15} | ❌ 预测异常: {e}")
                ai_predictions.append(-1)
                ai_confidences.append(0.0)
                prediction_times.append(0.0)

        return ai_predictions, ai_confidences, prediction_times

    except Exception as e:
        print(f"❌ AI V2.0系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_traditional_gz_method(data_list, true_labels, file_info):
    """测试传统GZ方法"""
    print("\n🔧 测试传统GZ方法")
    print("=" * 80)

    try:
        # 使用简化的传统分析方法
        gz_predictions = []
        gz_scores = []
        analysis_times = []

        print(f"\n🔍 开始传统方法分析 ({len(data_list)} 个样本):")

        for i, (df, true_label, info) in enumerate(zip(data_list, true_labels, file_info)):
            try:
                start_time = datetime.now()

                # 简化的传统分析逻辑
                result = analyze_traditional_simple(df)

                end_time = datetime.now()

                if result and '完整性类别' in result:
                    predicted_class = result['完整性类别']
                    gz_score = result.get('GZ综合评分', 0.0)

                    gz_predictions.append(predicted_class)
                    gz_scores.append(gz_score)
                    analysis_times.append((end_time - start_time).total_seconds())

                    # 转换为中文类别名称
                    class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                    true_name = class_names.get(true_label, f'未知({true_label})')
                    pred_name = class_names.get(predicted_class, f'未知({predicted_class})')

                    status = "✅" if predicted_class == true_label else "❌"
                    print(f"  {i+1:2d}. {info['file']:<15} | 真实: {true_name} | 预测: {pred_name} | GZ评分: {gz_score:.2f} {status}")
                else:
                    print(f"  {i+1:2d}. {info['file']:<15} | ❌ 分析失败")
                    gz_predictions.append(-1)
                    gz_scores.append(0.0)
                    analysis_times.append(0.0)

            except Exception as e:
                print(f"  {i+1:2d}. {info['file']:<15} | ❌ 分析异常: {e}")
                gz_predictions.append(-1)
                gz_scores.append(0.0)
                analysis_times.append(0.0)

        return gz_predictions, gz_scores, analysis_times

    except Exception as e:
        print(f"❌ 传统GZ方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def analyze_traditional_simple(df):
    """简化的传统分析方法"""
    try:
        # 标准化列名
        if 'Depth(m)' in df.columns:
            df = df.rename(columns={
                'Depth(m)': 'Depth',
                '1-2 Speed%': 'S1',
                '1-2 Amp%': 'A1',
                '1-3 Speed%': 'S2',
                '1-3 Amp%': 'A2',
                '2-3 Speed%': 'S3',
                '2-3 Amp%': 'A3'
            })

        # 传统分析配置
        config = {
            '正常': {'speed': (90.0, 1000.0), 'amp': (-100, 3)},
            '轻微畸变': {'speed': (80.0, 90.0), 'amp': (3, 6)},
            '明显畸变': {'speed': (70.0, 80.0), 'amp': (6, 12)},
            '严重畸变': {'speed': (0.0, 70.0), 'amp': (12, 100)}
        }

        # 计算异常比例
        abnormal_counts = {'轻微畸变': 0, '明显畸变': 0, '严重畸变': 0}
        total_points = 0

        for _, row in df.iterrows():
            for i in range(1, 4):
                s_col, a_col = f'S{i}', f'A{i}'
                if s_col in row and a_col in row:
                    speed = row[s_col]
                    amp = row[a_col]

                    if pd.notna(speed) and pd.notna(amp):
                        total_points += 1

                        # 分类异常
                        anomaly_type = classify_anomaly_simple(speed, amp, config)
                        if anomaly_type in abnormal_counts:
                            abnormal_counts[anomaly_type] += 1

        if total_points == 0:
            return {'完整性类别': -1, 'GZ综合评分': 0.0}

        # 计算异常比例
        severe_ratio = abnormal_counts['严重畸变'] / total_points
        obvious_ratio = abnormal_counts['明显畸变'] / total_points
        slight_ratio = abnormal_counts['轻微畸变'] / total_points

        # 根据传统GZ方法判定完整性类别
        if severe_ratio > 0.3 or obvious_ratio > 0.5:
            integrity_class = 3  # IV类桩
        elif severe_ratio > 0.1 or obvious_ratio > 0.3:
            integrity_class = 2  # III类桩
        elif slight_ratio > 0.3 or obvious_ratio > 0.1:
            integrity_class = 1  # II类桩
        else:
            integrity_class = 0  # I类桩

        # 计算GZ综合评分
        gz_score = 100 - (severe_ratio * 60 + obvious_ratio * 30 + slight_ratio * 10)

        return {
            '完整性类别': integrity_class,
            'GZ综合评分': gz_score,
            '异常比例': {
                '严重畸变': severe_ratio,
                '明显畸变': obvious_ratio,
                '轻微畸变': slight_ratio
            }
        }

    except Exception as e:
        print(f"传统分析异常: {e}")
        return {'完整性类别': -1, 'GZ综合评分': 0.0}

def classify_anomaly_simple(speed, amp, config):
    """简化的异常分类"""
    s_map = {'正常': 0, '轻微畸变': 1, '明显畸变': 2, '严重畸变': 3}
    o_types = ['严重畸变', '明显畸变', '轻微畸变', '正常']
    s_cat, a_cat = '正常', '正常'

    for lvl in o_types:
        if lvl in config and 'speed' in config[lvl]:
            s_min, s_max = config[lvl]['speed']
            if s_min <= speed < s_max:
                s_cat = lvl
                break

    for lvl in o_types:
        if lvl in config and 'amp' in config[lvl]:
            a_min, a_max = config[lvl]['amp']
            if a_min <= amp < a_max:
                a_cat = lvl
                break

    return s_cat if s_map.get(s_cat, -1) > s_map.get(a_cat, -1) else a_cat

def calculate_accuracy_metrics(true_labels, predictions, method_name):
    """计算准确率指标"""
    print(f"\n📊 {method_name} 准确率分析")
    print("=" * 60)

    # 过滤有效预测
    valid_indices = [i for i, pred in enumerate(predictions) if pred != -1]
    valid_true = [true_labels[i] for i in valid_indices]
    valid_pred = [predictions[i] for i in valid_indices]

    if not valid_true:
        print("❌ 没有有效的预测结果")
        return {}

    # 总体准确率
    correct = sum(1 for t, p in zip(valid_true, valid_pred) if t == p)
    total = len(valid_true)
    overall_accuracy = correct / total if total > 0 else 0

    print(f"总体准确率: {overall_accuracy:.2%} ({correct}/{total})")

    # 各类别准确率
    class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
    class_accuracies = {}

    print(f"\n各类别准确率:")
    for class_id in range(4):
        class_true = [i for i, t in enumerate(valid_true) if t == class_id]
        class_correct = [i for i in class_true if valid_pred[i] == class_id]

        if class_true:
            class_acc = len(class_correct) / len(class_true)
            class_accuracies[class_id] = class_acc
            print(f"  {class_names[class_id]}: {class_acc:.2%} ({len(class_correct)}/{len(class_true)})")
        else:
            class_accuracies[class_id] = 0.0
            print(f"  {class_names[class_id]}: 无样本")

    # 混淆矩阵
    print(f"\n混淆矩阵 (行:真实, 列:预测):")
    print("     ", end="")
    for i in range(4):
        print(f"{class_names[i]:<8}", end="")
    print()

    for true_class in range(4):
        print(f"{class_names[true_class]:<5}", end="")
        for pred_class in range(4):
            count = sum(1 for t, p in zip(valid_true, valid_pred)
                       if t == true_class and p == pred_class)
            print(f"{count:<8}", end="")
        print()

    return {
        'overall_accuracy': overall_accuracy,
        'class_accuracies': class_accuracies,
        'total_samples': total,
        'correct_predictions': correct,
        'valid_predictions': len(valid_indices),
        'total_files': len(predictions)
    }

def generate_comparison_report(ai_metrics, gz_metrics, ai_times, gz_times, ai_confidences, gz_scores):
    """生成对比报告"""
    print(f"\n📋 AI模式 vs 传统方法 对比报告")
    print("=" * 80)

    # 准确率对比
    print(f"📊 准确率对比:")
    print(f"  AI System V2.0 (94%模式): {ai_metrics['overall_accuracy']:.2%}")
    print(f"  传统GZ方法:              {gz_metrics['overall_accuracy']:.2%}")
    print(f"  准确率提升:              {(ai_metrics['overall_accuracy'] - gz_metrics['overall_accuracy']):.2%}")

    # 各类别准确率对比
    print(f"\n📈 各类别准确率对比:")
    class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
    for class_id in range(4):
        ai_acc = ai_metrics['class_accuracies'][class_id]
        gz_acc = gz_metrics['class_accuracies'][class_id]
        improvement = ai_acc - gz_acc
        print(f"  {class_names[class_id]}: AI {ai_acc:.2%} vs GZ {gz_acc:.2%} (提升: {improvement:+.2%})")

    # 性能对比
    if ai_times and gz_times:
        avg_ai_time = np.mean([t for t in ai_times if t > 0])
        avg_gz_time = np.mean([t for t in gz_times if t > 0])
        print(f"\n⏱️ 性能对比:")
        print(f"  AI平均预测时间: {avg_ai_time:.3f}秒")
        print(f"  GZ平均分析时间: {avg_gz_time:.3f}秒")
        print(f"  速度比较: AI比GZ {'快' if avg_ai_time < avg_gz_time else '慢'} {abs(avg_ai_time - avg_gz_time):.3f}秒")

    # 置信度分析
    if ai_confidences:
        valid_confidences = [c for c in ai_confidences if c > 0]
        if valid_confidences:
            avg_confidence = np.mean(valid_confidences)
            min_confidence = np.min(valid_confidences)
            max_confidence = np.max(valid_confidences)
            print(f"\n🎯 AI置信度分析:")
            print(f"  平均置信度: {avg_confidence:.2%}")
            print(f"  最低置信度: {min_confidence:.2%}")
            print(f"  最高置信度: {max_confidence:.2%}")

    # 保存详细报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'ai_metrics': ai_metrics,
        'gz_metrics': gz_metrics,
        'performance': {
            'ai_avg_time': np.mean([t for t in ai_times if t > 0]) if ai_times else 0,
            'gz_avg_time': np.mean([t for t in gz_times if t > 0]) if gz_times else 0
        },
        'confidence': {
            'avg_confidence': np.mean([c for c in ai_confidences if c > 0]) if ai_confidences else 0,
            'min_confidence': np.min([c for c in ai_confidences if c > 0]) if ai_confidences else 0,
            'max_confidence': np.max([c for c in ai_confidences if c > 0]) if ai_confidences else 0
        }
    }

    with open('ai_vs_traditional_comparison_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"\n💾 详细报告已保存: ai_vs_traditional_comparison_report.json")

def main():
    """主函数"""
    print("🔬 AI模式 vs 传统方法 桩基完整性判定准确率对比测试")
    print("=" * 100)

    # 1. 加载训练数据
    data_list, true_labels, file_info = load_training_data()

    if not data_list:
        print("❌ 无法加载训练数据，测试终止")
        return

    # 2. 测试AI System V2.0 (94%准确率模式)
    ai_predictions, ai_confidences, ai_times = test_ai_v2_system(data_list, true_labels, file_info)

    # 3. 测试传统GZ方法
    gz_predictions, gz_scores, gz_times = test_traditional_gz_method(data_list, true_labels, file_info)

    if ai_predictions is None or gz_predictions is None:
        print("❌ 测试失败，无法进行对比分析")
        return

    # 4. 计算准确率指标
    ai_metrics = calculate_accuracy_metrics(true_labels, ai_predictions, "AI System V2.0")
    gz_metrics = calculate_accuracy_metrics(true_labels, gz_predictions, "传统GZ方法")

    # 5. 生成对比报告
    generate_comparison_report(ai_metrics, gz_metrics, ai_times, gz_times, ai_confidences, gz_scores)

    # 6. 总结
    print(f"\n🎉 测试完成总结:")
    print(f"✅ 测试样本数: {len(data_list)} 个")
    print(f"✅ AI System V2.0 准确率: {ai_metrics['overall_accuracy']:.2%}")
    print(f"✅ 传统GZ方法 准确率: {gz_metrics['overall_accuracy']:.2%}")
    print(f"✅ AI模式优势: {(ai_metrics['overall_accuracy'] - gz_metrics['overall_accuracy']):.2%}")

    if ai_metrics['overall_accuracy'] > gz_metrics['overall_accuracy']:
        print(f"🚀 AI System V2.0 表现更优！")
    elif ai_metrics['overall_accuracy'] == gz_metrics['overall_accuracy']:
        print(f"⚖️ 两种方法表现相当")
    else:
        print(f"🔧 传统GZ方法表现更优")

if __name__ == "__main__":
    main()
