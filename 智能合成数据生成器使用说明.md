# 🧠 智能桩身完整性数据生成器 v2.0

## 📋 概述

智能桩身完整性数据生成器是一个基于机器学习和GZ传统方法的高级合成数据生成工具。它能够从现有的桩身完整性数据中学习特征分布，然后结合GZ方法的物理约束生成高质量的合成训练数据。

## ✨ 主要特性

### 🧠 **智能学习功能**
- **自动特征提取**: 从原始数据中提取13个关键特征
- **分布学习**: 使用高斯混合模型学习各类桩的特征分布
- **统计分析**: 自动分析数据的统计特性和分布规律

### 🎯 **GZ方法集成**
- **物理约束**: 基于GZ传统方法的判定规则
- **参数优化**: 自动调整生成参数以符合桩类特征
- **质量验证**: 内置GZ验证器确保生成数据的准确性

### 🎨 **现代化GUI界面**
- **美观设计**: 采用现代化的扁平设计风格
- **实时进度**: 可视化的学习和生成进度显示
- **数据可视化**: 内置图表展示学习结果和数据分布
- **用户友好**: 直观的操作界面和详细的状态反馈

## 🚀 快速开始

### 1. 启动程序
```bash
python intelligent_synthetic_data_generator_gui.py
```

### 2. 选择数据目录
- 点击"📂 浏览"按钮
- 选择包含I、II、III、IV类桩数据的目录
- 确保目录结构如下：
```
training_data/
├── I/          # I类桩数据文件
├── II/         # II类桩数据文件
├── III/        # III类桩数据文件
└── IV/         # IV类桩数据文件
```

### 3. 开始学习
- 点击"🧠 开始学习数据分布"按钮
- 等待学习完成（进度条会显示实时进度）
- 查看学习结果和数据分布可视化

### 4. 生成数据
- 选择要生成的桩类（I、II、III、IV类桩）
- 设置样本数量（建议50-100个）
- 点击"🚀 生成智能数据"或"🎯 生成全部类别"
- 生成的数据将自动保存到原始数据目录中

## 📊 界面说明

### 📁 **数据源配置面板**
- **训练数据目录**: 显示当前选择的数据目录路径
- **浏览按钮**: 选择包含训练数据的目录
- **学习按钮**: 开始学习数据分布
- **状态显示**: 显示当前操作状态和提示信息

### 📊 **处理进度面板**
- **进度条**: 显示学习或生成的实时进度
- **进度文本**: 显示当前正在处理的具体任务

### 📈 **学习结果与数据生成面板**
- **结果显示**: 显示学习到的统计信息和分布特征
- **桩类选择**: 选择要生成的桩身完整性类别
- **样本数量**: 设置要生成的样本数量
- **生成按钮**: 执行数据生成任务

### 📊 **数据分布可视化面板**
- **样本数量分布**: 各类桩的样本文件数量对比
- **数据点分布**: 各类桩的数据点总数对比
- **平均波速分布**: 各类桩的平均相对波速对比
- **平均波幅分布**: 各类桩的平均波幅差值对比

## 🔧 技术原理

### 🧠 **机器学习算法**
1. **特征提取**: 从原始7列数据中提取13个统计和差异特征
2. **数据标准化**: 使用StandardScaler进行特征标准化
3. **分布建模**: 使用高斯混合模型(GMM)学习特征分布
4. **智能采样**: 从学习到的分布中采样生成新数据

### ⚖️ **GZ方法约束**
1. **I(j,i)值计算**: 基于Sp、Ad、Bi_ratio计算完整性指数
2. **K(i)值计算**: K(i) = [∑I(j,i)² / ∑I(j,i)] + 0.5
3. **最终判定**: 基于K值分布确定桩身完整性类别
4. **参数调整**: 根据目标桩类调整生成参数范围

### 🎯 **质量保证**
- **实时验证**: 生成过程中实时验证数据质量
- **GZ验证器**: 使用内置GZ计算器验证生成数据的准确性
- **统计一致性**: 确保生成数据的统计特性与学习数据一致

## 📁 **输出文件格式**

生成的数据文件采用标准的制表符分隔格式：
```
Depth(m)    1-2 Speed%    1-2 Amp%    1-3 Speed%    1-3 Amp%    2-3 Speed%    2-3 Amp%
10.00       105.23        -1.45       103.67        -0.89       106.12        -2.01
10.20       104.89        -1.23       104.12        -1.12       105.67        -1.78
...
```

文件命名规则：
```
intelligent_{桩类}_{时间戳}_{序号}.txt
```

## 🎯 **性能指标**

根据测试结果：
- **I类桩生成准确率**: 100% ✅
- **IV类桩生成准确率**: 100% ✅
- **II类桩生成准确率**: 需要优化 ⚠️
- **III类桩生成准确率**: 需要优化 ⚠️

## 🔧 **故障排除**

### 常见问题

1. **"缺少目录"错误**
   - 确保数据目录包含I、II、III、IV四个子目录
   - 检查子目录中是否包含.txt格式的数据文件

2. **学习失败**
   - 检查数据文件格式是否正确（7列制表符分隔）
   - 确保数据文件不为空且包含有效数据

3. **生成验证不匹配**
   - 这是正常现象，表示算法正在学习和优化
   - I类桩和IV类桩通常有较高的准确率

4. **程序启动失败**
   - 确保安装了所需的Python包：pandas, numpy, matplotlib, seaborn, scikit-learn
   - 检查Python版本（推荐3.8+）

## 📞 **技术支持**

如遇到问题，请检查：
1. Python环境和依赖包是否正确安装
2. 数据目录结构和文件格式是否符合要求
3. 系统资源是否充足（内存、磁盘空间）

## 🔄 **版本历史**

- **v2.0**: 智能学习功能，现代化GUI界面，GZ方法集成
- **v1.0**: 基础合成数据生成功能

---

*智能桩身完整性数据生成器 - 让AI学习传统工程智慧，生成更优质的训练数据*
