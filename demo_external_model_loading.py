#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
外部模型加载功能演示脚本
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def create_demo_model():
    """创建演示用的模型文件"""
    print("🔧 创建演示模型文件")
    print("=" * 60)
    
    # 创建演示目录
    demo_dir = "demo_models"
    os.makedirs(demo_dir, exist_ok=True)
    
    # 生成演示数据
    np.random.seed(42)
    X = np.random.randn(200, 54)  # 54个特征
    y = np.random.randint(0, 4, 200)  # 4个类别 (I-IV类桩)
    
    # 创建一个高性能的演示模型
    print("📊 训练演示模型...")
    
    # 数据预处理
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 训练分类器
    classifier = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        class_weight='balanced'
    )
    classifier.fit(X_scaled, y)
    
    # 计算训练准确率
    train_accuracy = classifier.score(X_scaled, y)
    print(f"✅ 训练准确率: {train_accuracy:.2%}")
    
    # 创建完整模型包
    demo_model = {
        'classifier_model': classifier,
        'scaler': scaler,
        'feature_importance': dict(zip([f'特征_{i+1}' for i in range(54)], 
                                     classifier.feature_importances_)),
        'metadata': {
            'name': '演示高性能模型',
            'version': '1.0',
            'accuracy': train_accuracy,
            'features': 54,
            'classes': ['I类桩', 'II类桩', 'III类桩', 'IV类桩'],
            'description': '基于随机森林的高性能桩基完整性分析模型'
        }
    }
    
    # 保存模型
    model_path = os.path.join(demo_dir, "demo_high_performance_model.pkl")
    with open(model_path, 'wb') as f:
        pickle.dump(demo_model, f)
    
    print(f"✅ 演示模型已保存: {model_path}")
    print(f"📁 文件大小: {os.path.getsize(model_path) / 1024:.1f} KB")
    
    return model_path

def demo_model_loading():
    """演示模型加载过程"""
    print("\n📥 演示模型加载过程")
    print("=" * 60)
    
    # 创建演示模型
    model_path = create_demo_model()
    
    try:
        from model_manager import get_model_manager
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        # 获取模型管理器
        model_manager = get_model_manager()
        
        print(f"\n🔍 分析模型文件: {model_path}")
        
        # 加载外部模型
        model_name = "演示高性能模型 v1.0"
        success = model_manager.load_external_model(model_path, model_name)
        
        if success:
            print(f"✅ 模型加载成功!")
            
            # 获取模型信息
            models = model_manager.get_available_models()
            for key, model_info in models.items():
                if model_info.name == model_name:
                    print(f"\n📊 模型详细信息:")
                    print(f"  - 名称: {model_info.name}")
                    print(f"  - 类型: {model_info.model_type}")
                    print(f"  - 特征数: {model_info.feature_count}")
                    print(f"  - 预期准确率: {model_info.accuracy:.1%}")
                    print(f"  - 文件大小: {model_info.file_size / 1024:.1f} KB")
                    print(f"  - 创建时间: {model_info.created_date}")
                    break
            
            # 测试预测功能
            print(f"\n🎯 测试预测功能:")
            
            # 创建测试数据
            test_data = {
                'Depth(m)': [0.5, 1.0, 1.5, 2.0, 2.5, 3.0],
                '1-2 Speed%': [98, 95, 88, 82, 75, 70],
                '1-2 Amp%': [1, 3, 6, 10, 15, 20],
                '1-3 Speed%': [97, 94, 87, 81, 74, 69],
                '1-3 Amp%': [2, 4, 7, 11, 16, 21],
                '2-3 Speed%': [99, 96, 89, 83, 76, 71],
                '2-3 Amp%': [1, 2, 5, 9, 14, 19]
            }
            
            df = pd.DataFrame(test_data)
            print(f"📊 测试数据: {df.shape}")
            
            # 使用AI分析器V2进行预测
            analyzer = get_ai_analyzer_v2()
            
            # 设置刚加载的模型
            for key, model_info in models.items():
                if model_info.name == model_name:
                    analyzer.set_model(key)
                    break
            
            # 进行预测
            result = analyzer.predict(df)
            
            if result:
                category = result.get('完整性类别', 'N/A')
                confidence = result.get('ai_confidence', 0.0)
                
                # 转换数字类别为中文名称
                category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                if isinstance(category, (int, float, np.integer, np.floating)):
                    category_name = category_mapping.get(int(category), f'未知类别({category})')
                else:
                    category_name = category
                
                print(f"✅ 预测结果:")
                print(f"  - 桩基完整性类别: {category_name}")
                print(f"  - AI置信度: {confidence:.2%}")
                
                # 显示类别概率
                class_probs = result.get('class_probabilities', {})
                if class_probs:
                    print(f"  - 各类别概率:")
                    for class_key, prob in class_probs.items():
                        if isinstance(class_key, (int, float, np.integer, np.floating)):
                            class_name = category_mapping.get(int(class_key), f'类别{class_key}')
                        else:
                            class_name = class_key
                        print(f"    {class_name}: {prob:.2%}")
                
                print(f"\n🎉 演示模型预测成功!")
            else:
                print(f"❌ 预测失败")
        else:
            print(f"❌ 模型加载失败")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def show_gui_usage():
    """显示GUI使用说明"""
    print("\n🖥️ GUI使用说明")
    print("=" * 60)
    
    print("📋 使用步骤:")
    print("1. 启动GUI: python Pile_analyze_GZ_gui.py")
    print("2. 选择 '🚀 AI System V2.0'")
    print("3. 在模型选择面板中点击 '📥 加载模型'")
    print("4. 选择刚创建的演示模型文件:")
    print("   demo_models/demo_high_performance_model.pkl")
    print("5. 在对话框中:")
    print("   - 查看模型预览信息")
    print("   - 设置模型名称")
    print("   - 点击 '✅ 加载模型'")
    print("6. 模型加载后会自动出现在下拉列表中")
    print("7. 选择新加载的模型进行分析")
    
    print(f"\n💡 提示:")
    print("- 模型预览会显示详细的兼容性信息")
    print("- 系统会自动选择匹配的特征提取器")
    print("- 加载的模型会保存在模型注册表中")
    print("- 可以随时切换不同的模型进行对比")

def cleanup_demo():
    """清理演示文件"""
    print(f"\n🧹 清理演示文件")
    print("=" * 60)
    
    try:
        import shutil
        
        demo_dir = "demo_models"
        if os.path.exists(demo_dir):
            shutil.rmtree(demo_dir)
            print(f"✅ 已删除演示目录: {demo_dir}")
        
        # 可选：清理模型注册表
        # registry_file = "ai_models/models_registry.json"
        # if os.path.exists(registry_file):
        #     os.remove(registry_file)
        #     print(f"✅ 已清理模型注册表: {registry_file}")
        
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

def main():
    """主函数"""
    print("🎬 外部模型加载功能演示")
    print("=" * 80)
    
    try:
        # 演示模型加载
        demo_model_loading()
        
        # 显示GUI使用说明
        show_gui_usage()
        
        # 询问是否清理
        print(f"\n❓ 是否清理演示文件？")
        print("演示模型文件将被保留，您可以在GUI中继续使用")
        print("输入 'y' 清理，其他键保留文件")
        
        try:
            choice = input("请选择: ").strip().lower()
            if choice == 'y':
                cleanup_demo()
            else:
                print("📁 演示文件已保留，您可以在GUI中使用")
                print("文件位置: demo_models/demo_high_performance_model.pkl")
        except:
            print("📁 演示文件已保留")
        
        print(f"\n🎉 演示完成!")
        print(f"现在您可以启动GUI并尝试加载外部模型功能")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
