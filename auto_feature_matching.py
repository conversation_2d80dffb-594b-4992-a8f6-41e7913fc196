
def auto_match_features_for_model(model_data, df):
    """自动为模型匹配正确的特征"""
    try:
        # 获取模型期望的特征数量
        classifier = model_data.get('classifier_model') or model_data.get('model')
        
        if not classifier:
            raise ValueError("No classifier found in model")
        
        expected_features = None
        
        # 尝试获取期望特征数
        if hasattr(classifier, 'n_features_in_'):
            expected_features = classifier.n_features_in_
        elif hasattr(classifier, 'estimators_'):
            for estimator in classifier.estimators_:
                if hasattr(estimator, 'n_features_in_'):
                    expected_features = estimator.n_features_in_
                    break
        
        if expected_features is None:
            print("⚠️ 无法确定模型期望的特征数量")
            return None, None
        
        print(f"🎯 模型期望特征数: {expected_features}")
        
        # 根据期望特征数选择合适的特征提取器
        from feature_manager import get_feature_manager
        
        feature_manager = get_feature_manager()
        extractors = feature_manager.get_available_extractors()
        
        best_extractor = None
        best_features = None
        best_feature_names = None
        
        for extractor_name in extractors:
            try:
                feature_manager.set_current_extractor(extractor_name)
                extractor = feature_manager.get_current_extractor()
                
                features, feature_names = extractor.extract_features(df)
                feature_count = features.shape[1] if features.ndim > 1 else len(features)
                
                print(f"🔍 {extractor_name}: {feature_count} 特征")
                
                # 如果特征数量匹配
                if feature_count == expected_features:
                    print(f"✅ 找到匹配的特征提取器: {extractor_name}")
                    best_extractor = extractor_name
                    best_features = features
                    best_feature_names = feature_names
                    break
                    
            except Exception as e:
                print(f"⚠️ {extractor_name} 测试失败: {e}")
        
        # 如果没有完全匹配的，尝试特征选择
        if best_extractor is None and 'feature_selector' in model_data:
            print("🔧 尝试使用特征选择器...")
            
            # 使用高精度特征提取器
            feature_manager.set_current_extractor('advanced')
            extractor = feature_manager.get_current_extractor()
            
            features, feature_names = extractor.extract_features(df)
            
            # 应用特征选择器
            feature_selector = model_data['feature_selector']
            
            if hasattr(feature_selector, 'transform'):
                selected_features = feature_selector.transform(features.reshape(1, -1))
                
                if selected_features.shape[1] == expected_features:
                    print(f"✅ 特征选择后匹配: {selected_features.shape[1]} 特征")
                    best_features = selected_features
                    best_feature_names = [f"selected_feature_{i}" for i in range(selected_features.shape[1])]
                    best_extractor = 'advanced_with_selection'
        
        return best_features, best_feature_names, best_extractor
        
    except Exception as e:
        print(f"❌ 自动特征匹配失败: {e}")
        return None, None, None
