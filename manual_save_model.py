#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
手动保存训练模型脚本
Manual Model Save Script
"""

import os
import pickle
from datetime import datetime

def manual_save_model():
    """手动保存模型"""
    print("💾 手动保存模型")
    print("=" * 40)
    
    try:
        # 检查是否有增强训练器的模型文件
        enhanced_model_files = [
            "enhanced_quick_model.pkl",
            "enhanced_advanced_model.pkl", 
            "enhanced_research_model.pkl",
            "enhanced_94_percent_model.pkl"
        ]
        
        found_models = []
        for model_file in enhanced_model_files:
            if os.path.exists(model_file):
                found_models.append(model_file)
                print(f"✅ 找到模型: {model_file}")
        
        if not found_models:
            print("❌ 未找到任何训练好的模型")
            return False
        
        # 创建保存目录
        save_dir = "saved_models"
        os.makedirs(save_dir, exist_ok=True)
        
        # 复制模型到保存目录
        import shutil
        for model_file in found_models:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = os.path.splitext(model_file)[0]
            new_name = f"{base_name}_{timestamp}.pkl"
            new_path = os.path.join(save_dir, new_name)
            
            shutil.copy2(model_file, new_path)
            print(f"📁 已保存: {new_path}")
        
        print(f"\n🎉 所有模型已保存到: {save_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 手动保存失败: {e}")
        return False

if __name__ == "__main__":
    manual_save_model()
