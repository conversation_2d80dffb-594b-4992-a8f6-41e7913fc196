
# 增强训练系统集成指南

## 🎯 如何将94%+精度训练集成到现有系统

### 1. 修改 auto_train_classify_gui.py

在 `setup_training_modes` 方法中添加新的训练模式：

```python
# 添加增强训练模式
enhanced_frame = tk.Frame(self.training_modes_frame, bg='white', relief='solid', bd=1)
enhanced_frame.pack(fill='x', pady=5)

enhanced_header = tk.Frame(enhanced_frame, bg='#059669', height=50)
enhanced_header.pack(fill='x')
enhanced_header.pack_propagate(False)

tk.Radiobutton(enhanced_header, text="🎯 Enhanced 94%+ Training (Optimized)",
              variable=self.training_mode_var, value="enhanced",
              font=('Segoe UI', 12, 'bold'),
              fg='white', bg='#059669',
              selectcolor='#059669').pack(pady=15)

enhanced_desc = tk.Frame(enhanced_frame, bg='white')
enhanced_desc.pack(fill='x', padx=15, pady=15)

tk.Label(enhanced_desc, 
        text="• 118个高精度特征提取\n• 集成学习 (RF+XGB+SVM+GB)\n• SMOTE数据增强\n• 目标准确率: 94%+",
        font=('Segoe UI', 10), fg='#059669', bg='white', justify='left').pack(anchor='w')
```

### 2. 在 `start_training` 方法中添加增强训练分支：

```python
if mode == 'quick':
    self.run_quick_training()
elif mode == 'advanced':
    self.run_advanced_training()
elif mode == 'research':
    self.run_research_training()
elif mode == 'enhanced':  # 新增
    self.run_enhanced_training()
```

### 3. 添加 `run_enhanced_training` 方法：

```python
def run_enhanced_training(self):
    """运行增强94%+精度训练"""
    self.log_message("Starting Enhanced 94%+ Training mode...")
    
    try:
        from enhanced_training_system import Enhanced94PercentTrainer
        
        # 创建增强训练器
        trainer = Enhanced94PercentTrainer()
        
        # 准备数据
        self.progress_queue.put({
            'progress': 20,
            'status': 'Preparing enhanced training data...'
        })
        
        X, y = trainer.prepare_data(self.training_system.training_data_dir)
        
        # 训练94%+模型
        def progress_callback(progress, status):
            self.progress_queue.put({
                'progress': 20 + (progress * 0.7),  # 20-90% range
                'status': status
            })
        
        results = trainer.train_94_percent_model(X, y, progress_callback)
        
        # 保存模型
        self.progress_queue.put({
            'progress': 95,
            'status': 'Saving enhanced model...'
        })
        
        model_path = os.path.join(self.training_system.results_dir, 'enhanced_94_percent_model.pkl')
        trainer.save_model(model_path)
        
        # 更新结果
        self.training_results = {
            'mode': 'enhanced',
            'model_type': 'enhanced_94_percent',
            'completed': True,
            'accuracy': results['accuracy'],
            'trainer': trainer,
            'model_path': model_path
        }
        
        self.progress_queue.put({
            'progress': 100,
            'status': f'Enhanced training completed! Accuracy: {results["accuracy"]:.2%}',
            'system_status': 'Ready'
        })
        
        self.log_message(f"Enhanced Training completed with {results['accuracy']:.2%} accuracy!")
        
    except Exception as e:
        error_msg = f"Enhanced training failed: {str(e)}"
        self.log_message(f"ERROR: {error_msg}")
        self.progress_queue.put({
            'status': error_msg,
            'system_status': 'Error'
        })
        raise
```

## 🚀 使用步骤

1. 确保有足够的训练数据 (每类至少10个样本)
2. 启动 auto_train_classify_gui.py
3. 选择 "🎯 Enhanced 94%+ Training (Optimized)"
4. 点击 "🚀 Start Training"
5. 等待训练完成 (可能需要几分钟)
6. 查看训练结果和准确率

## 📊 预期效果

- **准确率**: 94%+
- **特征数**: 118个高精度特征
- **模型类型**: 集成学习 (RF+XGB+SVM+GB)
- **数据增强**: SMOTE处理类别不平衡
- **特征选择**: 递归特征消除

## ⚠️ 注意事项

1. 需要安装额外依赖: `pip install xgboost imbalanced-learn`
2. 训练时间较长，建议在性能较好的机器上运行
3. 需要足够的训练数据才能达到最佳效果
4. 模型文件较大，注意存储空间
