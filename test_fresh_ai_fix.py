#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全新测试AI修复 - 避免模块缓存问题
"""

import os
import sys
import importlib
import pandas as pd

# 强制清除所有相关模块缓存
modules_to_clear = [k for k in sys.modules.keys() if 'Pile_analyze_GZ_gui' in k]
for module in modules_to_clear:
    del sys.modules[module]

# 重新导入模块
from Pile_analyze_GZ_gui import BuiltInAIAnalyzer

def test_fresh_ai_fix():
    """全新测试AI修复"""
    print("🧪 全新测试AI修复 (避免缓存问题)")
    print("=" * 80)

    # 创建AI分析器
    analyzer = BuiltInAIAnalyzer()

    # 加载外部模型
    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return

    print(f"📥 加载外部模型: {model_path}")
    success = analyzer.load_models(model_path)

    if not success:
        print("❌ 外部模型加载失败")
        return

    print("✅ 外部模型加载成功")

    # 测试文件
    test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"

    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return

    try:
        # 读取数据
        df = pd.read_csv(test_file, sep='\t')
        print(f"✅ 数据读取成功: {df.shape}")

        # 提取特征
        features, feature_names = analyzer.extract_features(df)
        print(f"✅ 特征提取成功: {features.shape}")

        # 进行预测
        result = analyzer.predict(features)

        if result is None:
            print("❌ 预测失败")
            return

        print("✅ 预测成功")

        # 检查原始结果
        print(f"\n📋 原始预测结果:")
        original_category = result['完整性类别']
        print(f"  完整性类别 (原始): {original_category}")
        print(f"  类型: {type(original_category)}")

        # 检查AI分析结论
        reasoning = result.get('overall_reasoning', '无分析结论')
        print(f"\n📋 AI分析结论:")
        print(reasoning)

        # 检查是否包含中文类别名称
        category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
        expected_chinese_name = category_mapping.get(int(original_category), f'未知类别({original_category})')

        if expected_chinese_name in reasoning:
            print(f"✅ AI分析结论中包含中文类别名称: {expected_chinese_name}")
        else:
            print(f"❌ AI分析结论中不包含中文类别名称: {expected_chinese_name}")
            print(f"   期望找到: {expected_chinese_name}")
            print(f"   实际内容: {reasoning[:100]}...")

        # 检查是否还有数字类别
        if f"AI分析结果：{original_category}" in reasoning:
            print(f"❌ AI分析结论中仍然包含数字类别: {original_category}")
        else:
            print(f"✅ AI分析结论中不包含数字类别")

        # 直接测试_generate_reasoning方法
        print(f"\n🔧 直接测试_generate_reasoning方法:")

        test_reasoning = analyzer._generate_reasoning(
            original_category,
            result['ai_confidence'],
            result['anomaly_score'],
            result['class_probabilities']
        )

        print("直接调用_generate_reasoning的结果:")
        print(test_reasoning)

        if expected_chinese_name in test_reasoning:
            print(f"✅ 直接调用方法包含中文类别名称: {expected_chinese_name}")
        else:
            print(f"❌ 直接调用方法不包含中文类别名称: {expected_chinese_name}")

        # 模拟GUI显示逻辑
        print(f"\n🖥️ 模拟GUI显示逻辑:")

        # Convert numeric category to Chinese name if needed
        category = result.get('完整性类别', 'N/A')
        import numpy as np
        if isinstance(category, (int, float, np.integer, np.floating)):
            category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
            category = category_mapping.get(int(category), f'未知类别({category})')

        print(f"桩基完整性类别: {category}")
        print(f"AI置信度: {result.get('ai_confidence', 0.0):.2%}")
        print(f"异常分数: {result.get('anomaly_score', 0.0):.2f}")
        print()
        print(f"AI分析结论: {result.get('overall_reasoning', '无分析结论')}")
        print()

        # 显示类别概率
        print("各类别概率:")
        class_probabilities = result.get('class_probabilities', {})
        category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}

        for class_key, prob in class_probabilities.items():
            # Convert numeric keys to Chinese names
            if isinstance(class_key, (int, float, np.integer, np.floating)):
                class_name = category_mapping.get(int(class_key), f'类别{class_key}')
            else:
                class_name = class_key
            print(f"  {class_name}: {prob:.2%}")

        print("\n✅ 全新AI修复测试完成!")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fresh_ai_fix()
