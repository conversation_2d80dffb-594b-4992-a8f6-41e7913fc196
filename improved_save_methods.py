
    def save_trained_model(self):
        """Save the trained model with improved error handling"""
        try:
            # Get default model name based on training mode and timestamp
            mode = getattr(self, 'current_training_mode', 'unknown')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"pile_model_{mode}_{timestamp}.pkl"

            # Ask user for save location and name
            file_path = filedialog.asksaveasfilename(
                title="Save Trained Model",
                defaultextension=".pkl",
                filetypes=[
                    ("Pickle files", "*.pkl"),
                    ("All files", "*.*")
                ],
                initialdir="advanced_models" if os.path.exists("advanced_models") else "."
            )

            if not file_path:
                return

            # Create directory if it doesn't exist
            dir_path = os.path.dirname(file_path)
            if dir_path:
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    print(f"📁 目录已创建: {dir_path}")
                except Exception as e:
                    print(f"⚠️ 创建目录失败: {e}")

            # Improved save logic
            success = self.robust_save_model(file_path)

            if success:
                messagebox.showinfo("Model Saved",
                                  f"Model successfully saved to:\n{file_path}")
                self.log_message(f"Model saved to: {file_path}")

                # Update status
                self.progress_queue.put({
                    'status': f'Model saved to: {os.path.basename(file_path)}'
                })
            else:
                messagebox.showerror("Save Failed", "Failed to save the model.")

        except Exception as e:
            error_msg = f"Failed to save model: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            messagebox.showerror("Error", error_msg)
            self.log_message(f"Error saving model: {str(e)}")

    def robust_save_model(self, file_path):
        """Robust model save method with comprehensive error handling"""
        try:
            print(f"🔄 开始保存模型到: {file_path}")
            
            # Check if we have training results
            if not hasattr(self, 'training_results') or not self.training_results:
                print("❌ 没有训练结果可保存")
                return False
            
            mode = self.training_results.get('mode', 'unknown')
            model_type = self.training_results.get('model_type', '')
            
            print(f"📊 训练模式: {mode}")
            print(f"🤖 模型类型: {model_type}")
            
            # Save enhanced trainer models
            if 'enhanced' in model_type and 'trainer' in self.training_results:
                print("💾 保存增强训练模型...")
                trainer = self.training_results['trainer']
                
                if hasattr(trainer, 'save_model') and trainer.is_trained:
                    trainer.save_model(file_path)
                    print("✅ 增强模型保存成功")
                    return True
                else:
                    print("❌ 增强训练器未训练或无保存方法")
                    return False
            
            # Save traditional training system models
            elif hasattr(self, 'training_system') and self.training_system:
                print("💾 保存传统训练模型...")
                success = self.training_system.save_model(file_path)
                if success:
                    print("✅ 传统模型保存成功")
                else:
                    print("❌ 传统模型保存失败")
                return success
            
            else:
                print("❌ 没有可用的训练模型")
                return False
                
        except Exception as e:
            print(f"❌ 保存模型失败: {e}")
            traceback.print_exc()
            return False
