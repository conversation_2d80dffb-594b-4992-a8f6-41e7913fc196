#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modern AI Pile Integrity Analysis System - GUI
现代化AI桩基完整性分析系统 - 图形界面

A clean, modern, and user-friendly GUI for pile integrity analysis with AI capabilities.
Features:
- Clean and consistent design
- Easy-to-use interface
- Integrated training data generation
- Real-time progress feedback
- Professional appearance

Author: AI Pile Integrity Analysis System
Version: 3.0 (Modern GUI)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
import time
from datetime import datetime

class ModernPileGUI:
    """Modern, clean GUI for AI Pile Integrity Analysis"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AI Pile Integrity Analysis System v3.0")
        self.root.geometry("900x700")
        self.root.configure(bg='#f5f5f5')
        
        # Modern color scheme
        self.colors = {
            'primary': '#2c3e50',      # Dark blue
            'secondary': '#3498db',    # Light blue
            'success': '#27ae60',      # Green
            'warning': '#f39c12',      # Orange
            'danger': '#e74c3c',       # Red
            'light': '#ecf0f1',        # Light gray
            'white': '#ffffff',        # White
            'text': '#2c3e50',         # Dark text
            'bg': '#f5f5f5'           # Background
        }
        
        # Initialize variables
        self.training_data_dir = tk.StringVar()
        self.samples_per_class = tk.IntVar(value=50)
        self.training_mode = tk.StringVar(value="quick")
        self.generate_synthetic = tk.BooleanVar(value=True)
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready")
        
        self.setup_styles()
        self.create_interface()
        
    def setup_styles(self):
        """Setup modern styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 18, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['bg'])
        
        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=self.colors['text'],
                       background=self.colors['bg'])
        
        style.configure('Info.TLabel',
                       font=('Segoe UI', 10),
                       foreground='#666666',
                       background=self.colors['bg'])
        
        style.configure('Modern.TButton',
                       font=('Segoe UI', 10),
                       padding=(15, 8))
        
        style.configure('Primary.TButton',
                       font=('Segoe UI', 11, 'bold'),
                       padding=(20, 10))
        
        style.configure('Card.TFrame',
                       background=self.colors['white'],
                       relief='solid',
                       borderwidth=1)
        
    def create_interface(self):
        """Create the main interface"""
        # Header
        self.create_header()
        
        # Main content area
        main_frame = tk.Frame(self.root, bg=self.colors['bg'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Create cards
        self.create_setup_card(main_frame)
        self.create_training_card(main_frame)
        self.create_progress_card(main_frame)
        
        # Footer
        self.create_footer()
        
    def create_header(self):
        """Create modern header"""
        header = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)
        
        # Title
        title = tk.Label(header,
                        text="AI Pile Integrity Analysis System",
                        font=('Segoe UI', 20, 'bold'),
                        fg=self.colors['white'],
                        bg=self.colors['primary'])
        title.pack(pady=(20, 5))
        
        # Subtitle
        subtitle = tk.Label(header,
                           text="Modern AI-Powered Pile Integrity Assessment",
                           font=('Segoe UI', 11),
                           fg=self.colors['light'],
                           bg=self.colors['primary'])
        subtitle.pack()
        
    def create_setup_card(self, parent):
        """Create setup configuration card"""
        card = ttk.Frame(parent, style='Card.TFrame', padding=20)
        card.pack(fill='x', pady=(0, 15))
        
        # Card title
        ttk.Label(card, text="📁 Training Data Setup", style='Heading.TLabel').pack(anchor='w', pady=(0, 15))
        
        # Training data directory
        dir_frame = tk.Frame(card, bg=self.colors['white'])
        dir_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(dir_frame, text="Training Data Directory:", style='Info.TLabel').pack(anchor='w', pady=(0, 5))
        
        dir_input_frame = tk.Frame(dir_frame, bg=self.colors['white'])
        dir_input_frame.pack(fill='x')
        
        self.dir_entry = tk.Entry(dir_input_frame,
                                 textvariable=self.training_data_dir,
                                 font=('Segoe UI', 10),
                                 bg='white',
                                 relief='solid',
                                 bd=1)
        self.dir_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        ttk.Button(dir_input_frame,
                  text="Browse",
                  command=self.browse_directory,
                  style='Modern.TButton').pack(side='right')
        
        # Quick setup button
        ttk.Button(card,
                  text="🚀 Quick Setup (Auto-detect directories)",
                  command=self.quick_setup,
                  style='Modern.TButton').pack(pady=(10, 0))
        
    def create_training_card(self, parent):
        """Create training configuration card"""
        card = ttk.Frame(parent, style='Card.TFrame', padding=20)
        card.pack(fill='x', pady=(0, 15))
        
        # Card title
        ttk.Label(card, text="⚙️ Training Configuration", style='Heading.TLabel').pack(anchor='w', pady=(0, 15))
        
        # Two column layout
        config_frame = tk.Frame(card, bg=self.colors['white'])
        config_frame.pack(fill='x')
        
        # Left column
        left_col = tk.Frame(config_frame, bg=self.colors['white'])
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        # Synthetic data generation
        ttk.Checkbutton(left_col,
                       text="Generate synthetic training data",
                       variable=self.generate_synthetic,
                       command=self.on_synthetic_toggle).pack(anchor='w', pady=(0, 10))
        
        # Samples per class
        samples_frame = tk.Frame(left_col, bg=self.colors['white'])
        samples_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(samples_frame, text="Samples per class:", style='Info.TLabel').pack(anchor='w')
        
        samples_input = tk.Frame(samples_frame, bg=self.colors['white'])
        samples_input.pack(fill='x', pady=(5, 0))
        
        self.samples_scale = tk.Scale(samples_input,
                                     from_=10, to=200,
                                     orient='horizontal',
                                     variable=self.samples_per_class,
                                     bg=self.colors['white'],
                                     highlightthickness=0,
                                     font=('Segoe UI', 9))
        self.samples_scale.pack(fill='x')
        
        # Right column
        right_col = tk.Frame(config_frame, bg=self.colors['white'])
        right_col.pack(side='right', fill='both', expand=True)
        
        # Training mode
        ttk.Label(right_col, text="Training Mode:", style='Info.TLabel').pack(anchor='w', pady=(0, 5))
        
        modes = [
            ("Quick Training", "quick", "Fast setup with traditional methods"),
            ("Advanced Training", "advanced", "Deep learning with better accuracy"),
            ("Research Mode", "research", "Full analysis with detailed reports")
        ]
        
        for text, value, desc in modes:
            mode_frame = tk.Frame(right_col, bg=self.colors['white'])
            mode_frame.pack(fill='x', pady=2)
            
            ttk.Radiobutton(mode_frame,
                           text=text,
                           variable=self.training_mode,
                           value=value).pack(anchor='w')
            
            ttk.Label(mode_frame,
                     text=f"  {desc}",
                     style='Info.TLabel').pack(anchor='w', padx=(20, 0))
        
    def create_progress_card(self, parent):
        """Create progress and control card"""
        card = ttk.Frame(parent, style='Card.TFrame', padding=20)
        card.pack(fill='x', pady=(0, 15))
        
        # Card title
        ttk.Label(card, text="🚀 Training Control", style='Heading.TLabel').pack(anchor='w', pady=(0, 15))
        
        # Progress bar
        progress_frame = tk.Frame(card, bg=self.colors['white'])
        progress_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(progress_frame, text="Progress:", style='Info.TLabel').pack(anchor='w', pady=(0, 5))
        
        self.progress_bar = ttk.Progressbar(progress_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           length=400)
        self.progress_bar.pack(fill='x', pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame,
                                     textvariable=self.status_var,
                                     style='Info.TLabel')
        self.status_label.pack(anchor='w')
        
        # Control buttons
        button_frame = tk.Frame(card, bg=self.colors['white'])
        button_frame.pack(fill='x')
        
        self.start_button = ttk.Button(button_frame,
                                      text="🚀 Start Training",
                                      command=self.start_training,
                                      style='Primary.TButton')
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame,
                                     text="⏹ Stop",
                                     command=self.stop_training,
                                     state='disabled',
                                     style='Modern.TButton')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(button_frame,
                  text="📊 View Results",
                  command=self.view_results,
                  style='Modern.TButton').pack(side='right')
        
    def create_footer(self):
        """Create footer with status and info"""
        footer = tk.Frame(self.root, bg=self.colors['light'], height=40)
        footer.pack(fill='x', side='bottom')
        footer.pack_propagate(False)
        
        # Status
        status_frame = tk.Frame(footer, bg=self.colors['light'])
        status_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        tk.Label(status_frame,
                text="Status:",
                font=('Segoe UI', 9, 'bold'),
                bg=self.colors['light'],
                fg=self.colors['text']).pack(side='left')
        
        tk.Label(status_frame,
                textvariable=self.status_var,
                font=('Segoe UI', 9),
                bg=self.colors['light'],
                fg=self.colors['text']).pack(side='left', padx=(5, 0))
        
        # Version info
        tk.Label(status_frame,
                text="AI Pile Analysis v3.0",
                font=('Segoe UI', 9),
                bg=self.colors['light'],
                fg=self.colors['secondary']).pack(side='right')
        
    def browse_directory(self):
        """Browse for training data directory"""
        directory = filedialog.askdirectory(title="Select Training Data Directory")
        if directory:
            self.training_data_dir.set(directory)
            self.status_var.set(f"Selected: {os.path.basename(directory)}")
            
    def quick_setup(self):
        """Quick setup with auto-detection"""
        directory = filedialog.askdirectory(title="Select Root Directory (will auto-detect subdirectories)")
        if directory:
            self.training_data_dir.set(directory)
            
            # Check for subdirectories
            subdirs = []
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                if os.path.isdir(item_path):
                    subdirs.append(item)
            
            if subdirs:
                self.status_var.set(f"Auto-detected {len(subdirs)} subdirectories")
                messagebox.showinfo("Quick Setup", 
                                  f"Successfully detected {len(subdirs)} subdirectories:\n" + 
                                  "\n".join(subdirs[:5]) + 
                                  ("..." if len(subdirs) > 5 else ""))
            else:
                self.status_var.set("No subdirectories found")
                
    def on_synthetic_toggle(self):
        """Handle synthetic data generation toggle"""
        if self.generate_synthetic.get():
            self.samples_scale.configure(state='normal')
        else:
            self.samples_scale.configure(state='disabled')
            
    def start_training(self):
        """Start the training process"""
        if not self.training_data_dir.get():
            messagebox.showwarning("Warning", "Please select a training data directory first.")
            return
            
        # Disable start button, enable stop button
        self.start_button.configure(state='disabled')
        self.stop_button.configure(state='normal')
        
        # Start training in a separate thread
        self.training_thread = threading.Thread(target=self.run_training)
        self.training_thread.daemon = True
        self.training_thread.start()
        
    def run_training(self):
        """Run the actual training process"""
        try:
            self.status_var.set("Initializing training system...")
            self.progress_var.set(10)
            
            # Import training system
            from auto_train_and_classify import AutoTrainAndClassify
            
            self.status_var.set("Creating training system...")
            self.progress_var.set(20)
            
            # Create training system
            auto_system = AutoTrainAndClassify()
            auto_system.training_data_dir = self.training_data_dir.get()
            
            # Generate synthetic data if requested
            if self.generate_synthetic.get():
                self.status_var.set(f"Generating {self.samples_per_class.get()} samples per class...")
                self.progress_var.set(40)
                auto_system.generate_synthetic_data_files(samples_per_class=self.samples_per_class.get())
                
            self.status_var.set("Starting training...")
            self.progress_var.set(60)
            
            # Run training based on selected mode
            if self.training_mode.get() == "quick":
                auto_system.run_traditional_training()
            elif self.training_mode.get() == "advanced":
                auto_system.run_advanced_training()
            elif self.training_mode.get() == "research":
                results = auto_system.run_advanced_training()
                auto_system.generate_research_report(results)
                
            self.status_var.set("Training completed successfully!")
            self.progress_var.set(100)
            
            # Show success message
            self.root.after(0, lambda: messagebox.showinfo("Success", 
                                                          "Training completed successfully!\n"
                                                          "The AI model is now ready for use."))
            
        except Exception as e:
            self.status_var.set(f"Training failed: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Error", f"Training failed:\n{str(e)}"))
            
        finally:
            # Re-enable buttons
            self.root.after(0, self.training_finished)
            
    def training_finished(self):
        """Called when training is finished"""
        self.start_button.configure(state='normal')
        self.stop_button.configure(state='disabled')
        
    def stop_training(self):
        """Stop the training process"""
        self.status_var.set("Stopping training...")
        # Note: Actual stopping implementation would depend on the training system
        self.training_finished()
        
    def view_results(self):
        """View training results"""
        messagebox.showinfo("Results", "Results viewer will be implemented in the next version.")
        
    def run(self):
        """Run the GUI application"""
        # Center the window
        self.root.eval('tk::PlaceWindow . center')
        self.root.mainloop()

def main():
    """Main function"""
    try:
        app = ModernPileGUI()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
