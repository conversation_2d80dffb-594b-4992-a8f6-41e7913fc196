#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的GUI AI分析功能
"""

import pandas as pd
import os

def test_ai_v2_direct():
    """直接测试AI V2.0系统"""
    print("🧪 直接测试AI V2.0系统")
    print("=" * 60)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        # 获取AI分析器V2
        analyzer_v2 = get_ai_analyzer_v2()
        
        # 强制设置高精度配置
        models = analyzer_v2.model_manager.get_available_models()
        optimized_model_key = None
        
        for key, model_info in models.items():
            if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                optimized_model_key = key
                break
        
        if optimized_model_key:
            print(f"🎯 设置高精度模型: {optimized_model_key}")
            analyzer_v2.model_manager.load_model(optimized_model_key)
            analyzer_v2.set_model(optimized_model_key)
            analyzer_v2.set_feature_extractor('advanced')
        
        # 测试III类桩文件
        test_file = "training_data/III/1-2.txt"
        
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        print(f"📁 测试文件: {test_file}")
        
        # 读取数据
        df = pd.read_csv(test_file, sep='\t', encoding='utf-8')
        print(f"📊 数据形状: {df.shape}")
        
        # 进行预测
        result = analyzer_v2.predict(df)
        
        if result:
            predicted_class = result.get('完整性类别', -1)
            confidence = result.get('ai_confidence', 0.0)
            
            class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
            pred_name = class_names.get(predicted_class, f'未知({predicted_class})')
            
            print(f"🤖 AI V2.0预测结果: {pred_name}")
            print(f"🎯 置信度: {confidence:.2%}")
            print(f"📋 预期结果: III类桩")
            
            if predicted_class == 2:  # III类桩
                print(f"✅ 预测正确!")
                return True
            else:
                print(f"❌ 预测错误!")
                return False
        else:
            print(f"❌ 预测失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_ai_methods():
    """测试GUI中的AI方法"""
    print("\n🔍 测试GUI中的AI方法")
    print("=" * 60)
    
    try:
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        # 检查新添加的方法
        methods_to_check = [
            'run_ai_analysis',
            'run_ai_v1_analysis_internal',
            'run_ai_v2_analysis_internal'
        ]
        
        print("📋 检查GUI方法:")
        for method in methods_to_check:
            if hasattr(PileAnalyzerGZGUI, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} (缺失)")
        
        # 检查方法的文档字符串
        if hasattr(PileAnalyzerGZGUI, 'run_ai_analysis'):
            method = getattr(PileAnalyzerGZGUI, 'run_ai_analysis')
            doc = method.__doc__
            if doc and "selected AI system" in doc:
                print("✅ run_ai_analysis方法已更新")
            else:
                print("❌ run_ai_analysis方法未正确更新")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI方法检查失败: {e}")
        return False

def create_test_instructions():
    """创建测试说明"""
    print("\n📋 创建测试说明")
    print("=" * 60)
    
    instructions = """
🎯 GUI测试步骤

1. 启动GUI程序:
   python Pile_analyze_GZ_gui.py

2. 加载测试数据:
   - 点击"📂 Load Data"
   - 选择文件: training_data/III/1-2.txt

3. 选择AI System V2.0:
   - 在"🚀 AI System Selection"面板中
   - 选择"🚀 AI System V2.0 (推荐)"

4. 选择高精度模型:
   - 在"🤖 AI Model Selection (V2.0)"下拉菜单中
   - 选择"高精度AI模型 v1.0 (94.0%)"

5. 运行AI分析:
   - 点击"🤖 AI Analysis"按钮
   - 观察控制台输出，应该显示"🚀 Using AI System V2.0"

6. 检查结果:
   - 在"🤖 AI Enhanced Analysis Results"标签页中
   - 应该显示"III类桩"而不是"II类桩"

🔍 预期结果:
- 控制台输出: "🚀 Using AI System V2.0"
- 预测结果: "III类桩"
- 置信度: 应该较高 (>80%)

⚠️ 如果仍显示错误结果:
1. 检查控制台是否有错误信息
2. 确认选择了正确的AI系统版本
3. 确认选择了高精度模型
4. 重启GUI程序再试
"""
    
    with open('GUI_Test_Instructions.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ 测试说明已保存到: GUI_Test_Instructions.txt")

def main():
    """主函数"""
    print("🧪 测试修复后的GUI AI分析功能")
    print("=" * 80)
    
    # 1. 直接测试AI V2.0系统
    ai_v2_success = test_ai_v2_direct()
    
    # 2. 测试GUI方法
    gui_methods_success = test_gui_ai_methods()
    
    # 3. 创建测试说明
    create_test_instructions()
    
    # 4. 总结
    print(f"\n📋 测试总结")
    print("=" * 60)
    
    print(f"AI V2.0系统测试: {'✅ 成功' if ai_v2_success else '❌ 失败'}")
    print(f"GUI方法检查: {'✅ 成功' if gui_methods_success else '❌ 失败'}")
    
    if ai_v2_success and gui_methods_success:
        print(f"\n🎉 所有测试通过!")
        print(f"现在可以启动GUI程序测试修复效果")
        print(f"请按照 GUI_Test_Instructions.txt 中的步骤操作")
    else:
        print(f"\n⚠️ 部分测试失败")
        print(f"请检查相关问题后再测试GUI")
    
    print(f"\n🚀 下一步:")
    print(f"1. 启动GUI: python Pile_analyze_GZ_gui.py")
    print(f"2. 按照测试说明操作")
    print(f"3. 验证AI V2.0是否正确工作")

if __name__ == "__main__":
    main()
