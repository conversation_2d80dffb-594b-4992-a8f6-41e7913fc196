#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的模型加载对话框
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def create_test_model():
    """创建测试模型文件"""
    print("🔧 创建测试模型文件")
    print("=" * 60)
    
    # 确保ai_models目录存在
    ai_models_dir = "ai_models"
    os.makedirs(ai_models_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    X = np.random.randn(100, 54)  # 54个特征
    y = np.random.randint(0, 4, 100)  # 4个类别
    
    print("📊 训练测试模型...")
    
    # 数据预处理
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 训练分类器
    classifier = RandomForestClassifier(
        n_estimators=30,
        max_depth=6,
        random_state=42
    )
    classifier.fit(X_scaled, y)
    
    # 计算训练准确率
    train_accuracy = classifier.score(X_scaled, y)
    print(f"✅ 训练准确率: {train_accuracy:.2%}")
    
    # 创建完整模型包
    test_model = {
        'classifier_model': classifier,
        'scaler': scaler,
        'feature_importance': dict(zip([f'特征_{i+1}' for i in range(54)], 
                                     classifier.feature_importances_)),
        'metadata': {
            'name': '对话框测试模型',
            'version': '1.0',
            'accuracy': train_accuracy,
            'features': 54,
            'classes': ['I类桩', 'II类桩', 'III类桩', 'IV类桩'],
            'description': '用于测试修复后的模型加载对话框'
        }
    }
    
    # 保存到ai_models目录
    model_path = os.path.join(ai_models_dir, "dialog_test_model.pkl")
    with open(model_path, 'wb') as f:
        pickle.dump(test_model, f)
    
    print(f"✅ 测试模型已保存到: {model_path}")
    print(f"📁 文件大小: {os.path.getsize(model_path) / 1024:.1f} KB")
    
    return model_path

def test_dialog_programmatically():
    """程序化测试对话框功能"""
    print("\n🧪 程序化测试对话框功能")
    print("=" * 60)
    
    try:
        # 创建测试模型
        model_path = create_test_model()
        
        # 测试模型管理器的加载功能
        from model_manager import get_model_manager
        
        model_manager = get_model_manager()
        
        print(f"\n📥 测试模型加载:")
        success = model_manager.load_external_model(model_path, "对话框测试模型")
        
        if success:
            print(f"✅ 模型加载成功")
            
            # 获取模型信息
            models = model_manager.get_available_models()
            for key, model_info in models.items():
                if "对话框测试" in model_info.name:
                    print(f"📊 模型信息:")
                    print(f"  - 名称: {model_info.name}")
                    print(f"  - 类型: {model_info.model_type}")
                    print(f"  - 特征数: {model_info.feature_count}")
                    print(f"  - 预期准确率: {model_info.accuracy:.1%}")
                    break
        else:
            print(f"❌ 模型加载失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_dialog_test_instructions():
    """显示对话框测试说明"""
    print(f"\n🖥️ 对话框测试说明")
    print("=" * 60)
    
    print("📋 修复内容:")
    print("✅ 增加窗口大小: 500x400 → 600x500")
    print("✅ 允许窗口调整大小")
    print("✅ 按钮固定在底部显示")
    print("✅ 添加分隔线和提示文本")
    print("✅ 设置按钮宽度确保可见性")
    print("✅ 强制界面更新确保按钮显示")
    
    print(f"\n📋 GUI测试步骤:")
    print("1. 启动GUI: python Pile_analyze_GZ_gui.py")
    print("2. 选择 '🚀 AI System V2.0'")
    print("3. 点击 '📥 加载模型' 按钮")
    print("4. 文件对话框会自动打开到 'ai_models' 目录")
    print("5. 选择测试模型: dialog_test_model.pkl")
    print("6. 在加载对话框中检查:")
    print("   - 窗口大小是否合适 (600x500)")
    print("   - 是否能看到底部的按钮")
    print("   - '✅ 确定加载' 按钮是否清晰可见")
    print("   - '❌ 取消' 按钮是否清晰可见")
    print("   - 是否有提示文本")
    print("7. 点击 '✅ 确定加载' 按钮")
    print("8. 验证模型是否成功加载")
    
    print(f"\n🔧 关键修复点:")
    print("- 窗口大小增加确保内容完整显示")
    print("- 按钮使用 side='bottom' 固定在底部")
    print("- 添加分隔线区分内容和按钮区域")
    print("- 设置按钮宽度 width=15 确保可见")
    print("- 添加提示文本指导用户操作")
    print("- 使用 update_idletasks() 强制界面更新")
    
    print(f"\n💡 测试要点:")
    print("- 确认按钮完全可见且可点击")
    print("- 验证窗口可以调整大小")
    print("- 测试键盘快捷键 (回车键)")
    print("- 检查按钮响应和功能")

def test_gui_import():
    """测试GUI模块导入"""
    print(f"\n🔍 测试GUI模块导入")
    print("=" * 60)
    
    try:
        # 强制清除模块缓存
        modules_to_clear = [k for k in sys.modules.keys() if 'Pile_analyze_GZ_gui' in k]
        for module in modules_to_clear:
            del sys.modules[module]
        
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        print("✅ GUI模块导入成功")
        
        # 检查关键方法
        required_methods = [
            'load_external_model_v2',
            '_show_model_loading_dialog',
            '_analyze_and_preview_model'
        ]
        
        print("检查关键方法:")
        for method in required_methods:
            if hasattr(PileAnalyzerGZGUI, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """清理测试文件"""
    print(f"\n🧹 清理测试文件")
    print("=" * 60)
    
    try:
        test_file = "ai_models/dialog_test_model.pkl"
        
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"✅ 已删除: {test_file}")
        
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

def main():
    """主函数"""
    print("🔬 测试修复后的模型加载对话框")
    print("=" * 80)
    
    try:
        # 1. 测试GUI模块导入
        gui_success = test_gui_import()
        
        # 2. 程序化测试
        if gui_success:
            prog_success = test_dialog_programmatically()
        else:
            prog_success = False
        
        # 3. 显示测试说明
        show_dialog_test_instructions()
        
        # 总结
        print(f"\n📋 测试总结")
        print("=" * 60)
        
        print(f"GUI模块导入: {'✅ 成功' if gui_success else '❌ 失败'}")
        print(f"程序化测试: {'✅ 成功' if prog_success else '❌ 失败'}")
        
        if gui_success and prog_success:
            print(f"\n🎉 对话框修复测试成功!")
            print(f"现在可以启动GUI测试实际的对话框显示效果")
            
            # 询问是否保留测试文件
            print(f"\n❓ 是否保留测试模型文件供GUI测试？")
            print("测试模型: ai_models/dialog_test_model.pkl")
            print("输入 'n' 清理，其他键保留文件")
            
            try:
                choice = input("请选择: ").strip().lower()
                if choice == 'n':
                    cleanup_test_files()
                else:
                    print("📁 测试文件已保留，您可以在GUI中使用")
                    print("文件位置: ai_models/dialog_test_model.pkl")
            except:
                print("📁 测试文件已保留")
                
        else:
            print(f"\n❌ 测试存在问题，请检查代码实现")
        
        print(f"\n🎯 修复要点总结:")
        print("✅ 窗口大小: 600x500 (原来 500x400)")
        print("✅ 可调整大小: 允许用户调整窗口")
        print("✅ 按钮布局: 固定在底部确保可见")
        print("✅ 视觉改进: 分隔线和提示文本")
        print("✅ 强制更新: 确保界面正确显示")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
