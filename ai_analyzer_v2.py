#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI分析器 V2.0 - 支持多种特征提取方法和模型版本管理
AI Analyzer V2.0 - Support Multiple Feature Extraction Methods and Model Version Management
"""

import numpy as np
import pandas as pd
import time
from typing import Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from enhanced_feature_extractor import get_feature_manager
from model_manager import get_model_manager

class AIAnalyzerV2:
    """AI分析器 V2.0"""

    def __init__(self):
        self.feature_manager = get_feature_manager()
        self.model_manager = get_model_manager()

        # 分析配置
        self.config = {
            'auto_select_extractor': True,  # 自动选择特征提取器
            'enable_timing': True,          # 启用性能计时
            'cache_features': True,         # 缓存特征
            'verbose': True                 # 详细输出
        }

        # 缓存
        self.feature_cache = {}

        print("🚀 AI分析器 V2.0 初始化完成")
        self._print_system_status()

    def _print_system_status(self):
        """打印系统状态"""
        if self.config['verbose']:
            # 特征提取器状态
            extractors = self.feature_manager.get_available_extractors()
            current_extractor = self.feature_manager.get_current_extractor()

            print(f"📊 可用特征提取器: {len(extractors)} 个")
            for key, info in extractors.items():
                status = "✅ 当前" if current_extractor.name == info['name'] else "⚪"
                print(f"  {status} {info['name']}: {info['feature_count']} 特征")

            # 模型状态
            models = self.model_manager.get_available_models()
            current_model = self.model_manager.get_current_model_info()

            print(f"🤖 可用模型: {len(models)} 个")
            for key, info in models.items():
                status = "✅ 当前" if current_model and current_model.name == info.name else "⚪"
                load_status = "已加载" if info.is_loaded else "未加载"
                print(f"  {status} {info.name}: {info.accuracy:.1%} 准确率, {load_status}")

    def set_feature_extractor(self, extractor_key: str):
        """设置特征提取器"""
        try:
            self.feature_manager.set_current_extractor(extractor_key)

            # 如果启用自动选择，同时切换匹配的模型
            if self.config['auto_select_extractor']:
                self._auto_select_model(extractor_key)

            return True
        except Exception as e:
            print(f"❌ 设置特征提取器失败: {e}")
            return False

    def set_model(self, model_key: str):
        """设置模型"""
        try:
            self.model_manager.set_current_model(model_key)

            # 如果启用自动选择，同时切换匹配的特征提取器
            if self.config['auto_select_extractor']:
                self._auto_select_extractor(model_key)

            return True
        except Exception as e:
            print(f"❌ 设置模型失败: {e}")
            return False

    def _auto_select_model(self, extractor_key: str):
        """根据特征提取器自动选择模型"""
        extractor_info = self.feature_manager.get_available_extractors()[extractor_key]
        feature_count = extractor_info['feature_count']

        # 根据特征数量选择合适的模型
        models = self.model_manager.get_available_models()

        for key, model_info in models.items():
            if model_info.feature_count == feature_count:
                try:
                    self.model_manager.set_current_model(key)
                    print(f"🔄 自动切换到匹配模型: {model_info.name}")
                    break
                except:
                    continue

    def _auto_select_extractor(self, model_key: str):
        """根据模型自动选择特征提取器"""
        model_info = self.model_manager.get_model_info(model_key)
        if not model_info:
            return

        feature_count = model_info.feature_count

        # 根据特征数量选择合适的特征提取器
        extractors = self.feature_manager.get_available_extractors()

        for key, extractor_info in extractors.items():
            if extractor_info['feature_count'] == feature_count:
                try:
                    self.feature_manager.set_current_extractor(key)
                    print(f"🔄 自动切换到匹配特征提取器: {extractor_info['name']}")
                    break
                except:
                    continue

    def extract_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, list]:
        """提取特征"""
        start_time = time.time() if self.config['enable_timing'] else None

        try:
            # 生成缓存键
            cache_key = None
            if self.config['cache_features']:
                extractor_name = self.feature_manager.get_current_extractor().name
                data_hash = hash(str(df.values.tobytes()))
                cache_key = f"{extractor_name}_{data_hash}"

                if cache_key in self.feature_cache:
                    if self.config['verbose']:
                        print("📋 使用缓存特征")
                    return self.feature_cache[cache_key]

            # 提取特征
            features, feature_names = self.feature_manager.extract_features(df)

            # 缓存特征
            if self.config['cache_features'] and cache_key:
                self.feature_cache[cache_key] = (features, feature_names)

            # 性能计时
            if self.config['enable_timing'] and start_time:
                elapsed = time.time() - start_time
                if self.config['verbose']:
                    print(f"⏱️ 特征提取耗时: {elapsed:.3f}秒")

            return features, feature_names

        except Exception as e:
            print(f"❌ 特征提取失败: {e}")
            return np.array([]), []

    def predict(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """进行预测"""
        start_time = time.time() if self.config['enable_timing'] else None

        try:
            # 检查当前模型
            current_model = self.model_manager.get_current_model()
            current_model_info = self.model_manager.get_current_model_info()

            if not current_model or not current_model_info:
                print("❌ 没有可用的模型")
                return None

            if self.config['verbose']:
                print(f"🎯 使用模型: {current_model_info.name}")

            # 提取特征
            features, feature_names = self.extract_features(df)

            if features.size == 0:
                print("❌ 特征提取失败")
                return None

            # 确保特征维度匹配
            if len(features) != current_model_info.feature_count:
                print(f"❌ 特征维度不匹配: 期望{current_model_info.feature_count}, 实际{len(features)}")
                return None

            # 根据模型类型进行预测
            if current_model_info.model_type == 'optimized':
                result = self._predict_optimized(features, current_model, current_model_info)
            else:
                result = self._predict_standard(features, current_model, current_model_info)

            # 性能计时
            if self.config['enable_timing'] and start_time:
                elapsed = time.time() - start_time
                result['prediction_time'] = elapsed
                if self.config['verbose']:
                    print(f"⏱️ 预测耗时: {elapsed:.3f}秒")

            return result

        except Exception as e:
            print(f"❌ 预测失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _predict_optimized(self, features: np.ndarray, model: Dict, model_info) -> Dict[str, Any]:
        """使用优化模型进行预测"""
        # 确保特征形状正确
        if features.ndim == 1:
            features = features.reshape(1, -1)

        # 预处理特征
        preprocessor = model.get('preprocessor')
        if preprocessor:
            features_processed = preprocessor.transform(features)
        else:
            features_processed = features

        # 预测
        classifier = model['classifier_model']
        prediction = classifier.predict(features_processed)[0]
        probabilities = classifier.predict_proba(features_processed)[0]

        # 类别映射
        class_names = [0, 1, 2, 3]
        confidence = max(probabilities)

        # 创建类别概率字典
        class_probabilities = dict(zip(class_names, probabilities))

        # 生成推理
        reasoning = self._generate_reasoning(prediction, confidence, model_info, class_probabilities)

        return {
            '完整性类别': prediction,
            'ai_confidence': confidence,
            'anomaly_score': -0.05,  # 优化模型默认值
            'is_anomaly': False,
            'class_probabilities': class_probabilities,
            'model_info': model_info.to_dict(),
            'overall_reasoning': reasoning
        }

    def _predict_standard(self, features: np.ndarray, model: Dict, model_info) -> Dict[str, Any]:
        """使用标准模型进行预测"""
        # 支持多种模型键名格式
        classifier = model.get('classifier_model') or model.get('model')

        if not classifier:
            # 详细错误信息
            available_keys = list(model.keys())
            raise ValueError(f"No classifier found in standard model. Available keys: {available_keys}")

        # 确保特征形状正确
        if features.ndim == 1:
            features = features.reshape(1, -1)

        # 应用特征选择器（如果存在）
        if 'feature_selector' in model:
            feature_selector = model['feature_selector']
            if hasattr(feature_selector, 'transform'):
                print(f"🔧 应用特征选择器: {features.shape[1]} -> ", end="")
                features = feature_selector.transform(features)
                print(f"{features.shape[1]} 特征")

        # 应用数据标准化（如果存在）
        if 'scaler' in model:
            scaler = model['scaler']
            if hasattr(scaler, 'transform'):
                print(f"⚖️ 应用数据标准化")
                features = scaler.transform(features)

        # 预测
        prediction = classifier.predict(features)[0]
        probabilities = classifier.predict_proba(features)[0]

        confidence = max(probabilities)
        class_names = getattr(classifier, 'classes_', [0, 1, 2, 3])

        # 创建类别概率字典
        class_probabilities = dict(zip(class_names, probabilities))

        # 生成推理
        reasoning = self._generate_reasoning(prediction, confidence, model_info, class_probabilities)

        return {
            '完整性类别': prediction,
            'ai_confidence': confidence,
            'anomaly_score': -0.05,  # 默认值
            'is_anomaly': False,
            'class_probabilities': class_probabilities,
            'model_info': model_info.to_dict(),
            'overall_reasoning': reasoning
        }

    def _generate_reasoning(self, prediction, confidence, model_info, class_probabilities):
        """生成推理文本"""
        # 转换数字类别为中文名称
        category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}

        # 处理numpy数据类型
        import numpy as np
        if isinstance(prediction, (int, float, np.integer, np.floating)):
            prediction_name = category_mapping.get(int(prediction), f'未知类别({prediction})')
        else:
            prediction_name = prediction

        reasoning = f"AI分析结果：{prediction_name}\n\n"
        reasoning += f"模型信息：\n"
        reasoning += f"- 模型名称：{model_info.name}\n"
        reasoning += f"- 模型准确率：{model_info.accuracy:.1%}\n"
        reasoning += f"- 特征数量：{model_info.feature_count}\n\n"

        reasoning += f"置信度分析：\n"
        reasoning += f"- 预测置信度：{confidence:.2%}\n"

        if confidence > 0.8:
            reasoning += "- 置信度很高，预测结果非常可信\n"
        elif confidence > 0.6:
            reasoning += "- 置信度较高，预测结果可信\n"
        elif confidence > 0.4:
            reasoning += "- 置信度中等，建议结合传统方法\n"
        else:
            reasoning += "- 置信度较低，建议以传统方法为准\n"

        reasoning += f"\n各类别概率分布：\n"
        # 转换数字类别名称为中文名称
        for class_key, prob in sorted(class_probabilities.items(), key=lambda x: x[1], reverse=True):
            if isinstance(class_key, (int, float, np.integer, np.floating)):
                class_name = category_mapping.get(int(class_key), f'类别{class_key}')
            else:
                class_name = class_key
            reasoning += f"- {class_name}：{prob:.2%}\n"

        return reasoning

    def compare_models(self, df: pd.DataFrame, model_keys: list) -> Dict[str, Any]:
        """对比多个模型的预测结果"""
        results = {}

        # 保存当前模型
        original_model = self.model_manager.current_model_key

        try:
            for model_key in model_keys:
                if model_key in self.model_manager.get_available_models():
                    self.set_model(model_key)
                    result = self.predict(df)
                    if result:
                        results[model_key] = result

            return results

        finally:
            # 恢复原始模型
            if original_model:
                self.set_model(original_model)

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            'feature_extractors': self.feature_manager.get_available_extractors(),
            'current_extractor': self.feature_manager.get_current_extractor().get_info(),
            'models': {key: info.to_dict() for key, info in self.model_manager.get_available_models().items()},
            'current_model': self.model_manager.get_current_model_info().to_dict() if self.model_manager.get_current_model_info() else None,
            'model_statistics': self.model_manager.get_model_statistics(),
            'config': self.config
        }

# 全局AI分析器实例
ai_analyzer_v2 = AIAnalyzerV2()

def get_ai_analyzer_v2():
    """获取全局AI分析器V2实例"""
    return ai_analyzer_v2
