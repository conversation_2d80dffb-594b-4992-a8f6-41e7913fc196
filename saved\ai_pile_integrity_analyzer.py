import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import traceback
from collections import defaultdict
import os
import pickle
import joblib
from sklearn.ensemble import IsolationForest, RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

class AIPileIntegrityAnalyzer:
    def __init__(self, master=None):
        self.root = tk.Tk() if master is None else tk.Toplevel(master)
        self.root.title('AI桩基完整性分析系统')
        self.root.geometry('1200x800')

        # 配置参数 - 与原始系统保持一致，便于比较
        self.config = {
            '正常': {'speed': (90.0, 1000.0), 'amp': (-100, 3)},
            '轻微畸变': {'speed': (80.0, 90.0), 'amp': (3, 6)},
            '明显畸变': {'speed': (70.0, 80.0), 'amp': (6, 12)},
            '严重畸变': {'speed': (0.0, 70.0), 'amp': (12, 100)},
            'continuous_threshold': 0.5  # 异常点连续长度阈值，单位m
        }

        # AI模型相关
        self.models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ai_models')
        os.makedirs(self.models_dir, exist_ok=True)

        self.classifier_model = None
        self.anomaly_detector = None
        self.scaler = None
        self.feature_importance = None
        self.uncertainty_threshold = 0.2  # 不确定性阈值
        self.training_data = []

        # 状态栏变量
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")

        # 创建菜单
        self.create_menu()

        # 创建主界面
        self.create_main_interface()

        # 数据和分析结果
        self.df = None
        self.analysis_result_cache = None
        self.traditional_result = None
        self.ai_result = None
        self.comparison_result = None

        # 加载模型
        self.load_models()

    def create_menu(self):
        self.menu_bar = tk.Menu(self.root)

        # 文件菜单
        self.file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.file_menu.add_command(label='加载数据', command=self.load_data)
        self.file_menu.add_command(label='保存AI模型', command=self.save_models)
        self.file_menu.add_separator()
        self.file_menu.add_command(label='退出', command=self.root.quit)
        self.menu_bar.add_cascade(label='文件', menu=self.file_menu)

        # 分析菜单
        self.analysis_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.analysis_menu.add_command(label='传统分析', command=self.traditional_analysis)
        self.analysis_menu.add_command(label='AI分析', command=self.ai_analysis)
        self.analysis_menu.add_command(label='比较分析结果', command=self.compare_results)
        self.menu_bar.add_cascade(label='分析', menu=self.analysis_menu)

        # 可视化菜单
        self.visual_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.visual_menu.add_command(label='相对波速三维可视化', command=lambda: self.plotly_visualization('speed'), state='disabled')
        self.visual_menu.add_command(label='波幅三维可视化', command=lambda: self.plotly_visualization('amplitude'), state='disabled')
        self.visual_menu.add_command(label='AI特征重要性', command=self.show_feature_importance, state='disabled')
        self.visual_menu.add_command(label='异常检测可视化', command=self.show_anomaly_detection, state='disabled')
        self.menu_bar.add_cascade(label='可视化', menu=self.visual_menu)

        # AI训练菜单
        self.ai_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.ai_menu.add_command(label='训练AI模型', command=self.train_ai_model)
        self.ai_menu.add_command(label='调整AI参数', command=self.adjust_ai_parameters)
        self.menu_bar.add_cascade(label='AI训练', menu=self.ai_menu)

        # 报告菜单
        self.report_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.report_menu.add_command(label='生成传统报告', command=lambda: self.generate_report('traditional'))
        self.report_menu.add_command(label='生成AI增强报告', command=lambda: self.generate_report('ai'))
        self.menu_bar.add_cascade(label='报告', menu=self.report_menu)

        # 配置菜单
        self.config_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.config_menu.add_command(label='参数设置', command=self.show_settings)
        self.menu_bar.add_cascade(label='配置', menu=self.config_menu)

        self.root.config(menu=self.menu_bar)

    def create_main_interface(self):
        # 创建标签页控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 传统分析标签页
        self.traditional_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.traditional_frame, text='传统分析')

        self.traditional_text = tk.Text(self.traditional_frame, height=25, width=100)
        self.traditional_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # AI分析标签页
        self.ai_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.ai_frame, text='AI分析')

        self.ai_text = tk.Text(self.ai_frame, height=25, width=100)
        self.ai_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # 比较分析标签页
        self.comparison_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.comparison_frame, text='比较分析')

        self.comparison_text = tk.Text(self.comparison_frame, height=25, width=100)
        self.comparison_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def load_data(self):
        file_path = filedialog.askopenfilename(title='选择检测数据文件',
                                             filetypes=[('Text files', '*.txt')])
        if not file_path:
            return

        try:
            # 使用与原始系统相同的数据加载逻辑
            self.df = self.parse_data_file(file_path)

            if self.df is None or self.df.empty:
                messagebox.showerror('数据错误', '有效数据为空或所有数据均不符合规范。请检查文件内容、格式及表头。')
                return

            messagebox.showinfo('数据加载', f'成功加载并清洗 {len(self.df)} 条有效数据')

            # 启用可视化菜单项
            self.visual_menu.entryconfig('相对波速三维可视化', state='normal')
            self.visual_menu.entryconfig('波幅三维可视化', state='normal')

            # 自动执行传统分析和AI分析
            self.traditional_analysis()
            self.ai_analysis()

        except Exception as e:
            messagebox.showerror('数据加载错误', f'加载数据时出错: {str(e)}\n{traceback.format_exc()}')

    def parse_data_file(self, file_path):
        """解析数据文件，与原始系统保持兼容"""
        try:
            # 尝试使用制表符分隔
            df = pd.read_csv(file_path, sep='\t', header=0)

            # 检查列名并重命名
            if 'Depth(m)' in df.columns:
                df.rename(columns={
                    'Depth(m)': 'Depth',
                    '1-2 Speed%': 'S1',
                    '1-2 Amp%': 'A1',
                    '1-3 Speed%': 'S2',
                    '1-3 Amp%': 'A2',
                    '2-3 Speed%': 'S3',
                    '2-3 Amp%': 'A3'
                }, inplace=True)

            # 确保所有数值列都是数值类型
            for col in ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 删除缺失值
            df.dropna(inplace=True)

            return df

        except Exception as e:
            # 如果制表符分隔失败，尝试空格分隔
            try:
                df = pd.read_csv(file_path, delim_whitespace=True, header=0)

                # 检查列名并重命名
                if len(df.columns) >= 7:
                    df.columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']

                # 确保所有数值列都是数值类型
                for col in ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                # 删除缺失值
                df.dropna(inplace=True)

                return df

            except Exception as e2:
                messagebox.showerror('文件解析错误', f'无法解析文件。\n尝试制表符分隔失败: {str(e)}\n尝试空格分隔失败: {str(e2)}')
                return None

    def traditional_analysis(self):
        """使用传统方法进行桩身完整性分析"""
        if self.df is None or self.df.empty:
            messagebox.showwarning("数据错误", "请先加载有效数据")
            return

        try:
            self.status_var.set("正在进行传统分析...")
            self.root.update()

            # 执行传统分析
            self.traditional_result = self.analyze_traditional()

            # 显示分析结果
            self.display_traditional_results()

            self.status_var.set("传统分析完成")
        except Exception as e:
            messagebox.showerror('分析错误', f'传统分析过程出错: {str(e)}\n{traceback.format_exc()}')
            self.traditional_result = None
            self.traditional_text.delete(1.0, tk.END)
            self.traditional_text.insert(tk.END, f"分析数据时发生错误: {e}\n请检查数据和参数设置。")

    def ai_analysis(self):
        """使用AI方法进行桩身完整性分析"""
        if self.df is None or self.df.empty:
            messagebox.showwarning("数据错误", "请先加载有效数据")
            return

        try:
            self.status_var.set("正在进行AI分析...")
            self.root.update()

            # 如果模型未加载，尝试训练新模型
            if self.classifier_model is None or self.anomaly_detector is None:
                self.train_ai_model()

            # 执行AI分析
            self.ai_result = self.analyze_ai()

            # 显示分析结果
            self.display_ai_results()

            # 启用AI特征可视化
            self.visual_menu.entryconfig('AI特征重要性', state='normal')
            self.visual_menu.entryconfig('异常检测可视化', state='normal')

            self.status_var.set("AI分析完成")
        except Exception as e:
            messagebox.showerror('分析错误', f'AI分析过程出错: {str(e)}\n{traceback.format_exc()}')
            self.ai_result = None
            self.ai_text.delete(1.0, tk.END)
            self.ai_text.insert(tk.END, f"AI分析数据时发生错误: {e}\n请检查数据和模型设置。")

    def compare_results(self):
        """比较传统分析和AI分析的结果"""
        if self.traditional_result is None or self.ai_result is None:
            messagebox.showwarning("比较错误", "请先完成传统分析和AI分析")
            return

        try:
            self.status_var.set("正在比较分析结果...")
            self.root.update()

            # 执行比较分析
            self.comparison_result = self.compare_analysis_results()

            # 显示比较结果
            self.display_comparison_results()

            self.status_var.set("比较分析完成")
        except Exception as e:
            messagebox.showerror('比较错误', f'比较分析过程出错: {str(e)}\n{traceback.format_exc()}')
            self.comparison_result = None
            self.comparison_text.delete(1.0, tk.END)
            self.comparison_text.insert(tk.END, f"比较分析时发生错误: {e}\n请检查分析结果。")

    def extract_features(self, df):
        """从数据中提取特征"""
        features = []

        # 全局统计特征
        # 声速统计特征
        s1_mean = np.mean(df['S1'].values)
        s2_mean = np.mean(df['S2'].values)
        s3_mean = np.mean(df['S3'].values)
        s1_std = np.std(df['S1'].values)
        s2_std = np.std(df['S2'].values)
        s3_std = np.std(df['S3'].values)
        s1_max = np.max(df['S1'].values)
        s2_max = np.max(df['S2'].values)
        s3_max = np.max(df['S3'].values)
        s1_min = np.min(df['S1'].values)
        s2_min = np.min(df['S2'].values)
        s3_min = np.min(df['S3'].values)

        # 波幅统计特征
        a1_mean = np.mean(df['A1'].values)
        a2_mean = np.mean(df['A2'].values)
        a3_mean = np.mean(df['A3'].values)
        a1_std = np.std(df['A1'].values)
        a2_std = np.std(df['A2'].values)
        a3_std = np.std(df['A3'].values)
        a1_max = np.max(df['A1'].values)
        a2_max = np.max(df['A2'].values)
        a3_max = np.max(df['A3'].values)
        a1_min = np.min(df['A1'].values)
        a2_min = np.min(df['A2'].values)
        a3_min = np.min(df['A3'].values)

        # 添加基本统计特征
        features.extend([
            s1_mean, s2_mean, s3_mean,
            s1_std, s2_std, s3_std,
            s1_max, s2_max, s3_max,
            s1_min, s2_min, s3_min,
            a1_mean, a2_mean, a3_mean,
            a1_std, a2_std, a3_std,
            a1_max, a2_max, a3_max,
            a1_min, a2_min, a3_min
        ])

        # 导数特征：声速和波幅的变化率
        for i in range(1, 4):
            s_col, a_col = f'S{i}', f'A{i}'

            # 声速变化率
            s_diff = np.diff(df[s_col].values)
            if len(s_diff) > 0:
                features.extend([
                    np.mean(np.abs(s_diff)),  # 声速平均变化率
                    np.max(np.abs(s_diff)),   # 声速最大变化率
                ])
            else:
                features.extend([0, 0])

            # 波幅变化率
            a_diff = np.diff(df[a_col].values)
            if len(a_diff) > 0:
                features.extend([
                    np.mean(np.abs(a_diff)),  # 波幅平均变化率
                    np.max(np.abs(a_diff)),   # 波幅最大变化率
                ])
            else:
                features.extend([0, 0])

        # 横向特征：不同剖面之间的差异
        # 声速横向差异
        s_diff_12 = np.abs(df['S1'].values - df['S2'].values)
        s_diff_13 = np.abs(df['S1'].values - df['S3'].values)
        s_diff_23 = np.abs(df['S2'].values - df['S3'].values)

        features.extend([
            np.mean(s_diff_12),  # 剖面1-2声速平均差异
            np.mean(s_diff_13),  # 剖面1-3声速平均差异
            np.mean(s_diff_23),  # 剖面2-3声速平均差异
            np.max(s_diff_12),   # 剖面1-2声速最大差异
            np.max(s_diff_13),   # 剖面1-3声速最大差异
            np.max(s_diff_23),   # 剖面2-3声速最大差异
        ])

        # 波幅横向差异
        a_diff_12 = np.abs(df['A1'].values - df['A2'].values)
        a_diff_13 = np.abs(df['A1'].values - df['A3'].values)
        a_diff_23 = np.abs(df['A2'].values - df['A3'].values)

        features.extend([
            np.mean(a_diff_12),  # 剖面1-2波幅平均差异
            np.mean(a_diff_13),  # 剖面1-3波幅平均差异
            np.mean(a_diff_23),  # 剖面2-3波幅平均差异
            np.max(a_diff_12),   # 剖面1-2波幅最大差异
            np.max(a_diff_13),   # 剖面1-3波幅最大差异
            np.max(a_diff_23),   # 剖面2-3波幅最大差异
        ])

        # 异常点比例特征
        abnormal_speed_ratio = np.sum((df['S1'].values < 90) | (df['S2'].values < 90) | (df['S3'].values < 90)) / len(df)
        severe_abnormal_speed_ratio = np.sum((df['S1'].values < 70) | (df['S2'].values < 70) | (df['S3'].values < 70)) / len(df)
        abnormal_amp_ratio = np.sum((df['A1'].values > 3) | (df['A2'].values > 3) | (df['A3'].values > 3)) / len(df)
        severe_abnormal_amp_ratio = np.sum((df['A1'].values > 12) | (df['A2'].values > 12) | (df['A3'].values > 12)) / len(df)

        features.extend([
            abnormal_speed_ratio,
            severe_abnormal_speed_ratio,
            abnormal_amp_ratio,
            severe_abnormal_amp_ratio
        ])

        # 深度相关特征
        depth_range = np.max(df['Depth'].values) - np.min(df['Depth'].values)
        depth_mean = np.mean(df['Depth'].values)

        features.extend([
            depth_range,
            depth_mean
        ])

        return np.array(features).reshape(1, -1)

    def classify_anomaly(self, speed, amp):
        """使用传统方法对单个点进行异常分类"""
        if not (isinstance(speed, (int, float)) and isinstance(amp, (int, float))):
            return '数据无效'

        s_map = {'正常': 0, '轻微畸变': 1, '明显畸变': 2, '严重畸变': 3}
        o_types = ['严重畸变', '明显畸变', '轻微畸变', '正常']
        s_cat, a_cat = '正常', '正常'

        for lvl in o_types:
            if lvl in self.config and 'speed' in self.config[lvl]:
                s_min, s_max = self.config[lvl]['speed']
                if s_min <= speed < s_max:
                    s_cat = lvl
                    break

        for lvl in o_types:
            if lvl in self.config and 'amp' in self.config[lvl]:
                a_min, a_max = self.config[lvl]['amp']
                if a_min <= amp < a_max:
                    a_cat = lvl
                    break

        return s_cat if s_map.get(s_cat, -1) > s_map.get(a_cat, -1) else a_cat

    def analyze_traditional(self):
        """使用传统方法进行桩身完整性分析"""
        if self.df is None or self.df.empty:
            return {
                '完整性类别': 'N/A (无数据)', '异常区域': {},
                '最大异常比例': {'type': '', 'ratio': 0, 'depth_m': None},
                'depth_type_specific_metrics': {},
                'per_depth_classification_details': {},
                'overall_reasoning_intro': '未加载数据。',
                'detailed_overall_reason': '请先加载数据文件进行分析。'
            }

        POINT_SPACING = 0.1
        MAX_CONTINUOUS_DEPTH_STEP = POINT_SPACING * 1.25
        CONTINUOUS_LENGTH_THRESHOLD = self.config['continuous_threshold']

        raw_anomalies_by_depth = defaultdict(list)
        profile_data_for_continuity = {p:[] for p in range(1,4)}

        # 对每个点进行异常分类
        for _, row in self.df.iterrows():
            depth = float(row['Depth'])
            for i in range(1,4):
                s_col, a_col = f'S{i}', f'A{i}'
                if s_col not in row or a_col not in row:
                    continue

                s_val, a_val = row[s_col], row[a_col]
                if pd.isna(s_val) or pd.isna(a_val):
                    continue

                s = float(s_val)
                a = float(a_val)

                atype = self.classify_anomaly(s, a)
                if atype not in ['正常', '数据无效']:
                    item = {'剖面': str(i), '类型': atype, '声速': s, '波幅': a, '深度': depth}
                    raw_anomalies_by_depth[depth].append(item)
                    profile_data_for_continuity[i].append({'depth': depth, 'type': atype, 'item_ref': item})

        # 计算连续长度
        for p_idx in range(1, 4):
            anoms_in_prof = sorted(profile_data_for_continuity[p_idx], key=lambda x: x['depth'])
            if not anoms_in_prof:
                continue

            groups, cur_group = [], []
            for ameta in anoms_in_prof:
                if not cur_group or (ameta['type'] == cur_group[-1]['type'] and
                                     (ameta['depth'] - cur_group[-1]['depth']) <= MAX_CONTINUOUS_DEPTH_STEP):
                    cur_group.append(ameta)
                else:
                    groups.append(cur_group)
                    cur_group = [ameta]

            if cur_group:
                groups.append(cur_group)

            for grp in groups:
                length = (len(grp) - 1) * POINT_SPACING if len(grp) > 1 else POINT_SPACING
                for ameta in grp:
                    ameta['item_ref']['连续长度'] = length

        # 计算横向比例
        anomalies_for_report = defaultdict(list)
        depth_type_specific_metrics = {}

        for depth_float, items_at_d in raw_anomalies_by_depth.items():
            type_counts = {'轻微畸变': 0, '明显畸变': 0, '严重畸变': 0}
            for item in items_at_d:
                if item['类型'] in type_counts:
                    type_counts[item['类型']] += 1

            current_depth_metrics_calc = {
                'hr_light': type_counts['轻微畸变'] / 3.0,
                'hr_moderate': type_counts['明显畸变'] / 3.0,
                'hr_severe': type_counts['严重畸变'] / 3.0
            }
            depth_type_specific_metrics[depth_float] = current_depth_metrics_calc

            for item in items_at_d:
                if '连续长度' not in item:
                    item['连续长度'] = POINT_SPACING
                anomalies_for_report[depth_float].append(item)

        # 对每个深度进行分类
        per_depth_classification_details = {}

        for depth_float in sorted(anomalies_for_report.keys()):
            items_at_this_depth = anomalies_for_report[depth_float]
            metrics_at_this_depth = depth_type_specific_metrics.get(depth_float, {})

            current_depth_class = 'I类桩'
            reason_parts = []
            is_classified_at_depth = False

            # 计算各类型最大连续长度
            max_cl_severe, max_cl_moderate, max_cl_light = 0, 0, 0
            for item in items_at_this_depth:
                cl = item.get('连续长度', POINT_SPACING)
                if item['类型'] == '严重畸变':
                    max_cl_severe = max(max_cl_severe, cl)
                elif item['类型'] == '明显畸变':
                    max_cl_moderate = max(max_cl_moderate, cl)
                elif item['类型'] == '轻微畸变':
                    max_cl_light = max(max_cl_light, cl)

            # 判断各类型是否存在
            has_severe_type = any(item['类型'] == '严重畸变' for item in items_at_this_depth)
            has_moderate_type = any(item['类型'] == '明显畸变' for item in items_at_this_depth)
            has_light_type = any(item['类型'] == '轻微畸变' for item in items_at_this_depth)

            # 判断连续长度是否超过阈值
            is_cl_long_severe = max_cl_severe > CONTINUOUS_LENGTH_THRESHOLD
            is_cl_long_moderate = max_cl_moderate > CONTINUOUS_LENGTH_THRESHOLD
            is_cl_long_light = max_cl_light > CONTINUOUS_LENGTH_THRESHOLD

            # 获取横向比例
            hr_severe = metrics_at_this_depth.get('hr_severe', 0.0)
            hr_moderate = metrics_at_this_depth.get('hr_moderate', 0.0)
            hr_light = metrics_at_this_depth.get('hr_light', 0.0)

            # 判断横向比例是否超过50%
            hr_severe_wide = hr_severe > 0.5
            hr_moderate_wide = hr_moderate > 0.5
            hr_light_wide = hr_light > 0.5

            # IV类桩判定
            if has_severe_type and (is_cl_long_severe or hr_severe_wide):
                current_depth_class = 'IV类桩'
                reason_parts.append("存在'严重畸变'类型缺陷")
                if is_cl_long_severe:
                    reason_parts.append(f"其连续长度({max_cl_severe:.2f}m)>阈值({CONTINUOUS_LENGTH_THRESHOLD:.2f}m)")
                if hr_severe_wide:
                    reason_parts.append(f"其横向比例({hr_severe:.0%})>50%")
                is_classified_at_depth = True

            # III类桩判定
            if not is_classified_at_depth:
                if (has_moderate_type and (is_cl_long_moderate or hr_moderate_wide)) or \
                   (has_severe_type and not is_cl_long_severe and not hr_severe_wide):
                    current_depth_class = 'III类桩'
                    if has_moderate_type and (is_cl_long_moderate or hr_moderate_wide):
                        reason_parts.append("存在'明显畸变'类型缺陷")
                        if is_cl_long_moderate:
                            reason_parts.append(f"其连续长度({max_cl_moderate:.2f}m)>阈值({CONTINUOUS_LENGTH_THRESHOLD:.2f}m)")
                        if hr_moderate_wide:
                            reason_parts.append(f"其横向比例({hr_moderate:.0%})>50%")
                    elif has_severe_type:
                        reason_parts.append(f"存在'严重畸变'类型缺陷，但其连续长度({max_cl_severe:.2f}m)≤阈值且横向比例({hr_severe:.0%})≤50%")
                    is_classified_at_depth = True

            # II类桩判定
            if not is_classified_at_depth:
                if (has_light_type and (is_cl_long_light or hr_light_wide)) or \
                   (has_moderate_type and not is_cl_long_moderate and not hr_moderate_wide):
                    current_depth_class = 'II类桩'
                    if has_light_type and (is_cl_long_light or hr_light_wide):
                        reason_parts.append("存在'轻微畸变'类型缺陷")
                        if is_cl_long_light:
                            reason_parts.append(f"其连续长度({max_cl_light:.2f}m)>阈值({CONTINUOUS_LENGTH_THRESHOLD:.2f}m)")
                        if hr_light_wide:
                            reason_parts.append(f"其横向比例({hr_light:.0%})>50%")
                    elif has_moderate_type:
                        reason_parts.append(f"存在'明显畸变'类型缺陷，但其连续长度({max_cl_moderate:.2f}m)≤阈值且横向比例({hr_moderate:.0%})≤50%")
                    is_classified_at_depth = True

            # I类桩判定
            if not is_classified_at_depth:
                if has_light_type and not is_cl_long_light and not hr_light_wide:
                    current_depth_class = 'I类桩'
                    reason_parts.append(f"存在'轻微畸变'类型缺陷，但其连续长度({max_cl_light:.2f}m)≤阈值且横向比例({hr_light:.0%})≤50%")
                    is_classified_at_depth = True
                elif not has_severe_type and not has_moderate_type and not has_light_type:
                    current_depth_class = 'I类桩'
                    reason_parts.append("声速、波幅均处于正常范围")
                    is_classified_at_depth = True

            final_reason_str = "; ".join(reason_parts) if reason_parts else "该深度所有指标均在I类桩范围内或未满足II/III/IV类缺陷标准。"

            per_depth_classification_details[depth_float] = {
                'class': current_depth_class,
                'reason': final_reason_str,
                'max_cl_severe': max_cl_severe,
                'max_cl_moderate': max_cl_moderate,
                'max_cl_light': max_cl_light,
                'hr_severe': hr_severe,
                'hr_moderate': hr_moderate,
                'hr_light': hr_light
            }

        # 确定整体桩类
        class_severity_map = {'I类桩': 1, 'II类桩': 2, 'III类桩': 3, 'IV类桩': 4}
        class_severity_map_inv = {v: k for k, v in class_severity_map.items()}

        overall_pile_class_val = 1
        critical_depth_for_overall_class = None

        for depth, details in per_depth_classification_details.items():
            current_depth_severity_val = class_severity_map.get(details['class'], 1)
            if current_depth_severity_val > overall_pile_class_val:
                overall_pile_class_val = current_depth_severity_val
                critical_depth_for_overall_class = depth

        overall_pile_class_str = class_severity_map_inv.get(overall_pile_class_val, 'I类桩')

        # 生成分析结论
        if critical_depth_for_overall_class is not None:
            crit_details = per_depth_classification_details[critical_depth_for_overall_class]
            overall_reasoning_intro = f"桩体最严重缺陷判定为 {overall_pile_class_str}。"
            detailed_reason = f"主要判定依据源于深度 {critical_depth_for_overall_class:.2f}m (该深度判定为{crit_details['class']}): {crit_details['reason']}."
        else:
            overall_reasoning_intro = f"桩体综合判定为 {overall_pile_class_str}。"
            detailed_reason = "桩体未发现明显缺陷，各项指标均在正常范围内。"

        return {
            '完整性类别': overall_pile_class_str,
            '异常区域': dict(anomalies_for_report),
            'depth_type_specific_metrics': depth_type_specific_metrics,
            'per_depth_classification_details': per_depth_classification_details,
            'overall_reasoning_intro': overall_reasoning_intro,
            'detailed_overall_reason': detailed_reason
        }

    def analyze_ai(self):
        """使用AI方法进行桩身完整性分析"""
        if self.df is None or self.df.empty:
            return {
                '完整性类别': 'N/A (无数据)',
                '异常区域': {},
                'ai_confidence': 0.0,
                'anomaly_score': 0.0,
                'feature_importance': {},
                'overall_reasoning': '未加载数据。请先加载数据文件进行分析。'
            }

        # 提取特征
        features = self.extract_features(self.df)

        # 如果模型未加载，尝试训练新模型
        if self.classifier_model is None or self.anomaly_detector is None:
            self.train_ai_model()

        # 标准化特征
        if self.scaler is not None:
            features_scaled = self.scaler.transform(features)
        else:
            features_scaled = features

        # 异常检测
        anomaly_score = 0.0
        if self.anomaly_detector is not None:
            anomaly_score = -self.anomaly_detector.score_samples(features_scaled)[0]

        # 分类预测
        pile_class = 'I类桩'
        class_probabilities = {
            'I类桩': 0.0,
            'II类桩': 0.0,
            'III类桩': 0.0,
            'IV类桩': 0.0
        }

        if self.classifier_model is not None:
            class_idx = self.classifier_model.predict(features_scaled)[0]
            class_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
            pile_class = class_mapping.get(class_idx, 'I类桩')

            # 获取分类概率
            probabilities = self.classifier_model.predict_proba(features_scaled)[0]
            for i, prob in enumerate(probabilities):
                class_name = class_mapping.get(i, f'未知类别{i}')
                class_probabilities[class_name] = prob

        # 计算置信度
        max_prob = max(class_probabilities.values())
        confidence = max_prob

        # 获取特征重要性
        feature_importance = {}
        if hasattr(self.classifier_model, 'feature_importances_') and self.feature_importance is not None:
            for feature_name, importance in self.feature_importance.items():
                feature_importance[feature_name] = importance

        # 生成分析结论
        traditional_result = self.analyze_traditional()
        traditional_class = traditional_result['完整性类别']

        if pile_class == traditional_class:
            agreement_text = f"AI分析结果与传统分析一致，均为{pile_class}。"
        else:
            agreement_text = f"AI分析结果为{pile_class}，而传统分析结果为{traditional_class}。"

        confidence_text = f"AI模型置信度为{confidence:.2%}。"

        if anomaly_score > 1.0:
            anomaly_text = f"检测到异常模式（异常分数：{anomaly_score:.2f}），建议进一步检查。"
        else:
            anomaly_text = f"未检测到明显异常模式（异常分数：{anomaly_score:.2f}）。"

        overall_reasoning = f"{agreement_text} {confidence_text} {anomaly_text}"

        # 添加不确定性分析
        if confidence < self.uncertainty_threshold:
            overall_reasoning += f" 由于置信度较低（{confidence:.2%} < {self.uncertainty_threshold:.2%}），建议结合传统分析结果进行综合判断。"

        return {
            '完整性类别': pile_class,
            '异常区域': traditional_result['异常区域'],
            'ai_confidence': confidence,
            'anomaly_score': anomaly_score,
            'class_probabilities': class_probabilities,
            'feature_importance': feature_importance,
            'overall_reasoning': overall_reasoning,
            'traditional_result': traditional_result
        }

    def compare_analysis_results(self):
        """比较传统分析和AI分析的结果"""
        if self.traditional_result is None or self.ai_result is None:
            return {
                'comparison': '无法比较，请先完成传统分析和AI分析',
                'agreement': False,
                'confidence': 0.0,
                'recommendations': '请先完成传统分析和AI分析'
            }

        traditional_class = self.traditional_result['完整性类别']
        ai_class = self.ai_result['完整性类别']

        # 判断两种方法是否一致
        agreement = traditional_class == ai_class

        # 获取AI置信度
        confidence = self.ai_result.get('ai_confidence', 0.0)

        # 获取异常分数
        anomaly_score = self.ai_result.get('anomaly_score', 0.0)

        # 生成比较结论
        if agreement:
            comparison = f"传统分析和AI分析结果一致，均为{traditional_class}。"
            if confidence >= 0.9:
                recommendations = f"AI模型具有高置信度（{confidence:.2%}），结果可信。"
            elif confidence >= 0.7:
                recommendations = f"AI模型具有中等置信度（{confidence:.2%}），结果较为可信。"
            else:
                recommendations = f"AI模型置信度较低（{confidence:.2%}），建议以传统分析结果为主。"
        else:
            comparison = f"传统分析结果为{traditional_class}，而AI分析结果为{ai_class}。"

            class_severity = {'I类桩': 1, 'II类桩': 2, 'III类桩': 3, 'IV类桩': 4}
            traditional_severity = class_severity.get(traditional_class, 0)
            ai_severity = class_severity.get(ai_class, 0)

            if ai_severity > traditional_severity:
                if confidence >= 0.8:
                    recommendations = f"AI模型检测到更严重的问题（置信度：{confidence:.2%}），建议进一步检查。"
                else:
                    recommendations = f"AI模型检测到更严重的问题，但置信度不高（{confidence:.2%}），建议以传统分析为主，并关注AI标识的异常区域。"
            else:
                if confidence >= 0.8:
                    recommendations = f"AI模型认为问题不如传统分析严重（置信度：{confidence:.2%}），可能传统方法过于保守。"
                else:
                    recommendations = f"AI模型认为问题不如传统分析严重，但置信度不高（{confidence:.2%}），建议以传统分析为主。"

        # 添加异常检测结果
        if anomaly_score > 1.0:
            anomaly_text = f"AI检测到异常模式（异常分数：{anomaly_score:.2f}），可能存在传统方法未能识别的问题。"
            recommendations += f" {anomaly_text}"

        return {
            'comparison': comparison,
            'agreement': agreement,
            'confidence': confidence,
            'anomaly_score': anomaly_score,
            'recommendations': recommendations,
            'traditional_class': traditional_class,
            'ai_class': ai_class
        }

    def display_traditional_results(self):
        """显示传统分析结果"""
        if self.traditional_result is None:
            self.traditional_text.delete(1.0, tk.END)
            self.traditional_text.insert(tk.END, "没有传统分析结果可显示。\n")
            return

        res = self.traditional_result
        self.traditional_text.delete(1.0, tk.END)

        self.traditional_text.insert(tk.END, f"桩基完整性类别: {res.get('完整性类别','N/A')}\n")
        self.traditional_text.insert(tk.END, f"判定概述: {res.get('overall_reasoning_intro','无概述')}\n")
        self.traditional_text.insert(tk.END, f"详细判定依据: {res.get('detailed_overall_reason','无详细依据')}\n\n")

        self.traditional_text.insert(tk.END, '各深度异常详情:\n')
        if not res.get("异常区域"):
            self.traditional_text.insert(tk.END, '  无异常点\n')
        else:
            sorted_depths_float = sorted(res["异常区域"].keys())
            per_depth_class_details = res.get("per_depth_classification_details", {})

            for depth_key_float in sorted_depths_float:
                items_at_depth = res["异常区域"][depth_key_float]
                self.traditional_text.insert(tk.END, f'深度 {depth_key_float:.2f}m:\n')

                depth_class_info = per_depth_class_details.get(depth_key_float)
                if depth_class_info:
                    self.traditional_text.insert(tk.END, f"  该深度综合判定: {depth_class_info['class']} ({depth_class_info['reason']})\n")

                depth_metrics_for_display = res.get("depth_type_specific_metrics", {})
                metrics_at_this_depth = depth_metrics_for_display.get(depth_key_float, {})

                for item in items_at_depth:
                    cl = f"{item.get('连续长度', 0):.2f}m"
                    s, a = item.get("声速", "N/A"), item.get("波幅", "N/A")
                    s_t, a_t = (f"{v:.1f}" if isinstance(v, (int, float)) else str(v) for v in (s, a))

                    item_type = item["类型"]
                    type_specific_hr = 0.0
                    if item_type == '轻微畸变': type_specific_hr = metrics_at_this_depth.get('hr_light', 0.0)
                    elif item_type == '明显畸变': type_specific_hr = metrics_at_this_depth.get('hr_moderate', 0.0)
                    elif item_type == '严重畸变': type_specific_hr = metrics_at_this_depth.get('hr_severe', 0.0)
                    hr_display = f"{type_specific_hr*100:.0f}%"

                    self.traditional_text.insert(tk.END, f'    剖面{item["剖面"]}: {item_type} (声速:{s_t} 波幅:{a_t}, 连续:{cl}, 同类型横向:{hr_display})\n')
            self.traditional_text.insert(tk.END, "\n")

    def display_ai_results(self):
        """显示AI分析结果"""
        if self.ai_result is None:
            self.ai_text.delete(1.0, tk.END)
            self.ai_text.insert(tk.END, "没有AI分析结果可显示。\n")
            return

        res = self.ai_result
        self.ai_text.delete(1.0, tk.END)

        self.ai_text.insert(tk.END, f"桩基完整性类别: {res.get('完整性类别','N/A')}\n")
        self.ai_text.insert(tk.END, f"AI置信度: {res.get('ai_confidence', 0.0):.2%}\n")
        self.ai_text.insert(tk.END, f"异常分数: {res.get('anomaly_score', 0.0):.2f}\n\n")

        self.ai_text.insert(tk.END, f"AI分析结论: {res.get('overall_reasoning', '无分析结论')}\n\n")

        # 显示类别概率
        self.ai_text.insert(tk.END, "各类别概率:\n")
        class_probabilities = res.get('class_probabilities', {})
        for class_name, prob in class_probabilities.items():
            self.ai_text.insert(tk.END, f"  {class_name}: {prob:.2%}\n")

        self.ai_text.insert(tk.END, "\n特征重要性排名:\n")
        feature_importance = res.get('feature_importance', {})
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        for i, (feature, importance) in enumerate(sorted_features[:10]):  # 只显示前10个重要特征
            self.ai_text.insert(tk.END, f"  {i+1}. {feature}: {importance:.4f}\n")

    def display_comparison_results(self):
        """显示比较分析结果"""
        if self.comparison_result is None:
            self.comparison_text.delete(1.0, tk.END)
            self.comparison_text.insert(tk.END, "没有比较分析结果可显示。\n")
            return

        res = self.comparison_result
        self.comparison_text.delete(1.0, tk.END)

        self.comparison_text.insert(tk.END, f"比较结果: {res.get('comparison', '无比较结果')}\n\n")
        self.comparison_text.insert(tk.END, f"AI置信度: {res.get('confidence', 0.0):.2%}\n")
        self.comparison_text.insert(tk.END, f"异常分数: {res.get('anomaly_score', 0.0):.2f}\n\n")

        self.comparison_text.insert(tk.END, f"建议: {res.get('recommendations', '无建议')}\n\n")

        # 显示一致性分析
        agreement = res.get('agreement', False)
        if agreement:
            self.comparison_text.insert(tk.END, "结果一致性: 两种方法结果一致\n")
        else:
            self.comparison_text.insert(tk.END, "结果一致性: 两种方法结果不一致\n")
            self.comparison_text.insert(tk.END, f"  传统分析结果: {res.get('traditional_class', 'N/A')}\n")
            self.comparison_text.insert(tk.END, f"  AI分析结果: {res.get('ai_class', 'N/A')}\n")

    def train_ai_model(self):
        """训练AI模型"""
        # 如果没有数据，使用示例数据进行训练
        if (self.df is None or self.df.empty) and not self.training_data:
            messagebox.showinfo("训练信息", "当前没有加载数据，将使用内置示例数据进行训练。")
            self.generate_sample_data()
        elif not self.training_data:
            # 如果有当前数据但没有训练数据，也生成一些示例数据增强训练集
            messagebox.showinfo("训练信息", "将使用当前数据和内置示例数据进行训练。")
            self.generate_sample_data()

        try:
            self.status_var.set("正在训练AI模型...")
            self.root.update()

            # 准备训练数据
            X, y = self.prepare_training_data()

            if len(X) < 1:
                messagebox.showwarning("数据不足", "训练数据不足，无法训练模型。请加载至少一个数据文件。")
                return

            # 划分训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # 标准化特征
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)

            # 训练分类模型
            self.classifier_model = RandomForestClassifier(n_estimators=100, random_state=42)
            self.classifier_model.fit(X_train_scaled, y_train)

            # 训练异常检测模型
            self.anomaly_detector = IsolationForest(n_estimators=100, contamination=0.1, random_state=42)
            self.anomaly_detector.fit(X_train_scaled)

            # 评估模型
            y_pred = self.classifier_model.predict(X_test_scaled)
            accuracy = accuracy_score(y_test, y_pred)

            # 获取特征重要性
            if hasattr(self.classifier_model, 'feature_importances_'):
                feature_names = self.get_feature_names()
                importances = self.classifier_model.feature_importances_
                self.feature_importance = dict(zip(feature_names, importances))

            # 保存模型
            self.save_models()

            messagebox.showinfo("训练完成", f"AI模型训练完成，准确率: {accuracy:.2%}")
            self.status_var.set(f"AI模型训练完成，准确率: {accuracy:.2%}")

            # 启用AI特征可视化
            self.visual_menu.entryconfig('AI特征重要性', state='normal')
            self.visual_menu.entryconfig('异常检测可视化', state='normal')

        except Exception as e:
            messagebox.showerror('训练错误', f'训练AI模型时出错: {str(e)}\n{traceback.format_exc()}')
            self.status_var.set("训练AI模型失败")

    def prepare_training_data(self):
        """准备训练数据"""
        X = []
        y = []

        # 如果有历史训练数据，加载它们
        if self.training_data:
            for data_item in self.training_data:
                X.append(data_item['features'])
                y.append(data_item['label'])

        # 如果当前有数据，也加入训练集
        if self.df is not None and not self.df.empty:
            features = self.extract_features(self.df)

            # 使用传统方法分析得到标签
            traditional_result = self.analyze_traditional()
            pile_class = traditional_result['完整性类别']

            # 将类别转换为数值标签
            class_mapping = {'I类桩': 0, 'II类桩': 1, 'III类桩': 2, 'IV类桩': 3}
            label = class_mapping.get(pile_class, 0)

            X.append(features[0])
            y.append(label)

            # 将当前数据添加到训练数据集
            self.training_data.append({
                'features': features[0],
                'label': label,
                'pile_class': pile_class
            })

        return np.array(X), np.array(y)

    def generate_sample_data(self):
        """生成示例数据用于训练"""
        # 生成四种类型的桩基数据
        for pile_class in ['I类桩', 'II类桩', 'III类桩', 'IV类桩']:
            for _ in range(10):  # 每种类型生成10个样本
                # 根据桩类生成特征
                if pile_class == 'I类桩':
                    # 正常桩的特征
                    # 声速统计特征 (12个)
                    s_means = np.random.normal(95, 3, 3)  # 3个剖面的声速均值
                    s_stds = np.random.normal(5, 1, 3)    # 3个剖面的声速标准差
                    s_maxs = np.random.normal(105, 3, 3)  # 3个剖面的声速最大值
                    s_mins = np.random.normal(90, 3, 3)   # 3个剖面的声速最小值

                    # 波幅统计特征 (12个)
                    a_means = np.random.normal(0, 1, 3)   # 3个剖面的波幅均值
                    a_stds = np.random.normal(1, 0.3, 3)  # 3个剖面的波幅标准差
                    a_maxs = np.random.normal(2, 0.5, 3)  # 3个剖面的波幅最大值
                    a_mins = np.random.normal(-1, 0.5, 3) # 3个剖面的波幅最小值

                    # 导数特征 (12个)
                    s_mean_diffs = np.random.normal(2, 0.5, 3)  # 3个剖面的声速平均变化率
                    s_max_diffs = np.random.normal(5, 1, 3)     # 3个剖面的声速最大变化率
                    a_mean_diffs = np.random.normal(0.5, 0.2, 3) # 3个剖面的波幅平均变化率
                    a_max_diffs = np.random.normal(1, 0.3, 3)    # 3个剖面的波幅最大变化率

                    # 横向特征 (12个)
                    s_mean_cross_diffs = np.random.normal(3, 1, 3)  # 剖面间声速平均差异
                    s_max_cross_diffs = np.random.normal(8, 2, 3)   # 剖面间声速最大差异
                    a_mean_cross_diffs = np.random.normal(0.8, 0.2, 3) # 剖面间波幅平均差异
                    a_max_cross_diffs = np.random.normal(2, 0.5, 3)    # 剖面间波幅最大差异

                    # 异常点比例特征 (4个)
                    abnormal_ratios = np.random.normal(0.05, 0.02, 4)  # 异常点比例很低

                    # 深度相关特征 (2个)
                    depth_features = np.random.normal(15, 3, 2)  # 深度范围和平均深度

                elif pile_class == 'II类桩':
                    # 轻微畸变桩的特征
                    # 声速统计特征 (12个)
                    s_means = np.random.normal(85, 3, 3)  # 3个剖面的声速均值
                    s_stds = np.random.normal(7, 1, 3)    # 3个剖面的声速标准差
                    s_maxs = np.random.normal(95, 3, 3)   # 3个剖面的声速最大值
                    s_mins = np.random.normal(80, 3, 3)   # 3个剖面的声速最小值

                    # 波幅统计特征 (12个)
                    a_means = np.random.normal(4, 1, 3)   # 3个剖面的波幅均值
                    a_stds = np.random.normal(1.5, 0.3, 3)  # 3个剖面的波幅标准差
                    a_maxs = np.random.normal(6, 0.5, 3)  # 3个剖面的波幅最大值
                    a_mins = np.random.normal(3, 0.5, 3)  # 3个剖面的波幅最小值

                    # 导数特征 (12个)
                    s_mean_diffs = np.random.normal(3, 0.5, 3)  # 3个剖面的声速平均变化率
                    s_max_diffs = np.random.normal(8, 1, 3)     # 3个剖面的声速最大变化率
                    a_mean_diffs = np.random.normal(1, 0.2, 3)  # 3个剖面的波幅平均变化率
                    a_max_diffs = np.random.normal(2, 0.3, 3)   # 3个剖面的波幅最大变化率

                    # 横向特征 (12个)
                    s_mean_cross_diffs = np.random.normal(5, 1, 3)  # 剖面间声速平均差异
                    s_max_cross_diffs = np.random.normal(10, 2, 3)  # 剖面间声速最大差异
                    a_mean_cross_diffs = np.random.normal(1.5, 0.2, 3) # 剖面间波幅平均差异
                    a_max_cross_diffs = np.random.normal(3, 0.5, 3)    # 剖面间波幅最大差异

                    # 异常点比例特征 (4个)
                    abnormal_ratios = np.random.normal(0.2, 0.05, 4)  # 异常点比例中等

                    # 深度相关特征 (2个)
                    depth_features = np.random.normal(15, 3, 2)  # 深度范围和平均深度

                elif pile_class == 'III类桩':
                    # 明显畸变桩的特征
                    # 声速统计特征 (12个)
                    s_means = np.random.normal(75, 3, 3)  # 3个剖面的声速均值
                    s_stds = np.random.normal(8, 1, 3)    # 3个剖面的声速标准差
                    s_maxs = np.random.normal(85, 3, 3)   # 3个剖面的声速最大值
                    s_mins = np.random.normal(70, 3, 3)   # 3个剖面的声速最小值

                    # 波幅统计特征 (12个)
                    a_means = np.random.normal(8, 1, 3)   # 3个剖面的波幅均值
                    a_stds = np.random.normal(2, 0.3, 3)  # 3个剖面的波幅标准差
                    a_maxs = np.random.normal(12, 0.5, 3) # 3个剖面的波幅最大值
                    a_mins = np.random.normal(6, 0.5, 3)  # 3个剖面的波幅最小值

                    # 导数特征 (12个)
                    s_mean_diffs = np.random.normal(5, 0.5, 3)  # 3个剖面的声速平均变化率
                    s_max_diffs = np.random.normal(12, 1, 3)    # 3个剖面的声速最大变化率
                    a_mean_diffs = np.random.normal(2, 0.2, 3)  # 3个剖面的波幅平均变化率
                    a_max_diffs = np.random.normal(4, 0.3, 3)   # 3个剖面的波幅最大变化率

                    # 横向特征 (12个)
                    s_mean_cross_diffs = np.random.normal(8, 1, 3)  # 剖面间声速平均差异
                    s_max_cross_diffs = np.random.normal(15, 2, 3)  # 剖面间声速最大差异
                    a_mean_cross_diffs = np.random.normal(3, 0.2, 3) # 剖面间波幅平均差异
                    a_max_cross_diffs = np.random.normal(6, 0.5, 3)  # 剖面间波幅最大差异

                    # 异常点比例特征 (4个)
                    abnormal_ratios = np.random.normal(0.4, 0.05, 4)  # 异常点比例较高

                    # 深度相关特征 (2个)
                    depth_features = np.random.normal(15, 3, 2)  # 深度范围和平均深度

                else:  # 'IV类桩'
                    # 严重畸变桩的特征
                    # 声速统计特征 (12个)
                    s_means = np.random.normal(60, 5, 3)  # 3个剖面的声速均值
                    s_stds = np.random.normal(10, 2, 3)   # 3个剖面的声速标准差
                    s_maxs = np.random.normal(70, 5, 3)   # 3个剖面的声速最大值
                    s_mins = np.random.normal(50, 5, 3)   # 3个剖面的声速最小值

                    # 波幅统计特征 (12个)
                    a_means = np.random.normal(15, 2, 3)  # 3个剖面的波幅均值
                    a_stds = np.random.normal(3, 0.5, 3)  # 3个剖面的波幅标准差
                    a_maxs = np.random.normal(20, 2, 3)   # 3个剖面的波幅最大值
                    a_mins = np.random.normal(12, 1, 3)   # 3个剖面的波幅最小值

                    # 导数特征 (12个)
                    s_mean_diffs = np.random.normal(8, 1, 3)  # 3个剖面的声速平均变化率
                    s_max_diffs = np.random.normal(15, 2, 3)  # 3个剖面的声速最大变化率
                    a_mean_diffs = np.random.normal(3, 0.5, 3) # 3个剖面的波幅平均变化率
                    a_max_diffs = np.random.normal(6, 1, 3)    # 3个剖面的波幅最大变化率

                    # 横向特征 (12个)
                    s_mean_cross_diffs = np.random.normal(12, 2, 3)  # 剖面间声速平均差异
                    s_max_cross_diffs = np.random.normal(20, 3, 3)   # 剖面间声速最大差异
                    a_mean_cross_diffs = np.random.normal(5, 1, 3)   # 剖面间波幅平均差异
                    a_max_cross_diffs = np.random.normal(10, 2, 3)   # 剖面间波幅最大差异

                    # 异常点比例特征 (4个)
                    abnormal_ratios = np.random.normal(0.7, 0.1, 4)  # 异常点比例很高

                    # 深度相关特征 (2个)
                    depth_features = np.random.normal(15, 3, 2)  # 深度范围和平均深度

                # 组合所有特征
                features = np.concatenate([
                    s_means, s_stds, s_maxs, s_mins,
                    a_means, a_stds, a_maxs, a_mins,
                    s_mean_diffs, s_max_diffs, a_mean_diffs, a_max_diffs,
                    s_mean_cross_diffs, s_max_cross_diffs, a_mean_cross_diffs, a_max_cross_diffs,
                    abnormal_ratios, depth_features
                ])

                # 将类别转换为数值标签
                class_mapping = {'I类桩': 0, 'II类桩': 1, 'III类桩': 2, 'IV类桩': 3}
                label = class_mapping.get(pile_class, 0)

                # 添加到训练数据集
                self.training_data.append({
                    'features': features,
                    'label': label,
                    'pile_class': pile_class
                })

    def get_feature_names(self):
        """获取特征名称"""
        feature_names = []

        # 全局统计特征
        # 声速统计特征
        for i in range(1, 4):
            feature_names.extend([
                f'声速_{i}_均值',
                f'声速_{i}_标准差',
                f'声速_{i}_最大值',
                f'声速_{i}_最小值'
            ])

        # 波幅统计特征
        for i in range(1, 4):
            feature_names.extend([
                f'波幅_{i}_均值',
                f'波幅_{i}_标准差',
                f'波幅_{i}_最大值',
                f'波幅_{i}_最小值'
            ])

        # 导数特征
        for i in range(1, 4):
            feature_names.extend([
                f'声速_{i}_平均变化率',
                f'声速_{i}_最大变化率',
                f'波幅_{i}_平均变化率',
                f'波幅_{i}_最大变化率'
            ])

        # 横向特征
        feature_names.extend([
            '声速_1-2_平均差异',
            '声速_1-3_平均差异',
            '声速_2-3_平均差异',
            '声速_1-2_最大差异',
            '声速_1-3_最大差异',
            '声速_2-3_最大差异',
            '波幅_1-2_平均差异',
            '波幅_1-3_平均差异',
            '波幅_2-3_平均差异',
            '波幅_1-2_最大差异',
            '波幅_1-3_最大差异',
            '波幅_2-3_最大差异'
        ])

        # 异常点比例特征
        feature_names.extend([
            '异常声速点比例',
            '严重异常声速点比例',
            '异常波幅点比例',
            '严重异常波幅点比例'
        ])

        # 深度相关特征
        feature_names.extend([
            '深度范围',
            '平均深度'
        ])

        return feature_names

    def load_models(self):
        """加载AI模型"""
        try:
            classifier_path = os.path.join(self.models_dir, 'classifier_model.pkl')
            anomaly_detector_path = os.path.join(self.models_dir, 'anomaly_detector.pkl')
            scaler_path = os.path.join(self.models_dir, 'scaler.pkl')
            feature_importance_path = os.path.join(self.models_dir, 'feature_importance.pkl')
            training_data_path = os.path.join(self.models_dir, 'training_data.pkl')

            if os.path.exists(classifier_path):
                with open(classifier_path, 'rb') as f:
                    self.classifier_model = pickle.load(f)

            if os.path.exists(anomaly_detector_path):
                with open(anomaly_detector_path, 'rb') as f:
                    self.anomaly_detector = pickle.load(f)

            if os.path.exists(scaler_path):
                with open(scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)

            if os.path.exists(feature_importance_path):
                with open(feature_importance_path, 'rb') as f:
                    self.feature_importance = pickle.load(f)

            if os.path.exists(training_data_path):
                with open(training_data_path, 'rb') as f:
                    self.training_data = pickle.load(f)

            self.status_var.set("AI模型加载完成")
        except Exception as e:
            print(f"加载模型失败: {str(e)}")
            self.status_var.set("AI模型加载失败")

    def save_models(self):
        """保存AI模型"""
        try:
            os.makedirs(self.models_dir, exist_ok=True)

            classifier_path = os.path.join(self.models_dir, 'classifier_model.pkl')
            anomaly_detector_path = os.path.join(self.models_dir, 'anomaly_detector.pkl')
            scaler_path = os.path.join(self.models_dir, 'scaler.pkl')
            feature_importance_path = os.path.join(self.models_dir, 'feature_importance.pkl')
            training_data_path = os.path.join(self.models_dir, 'training_data.pkl')

            if self.classifier_model is not None:
                with open(classifier_path, 'wb') as f:
                    pickle.dump(self.classifier_model, f)

            if self.anomaly_detector is not None:
                with open(anomaly_detector_path, 'wb') as f:
                    pickle.dump(self.anomaly_detector, f)

            if self.scaler is not None:
                with open(scaler_path, 'wb') as f:
                    pickle.dump(self.scaler, f)

            if self.feature_importance is not None:
                with open(feature_importance_path, 'wb') as f:
                    pickle.dump(self.feature_importance, f)

            if self.training_data:
                with open(training_data_path, 'wb') as f:
                    pickle.dump(self.training_data, f)

            self.status_var.set("AI模型保存完成")
        except Exception as e:
            messagebox.showerror('保存错误', f'保存AI模型时出错: {str(e)}\n{traceback.format_exc()}')
            self.status_var.set("AI模型保存失败")

    def adjust_ai_parameters(self):
        """调整AI参数"""
        settings_win = tk.Toplevel(self.root)
        settings_win.title('AI参数设置')
        settings_win.geometry('400x300')

        # 不确定性阈值
        tk.Label(settings_win, text='不确定性阈值:').grid(row=0, column=0, padx=10, pady=10, sticky='w')
        uncertainty_var = tk.StringVar(value=str(self.uncertainty_threshold))
        uncertainty_entry = tk.Entry(settings_win, textvariable=uncertainty_var, width=10)
        uncertainty_entry.grid(row=0, column=1, padx=10, pady=10, sticky='w')

        # 保存按钮
        def save_settings():
            try:
                self.uncertainty_threshold = float(uncertainty_var.get())
                settings_win.destroy()
                messagebox.showinfo('设置已保存', 'AI参数已更新')
            except ValueError:
                messagebox.showerror('输入错误', '请输入有效的数值')

        tk.Button(settings_win, text='保存', command=save_settings).grid(row=1, column=0, columnspan=2, pady=20)

    def show_feature_importance(self):
        """显示特征重要性"""
        if self.feature_importance is None:
            messagebox.showwarning("数据不足", "没有特征重要性数据可显示，请先训练模型。")
            return

        try:
            # 创建特征重要性图表
            plt.figure(figsize=(10, 8))

            # 获取排序后的特征重要性
            sorted_features = sorted(self.feature_importance.items(), key=lambda x: x[1], reverse=True)
            features = [item[0] for item in sorted_features[:15]]  # 只显示前15个重要特征
            importances = [item[1] for item in sorted_features[:15]]

            # 创建条形图
            plt.barh(range(len(features)), importances, align='center')
            plt.yticks(range(len(features)), features)
            plt.xlabel('重要性')
            plt.ylabel('特征')
            plt.title('AI模型特征重要性')

            plt.tight_layout()
            plt.show()

        except Exception as e:
            messagebox.showerror('可视化错误', f'显示特征重要性时出错: {str(e)}\n{traceback.format_exc()}')

    def show_anomaly_detection(self):
        """显示异常检测结果"""
        if self.df is None or self.df.empty:
            messagebox.showwarning("数据错误", "请先加载有效数据")
            return

        if self.anomaly_detector is None or self.scaler is None:
            messagebox.showwarning("模型错误", "异常检测模型未加载，请先训练模型。")
            return

        try:
            # 提取特征
            features = self.extract_features(self.df)

            # 标准化特征
            features_scaled = self.scaler.transform(features)

            # 获取异常分数
            anomaly_scores = -self.anomaly_detector.score_samples(features_scaled)

            # 创建异常检测图表
            plt.figure(figsize=(10, 6))

            # 绘制异常分数
            plt.plot(anomaly_scores, 'o-', label='异常分数')
            plt.axhline(y=1.0, color='r', linestyle='--', label='异常阈值')

            # 标记异常点
            anomalies = anomaly_scores > 1.0
            if np.any(anomalies):
                plt.plot(np.where(anomalies)[0], anomaly_scores[anomalies], 'ro', markersize=10, label='异常点')

            plt.xlabel('样本索引')
            plt.ylabel('异常分数')
            plt.title('异常检测结果')
            plt.legend()

            plt.tight_layout()
            plt.show()

        except Exception as e:
            messagebox.showerror('可视化错误', f'显示异常检测结果时出错: {str(e)}\n{traceback.format_exc()}')

    def plotly_visualization(self, data_type):
        """使用Plotly进行三维可视化"""
        if self.df is None or self.df.empty:
            messagebox.showwarning("数据错误", "请先加载有效数据")
            return

        # 检查必要的数据列
        required_columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
        missing_columns = [col for col in required_columns if col not in self.df.columns]
        if missing_columns:
            messagebox.showerror("数据错误", f"缺失必要数据列: {', '.join(missing_columns)}")
            return

        try:
            # 使用所有数据点作为采样点
            data_points = len(self.df)
            print(f"使用所有数据点: {data_points}")

            # 创建模式选择对话框
            mode_choice = simpledialog.askstring("可视化模式", "请选择模式(surface/scatter):", initialvalue='scatter')

            # 准备数据
            depths = self.df['Depth'].values

            # 创建三维图表
            fig = go.Figure()

            # 添加三个剖面的数据
            profiles = []
            if data_type == 'speed':
                profiles = [
                    {'name': '剖面1-2', 'col': 'S1', 'y_val': 1},
                    {'name': '剖面1-3', 'col': 'S2', 'y_val': 2},
                    {'name': '剖面2-3', 'col': 'S3', 'y_val': 3}
                ]
                title = '桩基相对波速三维分布'
                xaxis_title = '相对波速 (%)'
            else:  # data_type == 'amplitude'
                profiles = [
                    {'name': '剖面1-2', 'col': 'A1', 'y_val': 1},
                    {'name': '剖面1-3', 'col': 'A2', 'y_val': 2},
                    {'name': '剖面2-3', 'col': 'A3', 'y_val': 3}
                ]
                title = '桩基波幅三维分布'
                xaxis_title = '波幅 (dB)'

            for profile in profiles:
                if mode_choice == 'surface':
                    # 创建网格数据用于surface图
                    y_vals = np.array([profile['y_val'] - 0.2, profile['y_val'] + 0.2])
                    z_vals = depths

                    # 创建网格
                    y_grid, z_grid = np.meshgrid(y_vals, z_vals)

                    # 对于每个深度和y值，使用相同的x值（相对波速或波幅）
                    x_grid = np.tile(self.df[profile['col']].values[:, np.newaxis], (1, len(y_vals)))

                    # 为每个点根据分类标准分配颜色
                    colors = []
                    for value in self.df[profile['col']].values:
                        if data_type == 'speed':
                            category = self.classify_value(value, 'speed')
                        else:
                            category = self.classify_value(value, 'amplitude')
                        colors.append(self.get_category_color(category))

                    # 创建自定义颜色刻度
                    custom_colorscale = [
                        [0, 'rgb(0,180,0)'],      # 绿色 - 正常
                        [0.33, 'rgb(255,255,0)'],  # 黄色 - 轻微畸变
                        [0.66, 'rgb(255,165,0)'],  # 橙色 - 明显畸变
                        [1, 'rgb(255,0,0)']       # 红色 - 严重畸变
                    ]

                    # 添加surface
                    fig.add_trace(go.Surface(
                        z=z_grid,
                        y=y_grid,
                        x=x_grid,
                        surfacecolor=np.tile(self.df[profile['col']].values[:, np.newaxis], (1, len(y_vals))),
                        colorscale=custom_colorscale,
                        opacity=0.8,
                        showscale=True,
                        colorbar=dict(
                            title=xaxis_title,
                            tickvals=self.get_category_thresholds(data_type),
                            ticktext=['严重畸变', '明显畸变', '轻微畸变', '正常']
                        ),
                        name=profile['name']
                    ))
                else:
                    # 为每个点根据分类标准分配颜色
                    colors = []
                    for value in self.df[profile['col']].values:
                        if data_type == 'speed':
                            category = self.classify_value(value, 'speed')
                        else:
                            category = self.classify_value(value, 'amplitude')
                        colors.append(self.get_category_color(category))

                    # 添加散点图
                    fig.add_trace(go.Scatter3d(
                        x=self.df[profile['col']].values,
                        y=np.ones(len(depths)) * profile['y_val'],
                        z=depths,
                        mode='markers',
                        marker=dict(
                            size=5,
                            color=colors,
                            opacity=0.8
                        ),
                        name=profile['name']
                    ))

            # 添加分类标准图例
            if mode_choice == 'scatter':
                # 为散点图添加图例
                for category, color in [('正常', 'rgb(0,180,0)'), ('轻微畸变', 'rgb(255,255,0)'),
                                       ('明显畸变', 'rgb(255,165,0)'), ('严重畸变', 'rgb(255,0,0)')]:
                    if data_type == 'speed':
                        range_text = f"{self.config[category]['speed'][0]}-{self.config[category]['speed'][1]}%"
                    else:
                        range_text = f"{self.config[category]['amp'][0]}-{self.config[category]['amp'][1]}dB"

                    fig.add_trace(go.Scatter3d(
                        x=[None],
                        y=[None],
                        z=[None],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color=color,
                            opacity=1.0
                        ),
                        name=f"{category} ({range_text})"
                    ))

            # 设置图表布局
            fig.update_layout(
                title=title,
                scene=dict(
                    xaxis_title=xaxis_title,
                    yaxis_title='剖面',
                    yaxis=dict(
                        tickvals=[1, 2, 3],
                        ticktext=['剖面1-2', '剖面1-3', '剖面2-3']
                    ),
                    zaxis_title='深度 (m)',
                    zaxis=dict(autorange='reversed')
                ),
                width=1000,
                height=800,
                legend=dict(
                    title="分类标准",
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                )
            )

            # 显示图表
            fig.show()

        except Exception as e:
            print(f"可视化失败: {str(e)}")
            messagebox.showerror("可视化错误", f"三维可视化失败: {str(e)}")

    def classify_value(self, value, data_type):
        """根据值和类型进行分类"""
        o_types = ['严重畸变', '明显畸变', '轻微畸变', '正常']

        for lvl in o_types:
            if lvl in self.config:
                if data_type == 'speed':
                    s_min, s_max = self.config[lvl]['speed']
                    if s_min <= value < s_max:
                        return lvl
                else:  # data_type == 'amplitude'
                    a_min, a_max = self.config[lvl]['amp']
                    if a_min <= value < a_max:
                        return lvl

        return '正常'  # 默认分类

    def get_category_color(self, category):
        """获取分类对应的颜色"""
        category_colors = {
            '正常': 'rgb(0,180,0)',       # 绿色
            '轻微畸变': 'rgb(255,255,0)',  # 黄色
            '明显畸变': 'rgb(255,165,0)',  # 橙色
            '严重畸变': 'rgb(255,0,0)'     # 红色
        }

        return category_colors.get(category, 'rgb(128,128,128)')  # 默认灰色

    def get_category_thresholds(self, data_type):
        """获取分类阈值"""
        if data_type == 'speed':
            return [
                self.config['严重畸变']['speed'][0],
                self.config['明显畸变']['speed'][0],
                self.config['轻微畸变']['speed'][0],
                self.config['正常']['speed'][0]
            ]
        else:  # data_type == 'amplitude'
            return [
                self.config['严重畸变']['amp'][0],
                self.config['明显畸变']['amp'][0],
                self.config['轻微畸变']['amp'][0],
                self.config['正常']['amp'][0]
            ]

    def generate_report(self, report_type):
        """生成报告"""
        if report_type == 'traditional' and self.traditional_result is None:
            messagebox.showwarning("报告生成", "请先完成传统分析")
            return

        if report_type == 'ai' and self.ai_result is None:
            messagebox.showwarning("报告生成", "请先完成AI分析")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            defaultextension=".md",
            filetypes=[("Markdown", "*.md"), ("Text", "*.txt"), ("All Files", "*.*")],
            title="保存报告"
        )

        if not file_path:
            return

        try:
            # 生成报告内容
            if report_type == 'traditional':
                content = self.generate_traditional_report()
            else:  # report_type == 'ai'
                content = self.generate_ai_report()

            # 保存报告
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            messagebox.showinfo("报告生成", f"报告已保存至: {file_path}")

        except Exception as e:
            messagebox.showerror('报告生成错误', f'生成报告时出错: {str(e)}\n{traceback.format_exc()}')

    def generate_traditional_report(self):
        """生成传统分析报告"""
        res = self.traditional_result

        lines = []
        lines.append("# 桩基完整性传统分析报告")
        lines.append("\n## 分析结论")
        lines.append(f"- **桩基完整性类别**: {res.get('完整性类别', 'N/A')}")
        lines.append(f"- **判定概述**: {res.get('overall_reasoning_intro', '无概述')}")
        lines.append(f"- **详细判定依据**: {res.get('detailed_overall_reason', '无详细依据')}")

        lines.append("\n## 各深度异常详情")
        if not res.get("异常区域"):
            lines.append("无异常点")
        else:
            sorted_depths_float = sorted(res["异常区域"].keys())
            per_depth_class_details = res.get("per_depth_classification_details", {})

            for depth_key_float in sorted_depths_float:
                items_at_depth = res["异常区域"][depth_key_float]
                lines.append(f"\n### 深度 {depth_key_float:.2f}m:")

                depth_class_info = per_depth_class_details.get(depth_key_float)
                if depth_class_info:
                    lines.append(f"- **该深度综合判定**: {depth_class_info['class']} ({depth_class_info['reason']})")

                for item in items_at_depth:
                    cl = f"{item.get('连续长度', 0):.2f}m"
                    s, a = item.get("声速", "N/A"), item.get("波幅", "N/A")
                    s_t, a_t = (f"{v:.1f}" if isinstance(v, (int, float)) else str(v) for v in (s, a))

                    lines.append(f"- 剖面{item['剖面']}: {item['类型']} (声速:{s_t} 波幅:{a_t}, 连续:{cl})")

        lines.append("\n## 分析参数")
        lines.append("### 分类标准")
        for category in ['正常', '轻微畸变', '明显畸变', '严重畸变']:
            s_min, s_max = self.config[category]['speed']
            a_min, a_max = self.config[category]['amp']
            lines.append(f"- {category}: 声速({s_min}-{s_max}%), 波幅({a_min}-{a_max}dB)")

        lines.append(f"\n### 连续长度阈值: {self.config['continuous_threshold']}m")

        return "\n".join(lines)

    def generate_ai_report(self):
        """生成AI分析报告"""
        res = self.ai_result

        lines = []
        lines.append("# 桩基完整性AI增强分析报告")
        lines.append("\n## AI分析结论")
        lines.append(f"- **桩基完整性类别**: {res.get('完整性类别', 'N/A')}")
        lines.append(f"- **AI置信度**: {res.get('ai_confidence', 0.0):.2%}")
        lines.append(f"- **异常分数**: {res.get('anomaly_score', 0.0):.2f}")
        lines.append(f"- **分析结论**: {res.get('overall_reasoning', '无分析结论')}")

        lines.append("\n## 各类别概率")
        class_probabilities = res.get('class_probabilities', {})
        for class_name, prob in class_probabilities.items():
            lines.append(f"- {class_name}: {prob:.2%}")

        lines.append("\n## 特征重要性排名")
        feature_importance = res.get('feature_importance', {})
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        for i, (feature, importance) in enumerate(sorted_features[:10]):  # 只显示前10个重要特征
            lines.append(f"{i+1}. {feature}: {importance:.4f}")

        lines.append("\n## 传统分析结果比较")
        traditional_result = res.get('traditional_result', {})
        traditional_class = traditional_result.get('完整性类别', 'N/A')
        lines.append(f"- **传统分析桩基完整性类别**: {traditional_class}")

        if traditional_class == res.get('完整性类别', 'N/A'):
            lines.append("- **结果一致性**: AI分析结果与传统分析结果一致")
        else:
            lines.append("- **结果一致性**: AI分析结果与传统分析结果不一致")
            lines.append(f"  - 传统分析结果: {traditional_class}")
            lines.append(f"  - AI分析结果: {res.get('完整性类别', 'N/A')}")

        return "\n".join(lines)

    def show_settings(self):
        """显示参数设置对话框"""
        settings_win = tk.Toplevel(self.root)
        settings_win.title('参数设置')
        settings_win.geometry('500x400')

        # 创建标签页控件
        settings_notebook = ttk.Notebook(settings_win)
        settings_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 分类标准标签页
        classification_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(classification_frame, text='分类标准')

        # 创建分类标准设置
        categories = ['正常', '轻微畸变', '明显畸变', '严重畸变']
        category_vars = {}

        for i, category in enumerate(categories):
            tk.Label(classification_frame, text=f"{category}:").grid(row=i, column=0, padx=10, pady=10, sticky='w')

            # 声速范围
            tk.Label(classification_frame, text="声速范围:").grid(row=i, column=1, padx=10, pady=10, sticky='w')
            s_min_var = tk.StringVar(value=str(self.config[category]['speed'][0]))
            s_min_entry = tk.Entry(classification_frame, textvariable=s_min_var, width=8)
            s_min_entry.grid(row=i, column=2, padx=5, pady=10, sticky='w')

            tk.Label(classification_frame, text="-").grid(row=i, column=3, padx=2, pady=10)

            s_max_var = tk.StringVar(value=str(self.config[category]['speed'][1]))
            s_max_entry = tk.Entry(classification_frame, textvariable=s_max_var, width=8)
            s_max_entry.grid(row=i, column=4, padx=5, pady=10, sticky='w')

            # 波幅范围
            tk.Label(classification_frame, text="波幅范围:").grid(row=i, column=5, padx=10, pady=10, sticky='w')
            a_min_var = tk.StringVar(value=str(self.config[category]['amp'][0]))
            a_min_entry = tk.Entry(classification_frame, textvariable=a_min_var, width=8)
            a_min_entry.grid(row=i, column=6, padx=5, pady=10, sticky='w')

            tk.Label(classification_frame, text="-").grid(row=i, column=7, padx=2, pady=10)

            a_max_var = tk.StringVar(value=str(self.config[category]['amp'][1]))
            a_max_entry = tk.Entry(classification_frame, textvariable=a_max_var, width=8)
            a_max_entry.grid(row=i, column=8, padx=5, pady=10, sticky='w')

            category_vars[category] = {
                's_min': s_min_var, 's_max': s_max_var,
                'a_min': a_min_var, 'a_max': a_max_var
            }

        # 其他参数标签页
        other_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(other_frame, text='其他参数')

        # 连续长度阈值
        tk.Label(other_frame, text='连续长度阈值 (m):').grid(row=0, column=0, padx=10, pady=10, sticky='w')
        continuous_threshold_var = tk.StringVar(value=str(self.config['continuous_threshold']))
        continuous_threshold_entry = tk.Entry(other_frame, textvariable=continuous_threshold_var, width=10)
        continuous_threshold_entry.grid(row=0, column=1, padx=10, pady=10, sticky='w')

        # 保存按钮
        def save_settings():
            try:
                # 保存分类标准
                for category, vars_dict in category_vars.items():
                    s_min = float(vars_dict['s_min'].get())
                    s_max = float(vars_dict['s_max'].get())
                    a_min = float(vars_dict['a_min'].get())
                    a_max = float(vars_dict['a_max'].get())

                    self.config[category]['speed'] = (s_min, s_max)
                    self.config[category]['amp'] = (a_min, a_max)

                # 保存其他参数
                self.config['continuous_threshold'] = float(continuous_threshold_var.get())

                settings_win.destroy()
                messagebox.showinfo('设置已保存', '参数设置已更新')
            except ValueError:
                messagebox.showerror('输入错误', '请输入有效的数值')

        tk.Button(settings_win, text='保存', command=save_settings).pack(pady=20)


def main():
    app = AIPileIntegrityAnalyzer()
    app.root.mainloop()


if __name__ == "__main__":
    main()
