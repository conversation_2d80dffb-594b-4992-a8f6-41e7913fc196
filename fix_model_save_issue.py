#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复模型保存失败问题
Fix Model Save Failure Issue
"""

import os
import pickle
import traceback
from datetime import datetime

def diagnose_save_issue():
    """诊断保存问题"""
    print("🔍 诊断模型保存问题")
    print("=" * 60)
    
    issues = []
    
    # 1. 检查目录权限
    print("\n📁 检查目录权限...")
    
    test_dirs = [
        ".",
        "advanced_models",
        "models",
        "results"
    ]
    
    for dir_path in test_dirs:
        try:
            # 创建目录（如果不存在）
            os.makedirs(dir_path, exist_ok=True)
            
            # 测试写入权限
            test_file = os.path.join(dir_path, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            
            print(f"  ✅ {dir_path}: 可写")
            
        except PermissionError:
            print(f"  ❌ {dir_path}: 权限不足")
            issues.append(f"目录 {dir_path} 权限不足")
        except Exception as e:
            print(f"  ⚠️ {dir_path}: {e}")
            issues.append(f"目录 {dir_path} 访问异常: {e}")
    
    # 2. 检查磁盘空间
    print("\n💾 检查磁盘空间...")
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free // (1024**3)
        print(f"  可用空间: {free_gb} GB")
        
        if free_gb < 1:
            print(f"  ⚠️ 磁盘空间不足")
            issues.append("磁盘空间不足")
        else:
            print(f"  ✅ 磁盘空间充足")
    except Exception as e:
        print(f"  ⚠️ 无法检查磁盘空间: {e}")
    
    # 3. 测试pickle序列化
    print("\n🔧 测试pickle序列化...")
    try:
        # 创建测试数据
        test_data = {
            'test_model': 'dummy_model',
            'test_scaler': 'dummy_scaler',
            'timestamp': datetime.now()
        }
        
        test_file = "test_pickle.pkl"
        with open(test_file, 'wb') as f:
            pickle.dump(test_data, f)
        
        # 测试读取
        with open(test_file, 'rb') as f:
            loaded_data = pickle.load(f)
        
        os.remove(test_file)
        print(f"  ✅ pickle序列化正常")
        
    except Exception as e:
        print(f"  ❌ pickle序列化失败: {e}")
        issues.append(f"pickle序列化失败: {e}")
    
    return issues

def create_robust_save_method():
    """创建健壮的保存方法"""
    print("\n🔧 创建健壮的保存方法...")
    
    save_method_code = '''
def robust_save_model(self, file_path):
    """健壮的模型保存方法"""
    try:
        print(f"🔄 开始保存模型到: {file_path}")
        
        # 1. 检查并创建目录
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            print(f"📁 创建目录: {dir_path}")
            os.makedirs(dir_path, exist_ok=True)
        
        # 2. 检查模型是否存在
        if not hasattr(self, 'training_results') or not self.training_results:
            print("❌ 没有训练结果可保存")
            return False
        
        # 3. 根据训练模式选择保存方法
        mode = self.training_results.get('mode', 'unknown')
        model_type = self.training_results.get('model_type', '')
        
        if 'enhanced' in model_type and 'trainer' in self.training_results:
            # 保存增强训练器
            print("💾 保存增强训练模型...")
            trainer = self.training_results['trainer']
            trainer.save_model(file_path)
            return True
            
        elif hasattr(self, 'training_system') and self.training_system:
            # 保存传统训练系统
            print("💾 保存传统训练模型...")
            return self.training_system.save_model(file_path)
        
        else:
            print("❌ 没有可用的训练模型")
            return False
            
    except Exception as e:
        print(f"❌ 保存模型失败: {e}")
        traceback.print_exc()
        return False
'''
    
    with open('robust_save_method.py', 'w', encoding='utf-8') as f:
        f.write(save_method_code)
    
    print("✅ 健壮保存方法已创建: robust_save_method.py")

def test_enhanced_trainer_save():
    """测试增强训练器保存"""
    print("\n🧪 测试增强训练器保存...")
    
    try:
        from enhanced_training_system import Enhanced94PercentTrainer
        
        # 创建训练器
        trainer = Enhanced94PercentTrainer()
        
        # 模拟训练状态
        trainer.is_trained = True
        trainer.model = "dummy_model"
        trainer.scaler = "dummy_scaler"
        trainer.feature_selector = "dummy_selector"
        
        # 测试保存
        test_path = "test_enhanced_model.pkl"
        trainer.save_model(test_path)
        
        # 验证文件存在
        if os.path.exists(test_path):
            print("✅ 增强训练器保存成功")
            
            # 测试加载
            trainer2 = Enhanced94PercentTrainer()
            trainer2.load_model(test_path)
            print("✅ 增强训练器加载成功")
            
            # 清理
            os.remove(test_path)
            return True
        else:
            print("❌ 保存文件未创建")
            return False
            
    except Exception as e:
        print(f"❌ 增强训练器保存测试失败: {e}")
        traceback.print_exc()
        return False

def fix_gui_save_method():
    """修复GUI中的保存方法"""
    print("\n🔧 修复GUI中的保存方法...")
    
    try:
        # 读取GUI文件
        gui_file = "auto_train_classify_gui.py"
        
        if not os.path.exists(gui_file):
            print(f"❌ GUI文件不存在: {gui_file}")
            return False
        
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有健壮的保存方法
        if 'robust_save_model' in content:
            print("✅ GUI已包含健壮保存方法")
            return True
        
        # 查找save_trained_model方法
        if 'def save_trained_model(self):' not in content:
            print("❌ 未找到save_trained_model方法")
            return False
        
        # 创建改进的保存方法
        improved_method = '''
    def save_trained_model(self):
        """Save the trained model with improved error handling"""
        try:
            # Get default model name based on training mode and timestamp
            mode = getattr(self, 'current_training_mode', 'unknown')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"pile_model_{mode}_{timestamp}.pkl"

            # Ask user for save location and name
            file_path = filedialog.asksaveasfilename(
                title="Save Trained Model",
                defaultextension=".pkl",
                filetypes=[
                    ("Pickle files", "*.pkl"),
                    ("All files", "*.*")
                ],
                initialdir="advanced_models" if os.path.exists("advanced_models") else "."
            )

            if not file_path:
                return

            # Create directory if it doesn't exist
            dir_path = os.path.dirname(file_path)
            if dir_path:
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    print(f"📁 目录已创建: {dir_path}")
                except Exception as e:
                    print(f"⚠️ 创建目录失败: {e}")

            # Improved save logic
            success = self.robust_save_model(file_path)

            if success:
                messagebox.showinfo("Model Saved",
                                  f"Model successfully saved to:\\n{file_path}")
                self.log_message(f"Model saved to: {file_path}")

                # Update status
                self.progress_queue.put({
                    'status': f'Model saved to: {os.path.basename(file_path)}'
                })
            else:
                messagebox.showerror("Save Failed", "Failed to save the model.")

        except Exception as e:
            error_msg = f"Failed to save model: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            messagebox.showerror("Error", error_msg)
            self.log_message(f"Error saving model: {str(e)}")

    def robust_save_model(self, file_path):
        """Robust model save method with comprehensive error handling"""
        try:
            print(f"🔄 开始保存模型到: {file_path}")
            
            # Check if we have training results
            if not hasattr(self, 'training_results') or not self.training_results:
                print("❌ 没有训练结果可保存")
                return False
            
            mode = self.training_results.get('mode', 'unknown')
            model_type = self.training_results.get('model_type', '')
            
            print(f"📊 训练模式: {mode}")
            print(f"🤖 模型类型: {model_type}")
            
            # Save enhanced trainer models
            if 'enhanced' in model_type and 'trainer' in self.training_results:
                print("💾 保存增强训练模型...")
                trainer = self.training_results['trainer']
                
                if hasattr(trainer, 'save_model') and trainer.is_trained:
                    trainer.save_model(file_path)
                    print("✅ 增强模型保存成功")
                    return True
                else:
                    print("❌ 增强训练器未训练或无保存方法")
                    return False
            
            # Save traditional training system models
            elif hasattr(self, 'training_system') and self.training_system:
                print("💾 保存传统训练模型...")
                success = self.training_system.save_model(file_path)
                if success:
                    print("✅ 传统模型保存成功")
                else:
                    print("❌ 传统模型保存失败")
                return success
            
            else:
                print("❌ 没有可用的训练模型")
                return False
                
        except Exception as e:
            print(f"❌ 保存模型失败: {e}")
            traceback.print_exc()
            return False
'''
        
        print("✅ 改进的保存方法已准备好")
        print("💡 建议手动将改进的方法添加到GUI文件中")
        
        # 保存改进方法到单独文件
        with open('improved_save_methods.py', 'w', encoding='utf-8') as f:
            f.write(improved_method)
        
        print("📄 改进方法已保存到: improved_save_methods.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复GUI保存方法失败: {e}")
        traceback.print_exc()
        return False

def create_manual_save_script():
    """创建手动保存脚本"""
    print("\n📝 创建手动保存脚本...")
    
    script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
手动保存训练模型脚本
Manual Model Save Script
"""

import os
import pickle
from datetime import datetime

def manual_save_model():
    """手动保存模型"""
    print("💾 手动保存模型")
    print("=" * 40)
    
    try:
        # 检查是否有增强训练器的模型文件
        enhanced_model_files = [
            "enhanced_quick_model.pkl",
            "enhanced_advanced_model.pkl", 
            "enhanced_research_model.pkl",
            "enhanced_94_percent_model.pkl"
        ]
        
        found_models = []
        for model_file in enhanced_model_files:
            if os.path.exists(model_file):
                found_models.append(model_file)
                print(f"✅ 找到模型: {model_file}")
        
        if not found_models:
            print("❌ 未找到任何训练好的模型")
            return False
        
        # 创建保存目录
        save_dir = "saved_models"
        os.makedirs(save_dir, exist_ok=True)
        
        # 复制模型到保存目录
        import shutil
        for model_file in found_models:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = os.path.splitext(model_file)[0]
            new_name = f"{base_name}_{timestamp}.pkl"
            new_path = os.path.join(save_dir, new_name)
            
            shutil.copy2(model_file, new_path)
            print(f"📁 已保存: {new_path}")
        
        print(f"\\n🎉 所有模型已保存到: {save_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 手动保存失败: {e}")
        return False

if __name__ == "__main__":
    manual_save_model()
'''
    
    with open('manual_save_model.py', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print("✅ 手动保存脚本已创建: manual_save_model.py")

def main():
    """主函数"""
    print("🔧 修复模型保存失败问题")
    print("=" * 80)
    
    # 1. 诊断问题
    issues = diagnose_save_issue()
    
    # 2. 测试增强训练器保存
    enhanced_save_ok = test_enhanced_trainer_save()
    
    # 3. 创建健壮保存方法
    create_robust_save_method()
    
    # 4. 修复GUI保存方法
    gui_fix_ok = fix_gui_save_method()
    
    # 5. 创建手动保存脚本
    create_manual_save_script()
    
    # 总结
    print(f"\n📋 问题修复总结")
    print("=" * 80)
    
    if issues:
        print("⚠️ 发现的问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ 未发现系统级问题")
    
    print(f"\\n🧪 测试结果:")
    print(f"增强训练器保存: {'✅ 正常' if enhanced_save_ok else '❌ 异常'}")
    print(f"GUI修复: {'✅ 完成' if gui_fix_ok else '❌ 失败'}")
    
    print(f"\\n💡 解决方案:")
    print("1. 使用 manual_save_model.py 手动保存现有模型")
    print("2. 查看 improved_save_methods.py 了解改进的保存方法")
    print("3. 确保有足够的磁盘空间和目录权限")
    print("4. 如果问题持续，请检查具体的错误信息")
    
    print(f"\\n🚀 下一步:")
    print("python manual_save_model.py  # 手动保存现有模型")

if __name__ == "__main__":
    main()
