#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试GZ数据生成问题
"""

import pandas as pd
from generate_optimized_synthetic_data import OptimizedGZDataGenerator

def debug_single_sample():
    """调试单个样本的生成过程"""
    generator = OptimizedGZDataGenerator()
    
    print("🔍 调试I类桩数据生成过程:")
    print("=" * 80)
    
    # 生成一个I类桩样本
    df = generator.generate_pile_data('I类桩', num_depths=10)
    
    print("📊 生成的数据样本:")
    print(df.head())
    
    print("\n🧮 逐行分析I(j,i)和K(i)值:")
    print("-" * 80)
    
    K_values = []
    
    for idx, row in df.iterrows():
        print(f"\n深度 {row['Depth']:.1f}m:")
        I_ji_values = []
        
        for profile_idx in range(3):
            sp = row[f'S{profile_idx+1}']
            ad = row[f'A{profile_idx+1}']
            bi = row[f'BI{profile_idx+1}']
            
            I_ji = generator.calculate_I_ji(sp, ad, bi)
            I_ji_values.append(I_ji)
            
            print(f"  剖面{profile_idx+1}: Sp={sp:.1f}%, Ad={ad:.1f}dB, Bi={bi:.2f} -> I(j,i)={I_ji}")
        
        K_i = generator.calculate_K_i(I_ji_values)
        K_values.append(K_i)
        print(f"  K(i) = {K_i}")
    
    print(f"\n📈 所有K值: {K_values}")
    predicted_class = generator.determine_final_category(K_values)
    print(f"🎯 预测类别: {predicted_class}")
    
    # 分析为什么不是I类桩
    has_K4 = any(k == 4 for k in K_values)
    has_K3 = any(k == 3 for k in K_values)
    has_K2 = any(k == 2 for k in K_values)
    all_K1 = all(k == 1 for k in K_values)
    
    print(f"\n📋 K值分析:")
    print(f"  有K=4: {has_K4}")
    print(f"  有K=3: {has_K3}")
    print(f"  有K=2: {has_K2}")
    print(f"  全是K=1: {all_K1}")

def test_parameter_combinations():
    """测试参数组合是否正确"""
    generator = OptimizedGZDataGenerator()
    
    print("\n🧪 测试I类桩参数组合:")
    print("=" * 80)
    
    # 测试I类桩的参数组合
    i_combinations = generator.pile_templates['I类桩']['combinations']
    
    for i, (sp_range, ad_range, bi_range) in enumerate(i_combinations):
        print(f"\n组合 {i+1}:")
        print(f"  Sp范围: {sp_range}")
        print(f"  Ad范围: {ad_range}")
        print(f"  Bi范围: {bi_range}")
        
        # 测试这个组合的中心值
        sp_center = (sp_range[0] + sp_range[1]) / 2
        ad_center = (ad_range[0] + ad_range[1]) / 2
        bi_center = (bi_range[0] + bi_range[1]) / 2
        
        I_ji = generator.calculate_I_ji(sp_center, ad_center, bi_center)
        print(f"  中心值测试: Sp={sp_center:.1f}, Ad={ad_center:.1f}, Bi={bi_center:.2f} -> I(j,i)={I_ji}")
        
        # 测试边界值
        for sp in [sp_range[0], sp_range[1]]:
            for ad in [ad_range[0], ad_range[1]]:
                for bi in [bi_range[0], bi_range[1]]:
                    I_ji = generator.calculate_I_ji(sp, ad, bi)
                    print(f"    边界测试: Sp={sp:.1f}, Ad={ad:.1f}, Bi={bi:.2f} -> I(j,i)={I_ji}")

def analyze_gz_rules():
    """分析GZ规则的具体实现"""
    generator = OptimizedGZDataGenerator()
    
    print("\n🔬 分析GZ规则实现:")
    print("=" * 80)
    
    # 测试I(j,i)=1的条件
    print("I(j,i)=1的条件:")
    test_cases_1 = [
        (105, -1, 0.9),  # Sp≥100, Ad≤0, Bi>0.8
        (95, 0, 0.9),    # 85≤Sp<100, Ad≤0, Bi>0.8
        (105, 2, 0.9),   # Sp≥100, 0<Ad≤4, Bi>0.8
    ]
    
    for sp, ad, bi in test_cases_1:
        I_ji = generator.calculate_I_ji(sp, ad, bi)
        print(f"  Sp={sp}, Ad={ad}, Bi={bi} -> I(j,i)={I_ji} {'✅' if I_ji == 1 else '❌'}")
    
    # 测试边界情况
    print("\n边界情况测试:")
    boundary_cases = [
        (100, 0, 0.8),   # 边界值
        (85, 0, 0.8),    # 边界值
        (100, 4, 0.8),   # 边界值
        (84, 0, 0.9),    # 应该不满足条件
        (100, 5, 0.9),   # 应该不满足条件
    ]
    
    for sp, ad, bi in boundary_cases:
        I_ji = generator.calculate_I_ji(sp, ad, bi)
        print(f"  Sp={sp}, Ad={ad}, Bi={bi} -> I(j,i)={I_ji}")

if __name__ == "__main__":
    debug_single_sample()
    test_parameter_combinations()
    analyze_gz_rules()
