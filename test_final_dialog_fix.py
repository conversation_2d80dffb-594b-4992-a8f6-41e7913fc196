#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试最终修复的模型加载对话框
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def create_simple_test_model():
    """创建简单的测试模型"""
    print("🔧 创建简单测试模型")
    print("=" * 60)
    
    # 确保ai_models目录存在
    ai_models_dir = "ai_models"
    os.makedirs(ai_models_dir, exist_ok=True)
    
    # 生成简单测试数据
    np.random.seed(42)
    X = np.random.randn(50, 54)  # 54个特征
    y = np.random.randint(0, 4, 50)  # 4个类别
    
    print("📊 训练简单模型...")
    
    # 简单的分类器
    classifier = RandomForestClassifier(
        n_estimators=10,
        max_depth=3,
        random_state=42
    )
    classifier.fit(X, y)
    
    # 计算训练准确率
    train_accuracy = classifier.score(X, y)
    print(f"✅ 训练准确率: {train_accuracy:.2%}")
    
    # 创建简单模型包
    simple_model = {
        'classifier_model': classifier,
        'metadata': {
            'name': '最终测试模型',
            'version': '1.0',
            'accuracy': train_accuracy,
            'features': 54,
            'description': '用于测试最终修复的对话框'
        }
    }
    
    # 保存到ai_models目录
    model_path = os.path.join(ai_models_dir, "final_test_model.pkl")
    with open(model_path, 'wb') as f:
        pickle.dump(simple_model, f)
    
    print(f"✅ 简单模型已保存到: {model_path}")
    print(f"📁 文件大小: {os.path.getsize(model_path) / 1024:.1f} KB")
    
    return model_path

def test_dialog_layout():
    """测试对话框布局"""
    print("\n🧪 测试对话框布局")
    print("=" * 60)
    
    try:
        # 创建测试模型
        model_path = create_simple_test_model()
        
        # 测试模型管理器的加载功能
        from model_manager import get_model_manager
        
        model_manager = get_model_manager()
        
        print(f"\n📥 测试模型加载:")
        success = model_manager.load_external_model(model_path, "最终测试模型")
        
        if success:
            print(f"✅ 模型加载成功")
            
            # 获取模型信息
            models = model_manager.get_available_models()
            for key, model_info in models.items():
                if "最终测试" in model_info.name:
                    print(f"📊 模型信息:")
                    print(f"  - 名称: {model_info.name}")
                    print(f"  - 类型: {model_info.model_type}")
                    print(f"  - 特征数: {model_info.feature_count}")
                    print(f"  - 预期准确率: {model_info.accuracy:.1%}")
                    break
        else:
            print(f"❌ 模型加载失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 对话框布局测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_final_instructions():
    """显示最终使用说明"""
    print(f"\n🖥️ 最终修复说明")
    print("=" * 60)
    
    print("📋 最新修复内容:")
    print("✅ 窗口大小: 650x550 (更大的显示区域)")
    print("✅ 布局方式: Grid布局替代Pack布局")
    print("✅ 预览高度: 限制为8行确保按钮可见")
    print("✅ 按钮位置: 固定在第5行(底部)")
    print("✅ 权重配置: 只有预览区域可扩展")
    
    print(f"\n📋 GUI测试步骤:")
    print("1. 启动GUI: python Pile_analyze_GZ_gui.py")
    print("2. 选择 '🚀 AI System V2.0'")
    print("3. 点击 '📥 加载模型' 按钮")
    print("4. 选择测试模型: final_test_model.pkl")
    print("5. 在650x550的对话框中检查:")
    print("   - 标题是否显示在顶部")
    print("   - 文件信息是否完整显示")
    print("   - 模型名称输入框是否可见")
    print("   - 预览区域是否适中(8行高度)")
    print("   - 分隔线是否清晰")
    print("   - '✅ 确定加载' 和 '❌ 取消' 按钮是否在底部可见")
    print("6. 点击 '✅ 确定加载' 按钮")
    print("7. 验证模型是否成功加载")
    
    print(f"\n🔧 关键技术改进:")
    print("- 使用Grid布局替代Pack布局")
    print("- 预览文本框高度限制为8行")
    print("- 按钮框架固定在grid第5行")
    print("- 只有预览区域(第3行)设置weight=1")
    print("- 窗口大小增加到650x550")
    
    print(f"\n💡 如果按钮仍然不可见:")
    print("- 尝试调整窗口大小(可拖拽)")
    print("- 检查屏幕分辨率是否足够")
    print("- 滚动预览区域查看是否有内容遮挡")
    print("- 使用回车键作为快捷操作")

def test_gui_import_final():
    """最终GUI导入测试"""
    print(f"\n🔍 最终GUI导入测试")
    print("=" * 60)
    
    try:
        # 强制清除模块缓存
        modules_to_clear = [k for k in sys.modules.keys() if 'Pile_analyze_GZ_gui' in k]
        for module in modules_to_clear:
            del sys.modules[module]
        
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        print("✅ GUI模块导入成功")
        
        # 检查关键方法
        required_methods = [
            'load_external_model_v2',
            '_show_model_loading_dialog',
            '_analyze_and_preview_model'
        ]
        
        print("检查关键方法:")
        for method in required_methods:
            if hasattr(PileAnalyzerGZGUI, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """清理测试文件"""
    print(f"\n🧹 清理测试文件")
    print("=" * 60)
    
    try:
        test_file = "ai_models/final_test_model.pkl"
        
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"✅ 已删除: {test_file}")
        
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

def main():
    """主函数"""
    print("🔬 测试最终修复的模型加载对话框")
    print("=" * 80)
    
    try:
        # 1. 测试GUI模块导入
        gui_success = test_gui_import_final()
        
        # 2. 测试对话框布局
        if gui_success:
            layout_success = test_dialog_layout()
        else:
            layout_success = False
        
        # 3. 显示最终说明
        show_final_instructions()
        
        # 总结
        print(f"\n📋 最终测试总结")
        print("=" * 60)
        
        print(f"GUI模块导入: {'✅ 成功' if gui_success else '❌ 失败'}")
        print(f"对话框布局: {'✅ 成功' if layout_success else '❌ 失败'}")
        
        if gui_success and layout_success:
            print(f"\n🎉 最终修复测试成功!")
            print(f"对话框现在应该能正确显示按钮了")
            
            # 询问是否保留测试文件
            print(f"\n❓ 是否保留测试模型文件供GUI测试？")
            print("测试模型: ai_models/final_test_model.pkl")
            print("输入 'n' 清理，其他键保留文件")
            
            try:
                choice = input("请选择: ").strip().lower()
                if choice == 'n':
                    cleanup_test_files()
                else:
                    print("📁 测试文件已保留，您可以在GUI中使用")
                    print("文件位置: ai_models/final_test_model.pkl")
            except:
                print("📁 测试文件已保留")
                
        else:
            print(f"\n❌ 测试存在问题，请检查代码实现")
        
        print(f"\n🎯 最终修复要点:")
        print("✅ 窗口大小: 650x550")
        print("✅ 布局方式: Grid布局")
        print("✅ 预览限制: 8行高度")
        print("✅ 按钮固定: 底部第5行")
        print("✅ 权重控制: 只有预览可扩展")
        
        print(f"\n🚀 现在可以启动GUI测试实际效果:")
        print("python Pile_analyze_GZ_gui.py")
        
    except Exception as e:
        print(f"❌ 最终测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
