#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试AI模型加载和预测问题
"""

import os
import pickle
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def debug_model_file():
    """调试模型文件内容"""
    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
    
    print("🔍 调试AI模型文件")
    print("=" * 60)
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    try:
        # 尝试加载模型文件
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        print(f"✅ 成功加载模型文件: {model_path}")
        print(f"📊 模型数据类型: {type(model_data)}")
        
        if isinstance(model_data, dict):
            print(f"📋 模型字典键: {list(model_data.keys())}")
            
            for key, value in model_data.items():
                print(f"  {key}: {type(value)}")
                if hasattr(value, 'shape'):
                    print(f"    形状: {value.shape}")
                elif hasattr(value, '__len__') and not isinstance(value, str):
                    print(f"    长度: {len(value)}")
        
        elif hasattr(model_data, 'predict'):
            print(f"📊 这是一个sklearn模型: {type(model_data)}")
            if hasattr(model_data, 'classes_'):
                print(f"  类别: {model_data.classes_}")
            if hasattr(model_data, 'n_features_in_'):
                print(f"  输入特征数: {model_data.n_features_in_}")
        
        else:
            print(f"⚠️  未知的模型格式: {type(model_data)}")
        
        return model_data
        
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_feature_extraction():
    """测试特征提取"""
    print("\n🧪 测试特征提取")
    print("=" * 60)
    
    # 导入AI分析器
    from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
    
    analyzer = BuiltInAIAnalyzer()
    
    # 测试数据文件
    test_files = [
        "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt",
        "F:/2025/AIpile/AIpiles_final/training_data/II/KBZ1-67.txt",
        "F:/2025/AIpile/AIpiles_final/training_data/III/1-2.txt",
        "F:/2025/AIpile/AIpiles_final/training_data/IV/KBZ1-40.txt"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"\n📁 测试文件: {os.path.basename(file_path)}")
            
            try:
                # 读取数据
                df = pd.read_csv(file_path, sep='\t')
                print(f"  数据形状: {df.shape}")
                print(f"  列名: {list(df.columns)}")
                
                # 提取特征
                features, feature_names = analyzer.extract_features(df)
                print(f"  提取的特征数: {features.shape[1] if features.size > 0 else 0}")
                print(f"  特征形状: {features.shape}")
                
                if len(feature_names) > 0:
                    print(f"  特征名称示例: {feature_names[:5]}...")
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")

def test_model_prediction():
    """测试模型预测"""
    print("\n🎯 测试模型预测")
    print("=" * 60)
    
    # 加载模型
    model_data = debug_model_file()
    if model_data is None:
        return
    
    # 导入AI分析器
    from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
    
    analyzer = BuiltInAIAnalyzer()
    
    # 尝试加载模型
    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
    success = analyzer.load_models(model_path)
    
    if not success:
        print("❌ 模型加载失败")
        return
    
    # 测试预测
    test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"
    if os.path.exists(test_file):
        try:
            df = pd.read_csv(test_file, sep='\t')
            features, _ = analyzer.extract_features(df)
            
            if features.size > 0:
                print(f"✅ 特征提取成功，形状: {features.shape}")
                
                # 进行预测
                result = analyzer.predict(features)
                
                if result:
                    print(f"🎯 预测结果: {result['完整性类别']}")
                    print(f"📊 置信度: {result['ai_confidence']:.3f}")
                    print(f"📈 类别概率:")
                    for class_name, prob in result['class_probabilities'].items():
                        print(f"  {class_name}: {prob:.3f}")
                else:
                    print("❌ 预测失败")
            else:
                print("❌ 特征提取失败")
                
        except Exception as e:
            print(f"❌ 预测测试失败: {e}")
            import traceback
            traceback.print_exc()

def compare_training_and_prediction():
    """比较训练时和预测时的特征"""
    print("\n🔄 比较训练和预测特征")
    print("=" * 60)
    
    # 模拟训练时的特征提取
    from auto_train_and_classify import AutoTrainAndClassify
    
    trainer = AutoTrainAndClassify()
    trainer.training_data_dir = "F:/2025/AIpile/AIpiles_final/training_data"
    
    try:
        # 获取训练数据
        class_data_files = trainer.get_class_data_files()
        print(f"📊 找到的数据文件:")
        for pile_class, files in class_data_files.items():
            print(f"  {pile_class}: {len(files)} 个文件")
        
        # 测试一个I类桩文件
        if 'I类桩' in class_data_files and len(class_data_files['I类桩']) > 0:
            test_file = class_data_files['I类桩'][0]
            print(f"\n🧪 测试文件: {os.path.basename(test_file)}")
            
            # 读取数据
            df = pd.read_csv(test_file, sep='\t')
            print(f"  原始数据形状: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            
            # 训练时的特征提取（序列数据）
            try:
                sequence_data = trainer.preprocess_sequence_data(df)
                print(f"  训练时序列特征形状: {sequence_data.shape}")
            except Exception as e:
                print(f"  ❌ 训练时特征提取失败: {e}")
            
            # 预测时的特征提取（统计特征）
            from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
            analyzer = BuiltInAIAnalyzer()
            
            try:
                features, feature_names = analyzer.extract_features(df)
                print(f"  预测时统计特征形状: {features.shape}")
                print(f"  特征数量: {len(feature_names)}")
            except Exception as e:
                print(f"  ❌ 预测时特征提取失败: {e}")
    
    except Exception as e:
        print(f"❌ 比较测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_model_file()
    test_feature_extraction()
    test_model_prediction()
    compare_training_and_prediction()
