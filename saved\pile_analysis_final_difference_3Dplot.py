import pandas as pd
import numpy as np
import plotly.graph_objects as go
from tkinter import simpledialog

import tkinter as tk
import traceback
from tkinter import ttk, filedialog, messagebox
from collections import defaultdict

class PileAnalyzer:
    def __init__(self, master=None):
        self.root = tk.Tk() if master is None else tk.Toplevel(master)
        self.root.title('桩基完整性分析系统')

        self.config = {
            '正常': {'speed': (90.0, 1000.0), 'amp': (-100, 3)},
            '轻微畸变': {'speed': (80.0, 90.0), 'amp': (3, 6)},
            '明显畸变': {'speed': (70.0, 80.0), 'amp': (6, 12)},
            '严重畸变': {'speed': (0.0, 70.0), 'amp': (12, 100)},
            'continuous_threshold': 0.5  # 异常点连续长度阈值，单位m
        }

        self.menu_bar = tk.Menu(self.root)

        self.file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.file_menu.add_command(label='加载数据', command=self.load_data)
        self.menu_bar.add_cascade(label='文件', menu=self.file_menu)

        self.config_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.config_menu.add_command(label='参数设置', command=self.show_settings)
        self.menu_bar.add_cascade(label='配置', menu=self.config_menu)

        self.visual_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.visual_menu.add_command(label='相对波速三维可视化', command=lambda: self.plotly_visualization('speed'), state='disabled')
        self.visual_menu.add_command(label='波幅三维可视化', command=lambda: self.plotly_visualization('amplitude'), state='disabled')
        self.menu_bar.add_cascade(label='三维可视化', menu=self.visual_menu)

        self.report_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.report_menu.add_command(label='生成报告', command=self.trigger_generate_report)
        self.menu_bar.add_cascade(label='报告', menu=self.report_menu)

        self.root.config(menu=self.menu_bar)

        self.result_text = tk.Text(self.root, height=25, width=100)
        self.result_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        self.df = None
        self.analysis_result_cache = None

    def load_data(self):
        file_path = filedialog.askopenfilename(title='选择检测数据文件',
                                             filetypes=[('Text files', '*.txt')])
        if not file_path: return

        current_df = None
        expected_col_names = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
        header_to_skip = None

        try:
            try:
                with open(file_path, 'r', encoding='utf-8-sig') as f:
                    first_line = f.readline().strip()
                if first_line:
                    potential_headers = [h.strip().lower() for h in first_line.split('\t')]
                    if "depth" in potential_headers[0] or \
                       any(col_name.lower() in " ".join(potential_headers).lower() for col_name in ["speed", "amp", "1-2", "1-3", "2-3"]) or \
                       any(kw in potential_headers[0] for kw in ["m", "%"]):
                        header_to_skip = 0
            except Exception: pass

            current_df = pd.read_csv(file_path, sep='\t', header=header_to_skip,
                                     names=None if header_to_skip == 0 else expected_col_names,
                                     dtype=str, na_filter=False, comment='#')

            if header_to_skip == 0 and len(current_df.columns) == len(expected_col_names):
                current_df.columns = expected_col_names
            elif header_to_skip != 0 and len(current_df.columns) == len(expected_col_names):
                pass

            if current_df.empty or (not any(col.lower().startswith('s') or 'speed' in col.lower() for col in current_df.columns if isinstance(col, str))) or len(current_df.columns) < len(expected_col_names) - 2 :
                raise ValueError("Tab parsing failed, produced unexpected structure, or insufficient columns.")

        except (pd.errors.ParserError, ValueError, IndexError, TypeError, FileNotFoundError) as e_tab:
            header_to_skip_space = None
            try:
                try:
                    with open(file_path, 'r', encoding='utf-8-sig') as f:
                        first_line_space = f.readline().strip()
                    if first_line_space:
                        potential_headers_space = [h.strip().lower() for h in first_line_space.split()]
                        if "depth" in potential_headers_space[0] or \
                           any(col_name.lower() in " ".join(potential_headers_space).lower() for col_name in ["speed", "amp", "1-2", "1-3", "2-3"]) or \
                           any(kw in potential_headers_space[0] for kw in ["m", "%"]):
                            header_to_skip_space = 0
                except Exception: pass
                current_df = pd.read_csv(file_path, delim_whitespace=True, header=header_to_skip_space,
                                         names=None if header_to_skip_space == 0 else expected_col_names,
                                         dtype=str, na_filter=False, comment='#')
                if header_to_skip_space == 0 and len(current_df.columns) == len(expected_col_names):
                    current_df.columns = expected_col_names
                elif header_to_skip_space != 0 and len(current_df.columns) == len(expected_col_names):
                    pass

            except Exception as e_space:
                messagebox.showerror('文件加载错误', f'无法解析文件。\nTab分隔尝试失败: {str(e_tab)[:150]}\n空格分隔尝试失败: {str(e_space)[:150]}')
                self.df = None; self.analysis_result_cache = None; return

        if current_df is None or len(current_df.columns) < len(expected_col_names):
            messagebox.showerror('文件加载错误', f'未能成功读取文件数据或列数不足 ({len(current_df.columns) if current_df is not None else 0}列)。请确保文件格式正确且至少包含7列，并正确命名或能够被程序识别。');
            self.df = None; self.analysis_result_cache = None; return

        if list(current_df.columns) != expected_col_names and len(current_df.columns) == len(expected_col_names):
            current_df.columns = expected_col_names

        self.df = current_df.copy()

        try:
            self.df['Depth'] = pd.to_numeric(self.df['Depth'].astype(str).replace('[^\\d.-]', '', regex=True), errors='coerce')
            self.df.dropna(subset=['Depth'], inplace=True)
            num_cols = ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']
            for col in num_cols:
                if col not in self.df.columns:
                    messagebox.showerror('数据错误', f'加载后缺少必要的列: {col}。请检查原始文件列名是否能对应到 S1/A1, S2/A2, S3/A3 (或 1-2_Speed%/Amp%, 1-3_Speed%/Amp%, 2-3_Speed%/Amp%)。');
                    self.df = None; self.analysis_result_cache = None; return
                self.df[col] = pd.to_numeric(self.df[col].astype(str).replace('[^\\d.-]', '', regex=True), errors='coerce')

            for col in num_cols:
                is_numeric_mask = pd.to_numeric(self.df[col], errors='coerce').notna()
                out_of_range_condition = pd.Series(False, index=self.df.index)
                if is_numeric_mask.any():
                    numeric_values = pd.to_numeric(self.df.loc[is_numeric_mask, col])
                    out_of_range_condition[is_numeric_mask] = (numeric_values < 0) | (numeric_values > 2000)
                self.df.loc[out_of_range_condition, col] = pd.NA

            self.df.dropna(subset=num_cols, how='any', inplace=True)
            self.df.reset_index(drop=True, inplace=True)
        except Exception as e:
            messagebox.showerror('数据错误', f'数据清洗转换失败: {str(e)}\n{traceback.format_exc()}'); self.df = None; self.analysis_result_cache = None; return

        if self.df.empty:
            messagebox.showerror('数据错误', '有效数据为空或所有数据均不符合规范。请检查文件内容、格式及表头。'); self.df = None; self.analysis_result_cache = None
            self.result_text.delete(1.0, tk.END); self.result_text.insert(tk.END, '加载数据失败或无有效数据。\n'); return

        messagebox.showinfo('数据加载', f'成功加载并清洗 {len(self.df)} 条有效数据')

        # 启用可视化菜单项
        self.visual_menu.entryconfig('相对波速三维可视化', state='normal')
        self.visual_menu.entryconfig('波幅三维可视化', state='normal')

        try:
            self.analysis_result_cache = self.analyze()
            self._display_analysis_results()
        except Exception as e:
            messagebox.showerror('分析错误', f'分析过程出错: {str(e)}\n{traceback.format_exc()}');
            self.df = None
            self.analysis_result_cache = None
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"分析数据时发生错误: {e}\n请检查数据和参数设置。")

    def cylindrical_to_cartesian(self, depth, theta):
        """将圆柱坐标转换为笛卡尔坐标"""
        radius = 0.5  # 桩基半径
        x = radius * np.cos(theta)
        y = radius * np.sin(theta)
        z = depth
        return x, y, z

    def get_color_by_classification(self, value, data_type):
        """根据数值和分类标准确定颜色

        Args:
            value: 数值（相对波速或波幅）
            data_type: 'speed'表示相对波速，'amplitude'表示波幅

        Returns:
            color: 颜色字符串，格式为'rgb(r,g,b)'
        """
        # 定义分类标准对应的颜色
        category_colors = {
            '正常': 'rgb(0,180,0)',       # 绿色
            '轻微畸变': 'rgb(255,255,0)',  # 黄色
            '明显畸变': 'rgb(255,165,0)',  # 橙色
            '严重畸变': 'rgb(255,0,0)'     # 红色
        }

        # 确定数值所属的分类
        category = '正常'  # 默认分类

        if data_type == 'speed':
            # 相对波速分类
            for cat, ranges in self.config.items():
                if cat in ['正常', '轻微畸变', '明显畸变', '严重畸变']:
                    s_min, s_max = ranges['speed']
                    if s_min <= value < s_max:
                        category = cat
                        break
        else:  # data_type == 'amplitude'
            # 波幅分类
            for cat, ranges in self.config.items():
                if cat in ['正常', '轻微畸变', '明显畸变', '严重畸变']:
                    a_min, a_max = ranges['amp']
                    if a_min <= value < a_max:
                        category = cat
                        break

        # 获取基础颜色
        base_color = category_colors[category]

        # 根据数值在分类范围内的位置调整颜色亮度
        if category in self.config:
            if data_type == 'speed':
                s_min, s_max = self.config[category]['speed']
                # 计算数值在范围内的相对位置（0-1）
                if s_max > s_min:
                    position = (value - s_min) / (s_max - s_min)
                    # 调整颜色亮度
                    r, g, b = map(int, base_color.replace('rgb(', '').replace(')', '').split(','))
                    # 使颜色随着数值变化而变化（越接近上限越亮）
                    factor = 0.5 + 0.5 * position  # 0.5-1.0
                    r = min(255, int(r * factor))
                    g = min(255, int(g * factor))
                    b = min(255, int(b * factor))
                    return f'rgb({r},{g},{b})'
            else:  # data_type == 'amplitude'
                a_min, a_max = self.config[category]['amp']
                # 计算数值在范围内的相对位置（0-1）
                if a_max > a_min:
                    position = (value - a_min) / (a_max - a_min)
                    # 调整颜色亮度
                    r, g, b = map(int, base_color.replace('rgb(', '').replace(')', '').split(','))
                    # 使颜色随着数值变化而变化（越接近上限越亮）
                    factor = 0.5 + 0.5 * position  # 0.5-1.0
                    r = min(255, int(r * factor))
                    g = min(255, int(g * factor))
                    b = min(255, int(b * factor))
                    return f'rgb({r},{g},{b})'

        return base_color

    def plotly_visualization(self, data_type):
        if self.df is None or self.df.empty:
            messagebox.showwarning("数据错误", "请先加载有效数据")
            return

        # 检查必要的数据列
        required_columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
        missing_columns = [col for col in required_columns if col not in self.df.columns]
        if missing_columns:
            messagebox.showerror("数据错误", f"缺失必要数据列: {', '.join(missing_columns)}")
            return

        try:
            import plotly.graph_objects as go
        except ImportError:
            messagebox.showerror("依赖错误", "请安装plotly库: pip install plotly")
            return

        # 使用所有数据点作为采样点
        data_points = len(self.df)
        print(f"使用所有数据点: {data_points}")

        # 创建模式选择对话框
        mode_choice = simpledialog.askstring("可视化模式", "请选择模式(surface/scatter):", initialvalue='scatter')

        # 准备数据
        depths = self.df['Depth'].values

        # 创建三维图表
        if data_type == 'speed':
            # 相对波速可视化
            fig = go.Figure()

            # 添加三个剖面的相对波速数据
            profiles = [
                {'name': '剖面1-2', 'col': 'S1', 'y_val': 1},
                {'name': '剖面1-3', 'col': 'S2', 'y_val': 2},
                {'name': '剖面2-3', 'col': 'S3', 'y_val': 3}
            ]

            for profile in profiles:
                if mode_choice == 'surface':
                    # 创建网格数据用于surface图
                    # 为了创建有效的surface，我们需要创建一个2D网格
                    # 在Y方向上稍微扩展一点，以便创建一个可见的面
                    y_vals = np.array([profile['y_val'] - 0.2, profile['y_val'] + 0.2])
                    z_vals = depths

                    # 创建网格
                    y_grid, z_grid = np.meshgrid(y_vals, z_vals)

                    # 对于每个深度和y值，使用相同的x值（相对波速或波幅）
                    x_grid = np.tile(self.df[profile['col']].values[:, np.newaxis], (1, len(y_vals)))

                    # 为每个点根据分类标准分配颜色
                    colors = []
                    for value in self.df[profile['col']].values:
                        colors.append(self.get_color_by_classification(value, 'speed'))

                    # 创建自定义颜色刻度
                    custom_colorscale = [
                        [0, 'rgb(0,180,0)'],      # 绿色 - 正常
                        [0.33, 'rgb(255,255,0)'],  # 黄色 - 轻微畸变
                        [0.66, 'rgb(255,165,0)'],  # 橙色 - 明显畸变
                        [1, 'rgb(255,0,0)']       # 红色 - 严重畸变
                    ]

                    # 添加surface
                    fig.add_trace(go.Surface(
                        z=z_grid,
                        y=y_grid,
                        x=x_grid,
                        surfacecolor=np.tile(self.df[profile['col']].values[:, np.newaxis], (1, len(y_vals))),
                        colorscale=custom_colorscale,
                        opacity=0.8,
                        showscale=True,
                        colorbar=dict(
                            title='相对波速 (%)',
                            tickvals=[
                                self.config['严重畸变']['speed'][0],
                                self.config['明显畸变']['speed'][0],
                                self.config['轻微畸变']['speed'][0],
                                self.config['正常']['speed'][0]
                            ],
                            ticktext=['严重畸变', '明显畸变', '轻微畸变', '正常']
                        ),
                        name=profile['name']
                    ))
                else:
                    # 根据分类标准为每个点分配颜色
                    colors = []
                    for value in self.df[profile['col']].values:
                        colors.append(self.get_color_by_classification(value, 'speed'))

                    # 添加散点图
                    fig.add_trace(go.Scatter3d(
                        x=self.df[profile['col']].values,
                        y=np.ones(len(depths)) * profile['y_val'],
                        z=depths,
                        mode='markers',
                        marker=dict(
                            size=5,
                            color=colors,
                            opacity=0.8
                        ),
                        name=profile['name']
                    ))

            # 添加分类标准图例
            if mode_choice == 'scatter':
                # 为散点图添加图例
                for category, color in [('正常', 'rgb(0,180,0)'), ('轻微畸变', 'rgb(255,255,0)'),
                                       ('明显畸变', 'rgb(255,165,0)'), ('严重畸变', 'rgb(255,0,0)')]:
                    fig.add_trace(go.Scatter3d(
                        x=[None],
                        y=[None],
                        z=[None],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color=color,
                            opacity=1.0
                        ),
                        name=f"{category} ({self.config[category]['speed'][0]}-{self.config[category]['speed'][1]}%)"
                    ))

            # 设置图表布局
            fig.update_layout(
                title='桩基相对波速三维分布',
                scene=dict(
                    xaxis_title='相对波速 (%)',
                    yaxis_title='剖面',
                    yaxis=dict(
                        tickvals=[1, 2, 3],
                        ticktext=['剖面1-2', '剖面1-3', '剖面2-3']
                    ),
                    zaxis_title='深度 (m)',
                    zaxis=dict(autorange='reversed')
                ),
                width=1000,
                height=800,
                legend=dict(
                    title="分类标准",
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                )
            )

        else:  # data_type == 'amplitude'
            # 波幅可视化
            fig = go.Figure()

            # 添加三个剖面的波幅数据
            profiles = [
                {'name': '剖面1-2', 'col': 'A1', 'y_val': 1},
                {'name': '剖面1-3', 'col': 'A2', 'y_val': 2},
                {'name': '剖面2-3', 'col': 'A3', 'y_val': 3}
            ]

            for profile in profiles:
                if mode_choice == 'surface':
                    # 创建网格数据用于surface图
                    # 为了创建有效的surface，我们需要创建一个2D网格
                    # 在Y方向上稍微扩展一点，以便创建一个可见的面
                    y_vals = np.array([profile['y_val'] - 0.2, profile['y_val'] + 0.2])
                    z_vals = depths

                    # 创建网格
                    y_grid, z_grid = np.meshgrid(y_vals, z_vals)

                    # 对于每个深度和y值，使用相同的x值（相对波速或波幅）
                    x_grid = np.tile(self.df[profile['col']].values[:, np.newaxis], (1, len(y_vals)))

                    # 为每个点根据分类标准分配颜色
                    colors = []
                    for value in self.df[profile['col']].values:
                        colors.append(self.get_color_by_classification(value, 'amplitude'))

                    # 创建自定义颜色刻度
                    custom_colorscale = [
                        [0, 'rgb(0,180,0)'],      # 绿色 - 正常
                        [0.33, 'rgb(255,255,0)'],  # 黄色 - 轻微畸变
                        [0.66, 'rgb(255,165,0)'],  # 橙色 - 明显畸变
                        [1, 'rgb(255,0,0)']       # 红色 - 严重畸变
                    ]

                    # 添加surface
                    fig.add_trace(go.Surface(
                        z=z_grid,
                        y=y_grid,
                        x=x_grid,
                        surfacecolor=np.tile(self.df[profile['col']].values[:, np.newaxis], (1, len(y_vals))),
                        colorscale=custom_colorscale,
                        opacity=0.8,
                        showscale=True,
                        colorbar=dict(
                            title='波幅 (dB)',
                            tickvals=[
                                self.config['严重畸变']['amp'][0],
                                self.config['明显畸变']['amp'][0],
                                self.config['轻微畸变']['amp'][0],
                                self.config['正常']['amp'][0]
                            ],
                            ticktext=['严重畸变', '明显畸变', '轻微畸变', '正常']
                        ),
                        name=profile['name']
                    ))
                else:
                    # 根据分类标准为每个点分配颜色
                    colors = []
                    for value in self.df[profile['col']].values:
                        colors.append(self.get_color_by_classification(value, 'amplitude'))

                    # 添加散点图
                    fig.add_trace(go.Scatter3d(
                        x=self.df[profile['col']].values,
                        y=np.ones(len(depths)) * profile['y_val'],
                        z=depths,
                        mode='markers',
                        marker=dict(
                            size=5,
                            color=colors,
                            opacity=0.8
                        ),
                        name=profile['name']
                    ))

            # 添加分类标准图例
            if mode_choice == 'scatter':
                # 为散点图添加图例
                for category, color in [('正常', 'rgb(0,180,0)'), ('轻微畸变', 'rgb(255,255,0)'),
                                       ('明显畸变', 'rgb(255,165,0)'), ('严重畸变', 'rgb(255,0,0)')]:
                    fig.add_trace(go.Scatter3d(
                        x=[None],
                        y=[None],
                        z=[None],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color=color,
                            opacity=1.0
                        ),
                        name=f"{category} ({self.config[category]['amp'][0]}-{self.config[category]['amp'][1]}dB)"
                    ))

            # 设置图表布局
            fig.update_layout(
                title='桩基波幅三维分布',
                scene=dict(
                    xaxis_title='波幅 (dB)',
                    yaxis_title='剖面',
                    yaxis=dict(
                        tickvals=[1, 2, 3],
                        ticktext=['剖面1-2', '剖面1-3', '剖面2-3']
                    ),
                    zaxis_title='深度 (m)',
                    zaxis=dict(autorange='reversed')
                ),
                width=1000,
                height=800,
                legend=dict(
                    title="分类标准",
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                )
            )

        # 添加异常捕获机制
        try:
            fig.show()
        except Exception as e:
            print(f"可视化失败: {str(e)}")
            messagebox.showerror("可视化错误", f"三维可视化失败: {str(e)}")

    def _display_analysis_results(self):
        if self.analysis_result_cache is None:
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "没有分析结果可显示。\n")
            return

        res = self.analysis_result_cache
        self.result_text.delete(1.0, tk.END)

        self.result_text.insert(tk.END, f"桩基完整性类别: {res.get('完整性类别','N/A')}\n")
        self.result_text.insert(tk.END, f"判定概述: {res.get('overall_reasoning_intro','无概述')}\n")
        self.result_text.insert(tk.END, f"详细判定依据: {res.get('detailed_overall_reason','无详细依据')}\n\n")

        self.result_text.insert(tk.END, '各深度异常详情:\n')
        if not res.get("异常区域"):
            self.result_text.insert(tk.END, '  无异常点\n')
        else:
            sorted_depths_float = sorted(res["异常区域"].keys())
            per_depth_class_details = res.get("per_depth_classification_details", {})

            for depth_key_float in sorted_depths_float:
                items_at_depth = res["异常区域"][depth_key_float]
                self.result_text.insert(tk.END, f'深度 {depth_key_float:.2f}m:\n')

                depth_class_info = per_depth_class_details.get(depth_key_float)
                if depth_class_info:
                    self.result_text.insert(tk.END, f"  该深度综合判定: {depth_class_info['class']} ({depth_class_info['reason']})\n")

                depth_metrics_for_display = res.get("depth_type_specific_metrics", {})
                metrics_at_this_depth = depth_metrics_for_display.get(depth_key_float, {})


                for item in items_at_depth:
                    cl = f"{item.get('连续长度', 0):.2f}m"
                    s, a = item.get("声速", "N/A"), item.get("波幅", "N/A")
                    s_t, a_t = (f"{v:.1f}" if isinstance(v, (int, float)) else str(v) for v in (s, a))

                    item_type = item["类型"]
                    type_specific_hr = 0.0
                    if item_type == '轻微畸变': type_specific_hr = metrics_at_this_depth.get('hr_light', 0.0)
                    elif item_type == '明显畸变': type_specific_hr = metrics_at_this_depth.get('hr_moderate', 0.0)
                    elif item_type == '严重畸变': type_specific_hr = metrics_at_this_depth.get('hr_severe', 0.0)
                    hr_display = f"{type_specific_hr*100:.0f}%"

                    self.result_text.insert(tk.END, f'    剖面{item["剖面"]}: {item_type} (声速:{s_t} 波幅:{a_t}, 连续:{cl}, 同类型横向:{hr_display})\n')
            self.result_text.insert(tk.END, "\n")


    def show_settings(self):
        self.settings_win = tk.Toplevel(self.root); self.settings_win.title('参数设置'); self.entries = {}

        r_idx = 0
        categories_to_display = ['正常', '轻微畸变', '明显畸变', '严重畸变']
        for r_idx, cat in enumerate(categories_to_display):
            tk.Label(self.settings_win, text=cat).grid(row=r_idx*2, column=0, padx=5, pady=(15 if r_idx==0 else 5, 5), sticky='w')
            for i, p_name in enumerate(['声速', '波幅']):
                tk.Label(self.settings_win, text=p_name).grid(row=r_idx*2, column=i*2+1, sticky='sw', padx=5, pady=(15 if r_idx==0 else 5,0))
                p_key = 'speed' if p_name == '声速' else 'amp'
                min_sv, max_sv = (tk.StringVar(value=str(self.config[cat][p_key][j])) for j in (0,1))

                entry_min = tk.Entry(self.settings_win, textvariable=min_sv, width=7)
                entry_min.grid(row=r_idx*2+1, column=i*2+1, padx=(5,0), sticky='w')

                tk.Label(self.settings_win, text="-").grid(row=r_idx*2+1, column=i*2+1, sticky='e', padx=(0,0))

                entry_max = tk.Entry(self.settings_win, textvariable=max_sv, width=7)
                entry_max.grid(row=r_idx*2+1, column=i*2+2, padx=(0,5), sticky='w')

                self.entries[f'{cat}_{p_key}'] = (min_sv, max_sv)

        next_row_for_global = (r_idx + 1) * 2

        tk.Label(self.settings_win, text='全局参数').grid(row=next_row_for_global, column=0, columnspan=2, padx=5, pady=(15,5), sticky='w')

        next_row_for_global +=1
        tk.Label(self.settings_win, text='异常点连续长度阈值(m):').grid(row=next_row_for_global, column=0, columnspan=2, sticky='w', padx=5, pady=2)

        self.ct_entry = tk.Entry(self.settings_win, width=7)
        self.ct_entry.insert(0, str(self.config['continuous_threshold']))
        self.ct_entry.grid(row=next_row_for_global, column=2, padx=5, pady=2, sticky='w')

        next_row_for_global +=1
        tk.Button(self.settings_win, text='保存', command=self.save_settings).grid(row=next_row_for_global, column=0, columnspan=3, pady=20)

    def save_settings(self):
        try:
            new_cfg = {}
            expected_categories = ['正常', '轻微畸变', '明显畸变', '严重畸变']

            for cat_key in expected_categories:
                if f'{cat_key}_speed' not in self.entries:
                    messagebox.showerror('内部错误', f'参数条目设置不一致: {cat_key}')
                    return

                new_cfg[cat_key] = {'speed': None, 'amp': None}
                for p_key in ['speed', 'amp']:
                    entry_key = f'{cat_key}_{p_key}'
                    if entry_key not in self.entries:
                        messagebox.showerror('内部错误', f'未找到参数条目: {entry_key}')
                        return

                    min_sv, max_sv = self.entries[entry_key]
                    min_v, max_v = float(min_sv.get()), float(max_sv.get())
                    if min_v >= max_v:
                        param_name_chn = "声速" if p_key == "speed" else "波幅"
                        messagebox.showerror('输入错误', f'{cat_key}类别的"{param_name_chn}"参数中最小值 ({min_v}) 必须小于最大值 ({max_v})。')
                        return
                    new_cfg[cat_key][p_key] = (min_v, max_v)

            try:
                ct_value = float(self.ct_entry.get())
                if ct_value <= 0:
                    raise ValueError('阈值必须大于0')
                new_cfg['continuous_threshold'] = ct_value
            except ValueError as e:
                messagebox.showerror('输入错误', f'连续长度阈值错误: {str(e)}')
                return

            self.config = new_cfg
        except ValueError:
            messagebox.showerror('输入错误', '参数值必须是有效数字。请检查所有输入框。')
            return

        self.settings_win.destroy()
        messagebox.showinfo('设置已保存', '参数配置已更新。')

        if self.df is not None:
            try:
                self.analysis_result_cache = self.analyze()
                self._display_analysis_results()
                self.result_text.insert(tk.END, "\n参数更新后，重新分析完成。\n")
            except Exception as e:
                messagebox.showerror('重新分析错误', f'使用新参数重新分析数据时出错: {str(e)}\n{traceback.format_exc()}')
                self.analysis_result_cache = None
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, f"使用新参数重新分析数据时出错: {e}\n请检查参数或重新加载数据。")

    def classify_anomaly(self, speed, amp):
        if not (isinstance(speed,(int,float)) and isinstance(amp,(int,float))): return '数据无效'
        s_map={'正常':0,'轻微畸变':1,'明显畸变':2,'严重畸变':3}; o_types=['严重畸变','明显畸变','轻微畸变','正常']
        s_cat,a_cat='正常','正常'

        for lvl in o_types:
            if lvl in self.config and 'speed' in self.config[lvl]:
                s_min,s_max=self.config[lvl]['speed']
                if s_min <= speed < s_max :
                    s_cat=lvl
                    break

        for lvl in o_types:
            if lvl in self.config and 'amp' in self.config[lvl]:
                a_min,a_max=self.config[lvl]['amp']
                if a_min <= amp < a_max:
                    a_cat=lvl
                    break

        return s_cat if s_map.get(s_cat,-1) > s_map.get(a_cat,-1) else a_cat

    def analyze(self):
        if self.df is None or self.df.empty:
            return {
                '完整性类别': 'N/A (无数据)', '异常区域': {},
                '最大异常比例': {'type': '', 'ratio': 0, 'depth_m': None},
                'depth_type_specific_metrics': {},
                'per_depth_classification_details': {},
                'overall_reasoning_intro': '未加载数据。',
                'detailed_overall_reason': '请先加载数据文件进行分析。'
            }

        POINT_SPACING = 0.1
        MAX_CONTINUOUS_DEPTH_STEP = POINT_SPACING * 1.25
        CONTINUOUS_LENGTH_THRESHOLD = self.config['continuous_threshold']

        raw_anomalies_by_depth = defaultdict(list)
        profile_data_for_continuity = {p:[] for p in range(1,4)}

        for _, row in self.df.iterrows():
            depth = float(row['Depth'])
            for i in range(1,4):
                s_col, a_col = f'S{i}', f'A{i}'
                if s_col not in row or a_col not in row: continue

                s_val, a_val = row[s_col], row[a_col]
                if pd.isna(s_val) or pd.isna(a_val): continue

                s = float(s_val)
                a = float(a_val)

                atype = self.classify_anomaly(s,a)
                if atype not in ['正常','数据无效']:
                    item={'剖面':str(i),'类型':atype,'声速':s,'波幅':a,'深度':depth}
                    raw_anomalies_by_depth[depth].append(item)
                    profile_data_for_continuity[i].append({'depth':depth,'type':atype,'item_ref':item})

        per_depth_classification_details = {}
        overall_reasoning_intro = "桩身完整，所有检测点均未发现符合II类及以上标准的缺陷。" # Default for I-class
        detailed_overall_reason = "根据提供的判别标准，桩体完整性为I类。" # Default for I-class

        if not raw_anomalies_by_depth:
            return {
                '完整性类别': 'I类桩', '异常区域': {},
                '最大异常比例': {'type': '', 'ratio': 0, 'depth_m': None},
                'depth_type_specific_metrics': {}, # Should be empty
                'per_depth_classification_details': per_depth_classification_details, # Empty
                'overall_reasoning_intro': overall_reasoning_intro,
                'detailed_overall_reason': detailed_overall_reason
            }

        for p_idx in range(1,4):
            anoms_in_prof = sorted(profile_data_for_continuity[p_idx],key=lambda x:x['depth'])
            if not anoms_in_prof: continue
            groups, cur_group = [], []
            for ameta in anoms_in_prof:
                if not cur_group or (ameta['type']==cur_group[-1]['type'] and (ameta['depth']-cur_group[-1]['depth'])<=MAX_CONTINUOUS_DEPTH_STEP):
                    cur_group.append(ameta)
                else: groups.append(cur_group); cur_group=[ameta]
            if cur_group: groups.append(cur_group)
            for grp in groups:
                length = (len(grp) - 1) * POINT_SPACING if len(grp) > 1 else POINT_SPACING
                for ameta in grp: ameta['item_ref']['连续长度'] = length

        anomalies_for_report = defaultdict(list)
        all_individual_anomalies = []
        depth_type_specific_metrics = {}

        for depth_float, items_at_d in raw_anomalies_by_depth.items():
            type_counts={'轻微畸变':0,'明显畸变':0,'严重畸变':0}
            for item in items_at_d:
                if item['类型'] in type_counts: type_counts[item['类型']] += 1

            current_depth_metrics_calc = {
                'hr_light':type_counts['轻微畸变']/3.0,
                'hr_moderate':type_counts['明显畸变']/3.0,
                'hr_severe':type_counts['严重畸变']/3.0
            }
            depth_type_specific_metrics[depth_float] = current_depth_metrics_calc

            for item in items_at_d:
                if '连续长度' not in item: item['连续长度']=POINT_SPACING
                anomalies_for_report[depth_float].append(item)
                all_individual_anomalies.append(item) # Used to check if any anomaly exists at all

        # Per-depth classification
        for depth_float in sorted(anomalies_for_report.keys()):
            items_at_this_depth = anomalies_for_report[depth_float]
            metrics_at_this_depth = depth_type_specific_metrics.get(depth_float, {})

            current_depth_class = 'I类桩'
            reason_parts = []
            is_classified_at_depth = False

            # Calculate type-specific max continuous lengths for this depth
            max_cl_severe, max_cl_moderate, max_cl_light = 0, 0, 0
            for item in items_at_this_depth:
                cl = item.get('连续长度', POINT_SPACING)
                if item['类型'] == '严重畸变': max_cl_severe = max(max_cl_severe, cl)
                elif item['类型'] == '明显畸变': max_cl_moderate = max(max_cl_moderate, cl)
                elif item['类型'] == '轻微畸变': max_cl_light = max(max_cl_light, cl)

            # Boolean flags for conditions
            has_severe_type = any(item['类型'] == '严重畸变' for item in items_at_this_depth)
            has_moderate_type = any(item['类型'] == '明显畸变' for item in items_at_this_depth)
            has_light_type = any(item['类型'] == '轻微畸变' for item in items_at_this_depth)

            is_cl_long_severe = max_cl_severe > CONTINUOUS_LENGTH_THRESHOLD
            is_cl_long_moderate = max_cl_moderate > CONTINUOUS_LENGTH_THRESHOLD
            is_cl_long_light = max_cl_light > CONTINUOUS_LENGTH_THRESHOLD

            hr_severe = metrics_at_this_depth.get('hr_severe', 0.0)
            hr_moderate = metrics_at_this_depth.get('hr_moderate', 0.0)
            hr_light = metrics_at_this_depth.get('hr_light', 0.0)

            hr_severe_wide = hr_severe > 0.5
            hr_moderate_wide = hr_moderate > 0.5
            hr_light_wide = hr_light > 0.5

            # IV类桩
            if has_severe_type and (is_cl_long_severe or hr_severe_wide):
                current_depth_class = 'IV类桩'
                reason_parts.append("存在'严重畸变'类型缺陷")
                if is_cl_long_severe: reason_parts.append(f"其连续长度({max_cl_severe:.2f}m)>阈值({CONTINUOUS_LENGTH_THRESHOLD:.2f}m)")
                if hr_severe_wide: reason_parts.append(f"其横向比例({hr_severe:.0%})>50%")
                is_classified_at_depth = True

            # III类桩
            if not is_classified_at_depth:
                if (has_moderate_type and (is_cl_long_moderate or hr_moderate_wide)) or \
                   (has_severe_type and not is_cl_long_severe and not hr_severe_wide):
                    current_depth_class = 'III类桩'
                    if has_moderate_type and (is_cl_long_moderate or hr_moderate_wide):
                        reason_parts.append("存在'明显畸变'类型缺陷")
                        if is_cl_long_moderate: reason_parts.append(f"其连续长度({max_cl_moderate:.2f}m)>阈值({CONTINUOUS_LENGTH_THRESHOLD:.2f}m)")
                        if hr_moderate_wide: reason_parts.append(f"其横向比例({hr_moderate:.0%})>50%")
                    elif has_severe_type: # This implies (not is_cl_long_severe and not hr_severe_wide)
                        reason_parts.append(f"存在'严重畸变'类型缺陷，但其连续长度({max_cl_severe:.2f}m)≤阈值且横向比例({hr_severe:.0%})≤50%")
                    is_classified_at_depth = True

            # II类桩
            if not is_classified_at_depth:
                if (has_light_type and (is_cl_long_light or hr_light_wide)) or \
                   (has_moderate_type and not is_cl_long_moderate and not hr_moderate_wide):
                    current_depth_class = 'II类桩'
                    if has_light_type and (is_cl_long_light or hr_light_wide):
                        reason_parts.append("存在'轻微畸变'类型缺陷")
                        if is_cl_long_light: reason_parts.append(f"其连续长度({max_cl_light:.2f}m)>阈值({CONTINUOUS_LENGTH_THRESHOLD:.2f}m)")
                        if hr_light_wide: reason_parts.append(f"其横向比例({hr_light:.0%})>50%")
                    elif has_moderate_type: # This implies (not is_cl_long_moderate and not hr_moderate_wide)
                        reason_parts.append(f"存在'明显畸变'类型缺陷，但其连续长度({max_cl_moderate:.2f}m)≤阈值且横向比例({hr_moderate:.0%})≤50%")
                    is_classified_at_depth = True

            # I类桩 (special case for light anomalies)
            if not is_classified_at_depth:
                if has_light_type and not is_cl_long_light and not hr_light_wide: # CL <= thresh AND HR <= 50%
                    current_depth_class = 'I类桩'
                    reason_parts.append(f"存在'轻微畸变'类型缺陷，但其连续长度({max_cl_light:.2f}m)≤阈值且横向比例({hr_light:.0%})≤50%")
                    is_classified_at_depth = True
                elif not has_severe_type and not has_moderate_type and not has_light_type:
                    # This case should not be reached if depth_float is from anomalies_for_report,
                    # as it implies no '畸变' type anomalies were found at this depth.
                    # However, if it were possible, it would be I-class.
                    current_depth_class = 'I类桩'
                    reason_parts.append("声速、波幅均处于正常范围") # Or "无明显异常"
                    is_classified_at_depth = True


            final_reason_str = "; ".join(reason_parts) if reason_parts else "该深度所有指标均在I类桩范围内或未满足II/III/IV类缺陷标准。"
            if not is_classified_at_depth and (has_severe_type or has_moderate_type or has_light_type):
                # Fallback if specific rules weren't met but anomalies exist, should be rare with new logic
                current_depth_class = 'II类桩' # Default to II if unclassified anomalies exist
                final_reason_str = "存在异常点，但未完全匹配特定的II/III/IV类高级别缺陷定义，按II类处理。"


            per_depth_classification_details[depth_float] = {
                'class': current_depth_class, 'reason': final_reason_str,
                'max_cl_severe': max_cl_severe, 'max_cl_moderate': max_cl_moderate, 'max_cl_light': max_cl_light,
                'is_cl_long_severe': is_cl_long_severe, 'is_cl_long_moderate': is_cl_long_moderate, 'is_cl_long_light': is_cl_long_light,
                'hr_severe': hr_severe, 'hr_moderate': hr_moderate, 'hr_light': hr_light,
                'hr_severe_wide': hr_severe_wide, 'hr_moderate_wide': hr_moderate_wide, 'hr_light_wide': hr_light_wide,
                'has_severe_type': has_severe_type, 'has_moderate_type': has_moderate_type, 'has_light_type': has_light_type
            }

        # Determine Overall Pile Classification
        class_severity_map = {'I类桩': 1, 'II类桩': 2, 'III类桩': 3, 'IV类桩': 4}
        class_severity_map_inv = {v: k for k, v in class_severity_map.items()}

        overall_pile_class_val = 1
        critical_depth_for_overall_class = None

        if not per_depth_classification_details and all_individual_anomalies:
            overall_pile_class_val = 2
            overall_reasoning_intro = f"桩体综合判定为 {class_severity_map_inv.get(overall_pile_class_val, 'II类桩')}。"
            detailed_overall_reason = "桩体存在零星异常点，但未在特定深度形成可按新标准分类的缺陷区域。"
        elif per_depth_classification_details:
            for depth, details in per_depth_classification_details.items():
                current_depth_severity_val = class_severity_map.get(details['class'], 1)
                if current_depth_severity_val > overall_pile_class_val:
                    overall_pile_class_val = current_depth_severity_val
                    critical_depth_for_overall_class = depth

            overall_pile_class_str_final = class_severity_map_inv.get(overall_pile_class_val, 'I类桩')

            if critical_depth_for_overall_class is not None:
                crit_details = per_depth_classification_details[critical_depth_for_overall_class]
                overall_reasoning_intro = f"桩体最严重缺陷判定为 {overall_pile_class_str_final}。"
                detailed_reason_parts = [f"主要判定依据源于深度 {critical_depth_for_overall_class:.2f}m (该深度判定为{crit_details['class']}): {crit_details['reason']}."]

                key_metrics_parts = []
                if crit_details['has_severe_type']:
                    key_metrics_parts.append(f"严重畸变(CL:{crit_details['max_cl_severe']:.2f}m, HR:{crit_details['hr_severe']:.0%})")
                if crit_details['has_moderate_type']:
                    key_metrics_parts.append(f"明显畸变(CL:{crit_details['max_cl_moderate']:.2f}m, HR:{crit_details['hr_moderate']:.0%})")
                if crit_details['has_light_type']:
                    key_metrics_parts.append(f"轻微畸变(CL:{crit_details['max_cl_light']:.2f}m, HR:{crit_details['hr_light']:.0%})")

                if key_metrics_parts:
                    detailed_reason_parts.append("临界深度关键指标: " + "; ".join(key_metrics_parts) + ".")
                detailed_overall_reason = " ".join(detailed_reason_parts)

            elif overall_pile_class_val > 1 :
                overall_reasoning_intro = f"桩体综合判定为 {overall_pile_class_str_final}。"
                detailed_overall_reason = "桩体存在多处缺陷或异常，未形成单一主导的严重缺陷点，按最严重等级综合评定。"
                example_depth = next(iter(d for d, det in per_depth_classification_details.items() if class_severity_map.get(det['class']) == overall_pile_class_val), None)
                if example_depth:
                    ex_details = per_depth_classification_details[example_depth]
                    detailed_overall_reason += f" 例如，深度 {example_depth:.2f}m 判定为 {ex_details['class']} ({ex_details['reason']})."
            # else: I-class already handled by initialization
        # If per_depth_classification_details is empty AND all_individual_anomalies is also empty, it's I-class (covered by initial empty raw_anomalies_by_depth check)

        # Old max_anom_info logic (can be phased out or re-evaluated)
        sev_map_judge={'严重畸变':3,'明显畸变':2,'轻微畸变':1,'':0, '正常': -1, '数据无效': -2}
        primary_anomaly_depth_for_summary = None
        primary_anomaly_type_str_for_summary = ""
        primary_anomaly_type_specific_hr_for_summary = -1.0
        overall_most_severe_type_val_old = -1

        if anomalies_for_report: # anomalies_for_report contains actual anomalies
            sorted_depths_for_summary_eval = sorted(anomalies_for_report.keys())
            for d_float in sorted_depths_for_summary_eval:
                items_at_this_depth_summary = anomalies_for_report[d_float]
                metrics_at_this_depth_summary = depth_type_specific_metrics.get(d_float, {})
                for item_summary in items_at_this_depth_summary:
                    current_item_type_str = item_summary['类型']
                    current_item_type_val = sev_map_judge.get(current_item_type_str, 0) # Use 0 for unknown types
                    # current_item_depth = d_float # Not used here
                    current_item_type_specific_hr = 0.0
                    if current_item_type_str == '严重畸变': current_item_type_specific_hr = metrics_at_this_depth_summary.get('hr_severe', 0.0)
                    elif current_item_type_str == '明显畸变': current_item_type_specific_hr = metrics_at_this_depth_summary.get('hr_moderate', 0.0)
                    elif current_item_type_str == '轻微畸变': current_item_type_specific_hr = metrics_at_this_depth_summary.get('hr_light', 0.0)

                    if current_item_type_val > overall_most_severe_type_val_old:
                        overall_most_severe_type_val_old = current_item_type_val
                        primary_anomaly_type_str_for_summary = current_item_type_str
                        primary_anomaly_depth_for_summary = d_float
                        primary_anomaly_type_specific_hr_for_summary = current_item_type_specific_hr
                    elif current_item_type_val == overall_most_severe_type_val_old:
                        if current_item_type_specific_hr > primary_anomaly_type_specific_hr_for_summary:
                            primary_anomaly_depth_for_summary = d_float # Update depth if HR is higher for same severity
                            primary_anomaly_type_specific_hr_for_summary = current_item_type_specific_hr

        max_anom_info_calc = {
            'type': primary_anomaly_type_str_for_summary,
            'ratio': primary_anomaly_type_specific_hr_for_summary if primary_anomaly_type_specific_hr_for_summary >= 0.0 else 0.0,
            'depth_m': primary_anomaly_depth_for_summary
        }

        return {
            '完整性类别': class_severity_map_inv.get(overall_pile_class_val, 'I类桩'),
            '异常区域': dict(anomalies_for_report),
            '最大异常比例': max_anom_info_calc,
            'depth_type_specific_metrics': depth_type_specific_metrics,
            'per_depth_classification_details': per_depth_classification_details,
            'overall_reasoning_intro': overall_reasoning_intro,
            'detailed_overall_reason': detailed_overall_reason
        }

    def trigger_generate_report(self):
        if not self.analysis_result_cache: messagebox.showwarning("报告生成","请先加载并分析数据。"); return
        path = filedialog.asksaveasfilename(defaultextension=".md",filetypes=[("MD","*.md"),("TXT","*.txt"),("All","*.*")],title="保存报告")
        if not path: return
        try:
            content=self.generate_report_content(self.analysis_result_cache)
            with open(path,'w',encoding='utf-8') as f: f.write(content)
            messagebox.showinfo("报告生成",f"报告已保存: {path}")
        except Exception as e: messagebox.showerror("报告生成失败",f"无法生成或保存报告: {str(e)}\n{traceback.format_exc()}")

    def generate_report_content(self, result):
        lines = [f"# 桩基完整性分析报告"]

        lines.append(f"## 结论")
        lines.append(f"- **桩基完整性类别**: {result.get('完整性类别','N/A')}")
        lines.append(f"- **判定概述**: {result.get('overall_reasoning_intro','无概述')}")
        lines.append(f"- **详细判定依据**: {result.get('detailed_overall_reason','无详细依据')}")

        lines.append(f"\n## 各深度异常详情")
        anomalies_data=result.get('异常区域',{})
        per_depth_class_details = result.get('per_depth_classification_details', {})
        depth_metrics_for_report = result.get('depth_type_specific_metrics', {})

        if not anomalies_data: lines.append("无明显异常点。")
        else:
            sorted_depths_float = sorted(anomalies_data.keys())

            for depth_key_float in sorted_depths_float:
                lines.append(f"\n### 深度 {depth_key_float:.2f}m:")

                depth_class_info = per_depth_class_details.get(depth_key_float)
                if depth_class_info:
                    lines.append(f"  - **该深度综合判定**: {depth_class_info['class']} ({depth_class_info['reason']})")

                metrics_at_this_depth = depth_metrics_for_report.get(depth_key_float, {})
                for item in anomalies_data[depth_key_float]:
                    s,a=item.get('声速','N/A'),item.get('波幅','N/A')
                    s_str=f"{s:.1f}" if isinstance(s,(int,float)) else str(s)
                    a_str=f"{a:.1f}" if isinstance(a,(int,float)) else str(a)
                    cl_str=f"{item.get('连续长度',0):.2f}m"

                    item_type = item["类型"]
                    type_specific_hr_val = 0.0
                    if item_type == '轻微畸变': type_specific_hr_val = metrics_at_this_depth.get('hr_light', 0.0)
                    elif item_type == '明显畸变': type_specific_hr_val = metrics_at_this_depth.get('hr_moderate', 0.0)
                    elif item_type == '严重畸变': type_specific_hr_val = metrics_at_this_depth.get('hr_severe', 0.0)
                    hr_display_str = f"{type_specific_hr_val*100:.0f}%"

                    lines.append(f"  - 剖面 {item.get('剖面','N/A')}: {item_type} (声速:{s_str}, 波幅:{a_str}, 连续:{cl_str}, 同类型横向:{hr_display_str})")
        return "\n".join(lines)

if __name__ == '__main__':
    app = PileAnalyzer()
    app.root.mainloop()