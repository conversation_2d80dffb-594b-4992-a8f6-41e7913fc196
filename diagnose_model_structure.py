#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
诊断模型结构问题
Diagnose Model Structure Issues
"""

import os
import pickle
import traceback

def check_model_file_structure(model_path):
    """检查模型文件结构"""
    print(f"🔍 检查模型文件结构: {model_path}")
    print("=" * 80)
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        print(f"✅ 模型文件加载成功")
        print(f"📊 模型数据类型: {type(model_data)}")
        
        if isinstance(model_data, dict):
            print(f"📋 模型字典键:")
            for key in model_data.keys():
                value = model_data[key]
                print(f"  - {key}: {type(value)}")
                
                # 检查分类器
                if key == 'classifier_model' and value is not None:
                    print(f"    ✅ 分类器存在: {type(value)}")
                    if hasattr(value, 'classes_'):
                        print(f"    📊 类别: {value.classes_}")
                    if hasattr(value, 'predict'):
                        print(f"    🎯 支持预测")
                elif key == 'classifier_model' and value is None:
                    print(f"    ❌ 分类器为None")
        
        return model_data
        
    except Exception as e:
        print(f"❌ 模型文件加载失败: {e}")
        traceback.print_exc()
        return None

def find_available_models():
    """查找可用的模型文件"""
    print(f"\n🔍 查找可用的模型文件")
    print("=" * 80)
    
    model_dirs = [
        "advanced_models",
        "models", 
        ".",
        "training_results"
    ]
    
    found_models = []
    
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            print(f"📁 检查目录: {model_dir}")
            
            for file in os.listdir(model_dir):
                if file.endswith('.pkl'):
                    model_path = os.path.join(model_dir, file)
                    print(f"  📄 找到模型: {file}")
                    found_models.append(model_path)
    
    return found_models

def check_enhanced_models():
    """检查增强训练模型"""
    print(f"\n🔍 检查增强训练模型")
    print("=" * 80)
    
    enhanced_models = [
        "enhanced_quick_model.pkl",
        "enhanced_advanced_model.pkl", 
        "enhanced_research_model.pkl",
        "enhanced_94_percent_model.pkl"
    ]
    
    working_models = []
    
    for model_file in enhanced_models:
        if os.path.exists(model_file):
            print(f"✅ 找到增强模型: {model_file}")
            
            # 检查模型结构
            model_data = check_model_file_structure(model_file)
            if model_data and 'model' in model_data:
                working_models.append(model_file)
                print(f"  ✅ 模型结构正常")
            else:
                print(f"  ⚠️ 模型结构异常")
        else:
            print(f"❌ 增强模型不存在: {model_file}")
    
    return working_models

def create_compatible_model():
    """创建兼容的模型文件"""
    print(f"\n🔧 创建兼容的模型文件")
    print("=" * 80)
    
    try:
        # 检查是否有增强训练器可用
        from enhanced_training_system import Enhanced94PercentTrainer
        
        trainer = Enhanced94PercentTrainer()
        
        # 检查训练数据
        if os.path.exists('training_data'):
            print("📊 使用训练数据创建新模型...")
            
            # 准备数据
            X, y = trainer.prepare_data('training_data')
            print(f"✅ 数据准备完成: {X.shape[0]} 样本, {X.shape[1]} 特征")
            
            # 训练模型
            def progress_callback(progress, status):
                if progress % 25 == 0:
                    print(f"  {progress:3.0f}% - {status}")
            
            results = trainer.train_94_percent_model(X, y, progress_callback)
            print(f"✅ 训练完成，准确率: {results['accuracy']:.2%}")
            
            # 保存为兼容格式
            compatible_model_path = "compatible_ai_model.pkl"
            
            # 创建兼容的模型结构
            compatible_model = {
                'classifier_model': trainer.model,  # 集成模型
                'scaler': trainer.scaler,
                'feature_selector': trainer.feature_selector,
                'feature_extractor': trainer.feature_extractor,
                'model_type': 'enhanced_compatible',
                'accuracy': results['accuracy'],
                'feature_count': results['n_features']
            }
            
            with open(compatible_model_path, 'wb') as f:
                pickle.dump(compatible_model, f)
            
            print(f"✅ 兼容模型已保存: {compatible_model_path}")
            
            # 验证兼容模型
            verify_model = check_model_file_structure(compatible_model_path)
            if verify_model and 'classifier_model' in verify_model and verify_model['classifier_model'] is not None:
                print(f"✅ 兼容模型验证成功")
                return compatible_model_path
            else:
                print(f"❌ 兼容模型验证失败")
                return None
        
        else:
            print("❌ 训练数据不存在，无法创建新模型")
            return None
            
    except Exception as e:
        print(f"❌ 创建兼容模型失败: {e}")
        traceback.print_exc()
        return None

def fix_ai_v2_model_loading():
    """修复AI V2模型加载问题"""
    print(f"\n🔧 修复AI V2模型加载问题")
    print("=" * 80)
    
    try:
        from model_manager import get_model_manager
        
        model_manager = get_model_manager()
        
        # 检查当前注册的模型
        models = model_manager.get_available_models()
        print(f"📊 当前注册模型数: {len(models)}")
        
        problem_models = []
        working_models = []
        
        for key, model_info in models.items():
            print(f"\n📋 检查模型: {model_info.name}")
            print(f"  - 路径: {model_info.path}")
            print(f"  - 类型: {model_info.model_type}")
            
            # 检查模型文件
            if os.path.exists(model_info.path):
                model_data = check_model_file_structure(model_info.path)
                
                if model_data:
                    if isinstance(model_data, dict) and 'classifier_model' in model_data and model_data['classifier_model'] is not None:
                        print(f"  ✅ 模型结构正常")
                        working_models.append(key)
                    else:
                        print(f"  ❌ 模型缺少分类器")
                        problem_models.append(key)
                else:
                    print(f"  ❌ 模型加载失败")
                    problem_models.append(key)
            else:
                print(f"  ❌ 模型文件不存在")
                problem_models.append(key)
        
        print(f"\n📊 检查结果:")
        print(f"  ✅ 正常模型: {len(working_models)}")
        print(f"  ❌ 问题模型: {len(problem_models)}")
        
        if working_models:
            print(f"\n🎯 推荐使用的模型:")
            for key in working_models:
                model_info = models[key]
                print(f"  - {model_info.name} (准确率: {model_info.accuracy:.1%})")
        
        return working_models, problem_models
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        traceback.print_exc()
        return [], []

def create_fix_script():
    """创建修复脚本"""
    print(f"\n📝 创建修复脚本")
    print("=" * 80)
    
    fix_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI V2 模型加载修复脚本
AI V2 Model Loading Fix Script
"""

def fix_ai_v2_prediction():
    """修复AI V2预测问题"""
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        from model_manager import get_model_manager
        
        # 获取分析器和模型管理器
        analyzer = get_ai_analyzer_v2()
        model_manager = get_model_manager()
        
        # 查找可用的模型
        models = model_manager.get_available_models()
        
        # 寻找有效的模型
        for key, model_info in models.items():
            if model_info.model_type in ['enhanced', 'optimized']:
                try:
                    # 尝试加载模型
                    model_manager.load_model(key)
                    analyzer.set_model(key)
                    analyzer.set_feature_extractor('advanced')
                    
                    print(f"✅ 成功设置模型: {model_info.name}")
                    return True
                    
                except Exception as e:
                    print(f"⚠️ 模型 {model_info.name} 加载失败: {e}")
                    continue
        
        # 如果没有可用模型，创建新的
        print("🔧 创建新的兼容模型...")
        compatible_model_path = create_compatible_model()
        
        if compatible_model_path:
            # 注册新模型
            model_manager.register_model(
                key="compatible_enhanced",
                name="Compatible Enhanced Model",
                path=compatible_model_path,
                model_type="enhanced",
                accuracy=0.94,
                feature_count=118
            )
            
            # 设置新模型
            analyzer.set_model("compatible_enhanced")
            analyzer.set_feature_extractor('advanced')
            
            print(f"✅ 新模型创建并设置成功")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_ai_v2_prediction()
'''
    
    with open('fix_ai_v2_model_loading.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 修复脚本已创建: fix_ai_v2_model_loading.py")

def main():
    """主函数"""
    print("🔍 诊断AI V2模型结构问题")
    print("=" * 100)
    
    # 1. 检查问题模型
    problem_model = "advanced_models/model_q1.pkl"
    if os.path.exists(problem_model):
        print(f"📋 检查问题模型: {problem_model}")
        check_model_file_structure(problem_model)
    
    # 2. 查找所有可用模型
    found_models = find_available_models()
    
    # 3. 检查每个模型的结构
    print(f"\n🔍 详细检查所有模型")
    print("=" * 80)
    
    for model_path in found_models[:5]:  # 只检查前5个
        check_model_file_structure(model_path)
        print("-" * 40)
    
    # 4. 检查增强模型
    working_enhanced = check_enhanced_models()
    
    # 5. 修复AI V2模型加载
    working_models, problem_models = fix_ai_v2_model_loading()
    
    # 6. 创建兼容模型（如果需要）
    if not working_models:
        print(f"\n🔧 没有可用模型，创建新的兼容模型...")
        compatible_model = create_compatible_model()
        if compatible_model:
            working_models.append(compatible_model)
    
    # 7. 创建修复脚本
    create_fix_script()
    
    # 总结
    print(f"\n📋 诊断总结")
    print("=" * 100)
    
    print(f"✅ 可用模型: {len(working_models)}")
    print(f"❌ 问题模型: {len(problem_models)}")
    
    if working_models:
        print(f"\n🎯 推荐解决方案:")
        print("1. 使用 fix_ai_v2_model_loading.py 自动修复")
        print("2. 或手动设置可用的模型")
        print("3. 重新启动GUI并测试AI预测")
    else:
        print(f"\n⚠️ 需要创建新模型:")
        print("1. 运行增强训练系统")
        print("2. 保存训练好的模型")
        print("3. 注册到模型管理器")

if __name__ == "__main__":
    main()
