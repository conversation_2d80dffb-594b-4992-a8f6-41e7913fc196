#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强特征提取器 - 支持多种特征提取方法
Enhanced Feature Extractor - Support Multiple Feature Extraction Methods
"""

import numpy as np
import pandas as pd
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BaseFeatureExtractor:
    """基础特征提取器接口"""
    
    def __init__(self, name, description, feature_count):
        self.name = name
        self.description = description
        self.feature_count = feature_count
    
    def extract_features(self, df):
        """提取特征的抽象方法"""
        raise NotImplementedError("Subclasses must implement extract_features method")
    
    def get_info(self):
        """获取特征提取器信息"""
        return {
            'name': self.name,
            'description': self.description,
            'feature_count': self.feature_count
        }

class StandardFeatureExtractor(BaseFeatureExtractor):
    """标准特征提取器 - 54个基础特征"""
    
    def __init__(self):
        super().__init__(
            name="标准特征提取器",
            description="提取54个基础统计特征，快速分析",
            feature_count=54
        )
    
    def extract_features(self, df):
        """提取标准特征"""
        try:
            # 确保列名正确
            if 'Depth(m)' in df.columns:
                df = df.rename(columns={
                    'Depth(m)': 'Depth',
                    '1-2 Speed%': 'S1',
                    '1-2 Amp%': 'A1',
                    '1-3 Speed%': 'S2', 
                    '1-3 Amp%': 'A2',
                    '2-3 Speed%': 'S3',
                    '2-3 Amp%': 'A3'
                })
            
            features = []
            
            # 基础统计特征
            for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
                if col in df.columns:
                    data = df[col].values
                    features.extend([
                        np.mean(data),
                        np.std(data),
                        np.median(data),
                        np.percentile(data, 25),
                        np.percentile(data, 75),
                        np.min(data),
                        np.max(data),
                        np.ptp(data),  # 极差
                        self._safe_skew(data)
                    ])
            
            return np.array(features), [f'feature_{i}' for i in range(len(features))]
            
        except Exception as e:
            print(f"❌ 标准特征提取失败: {e}")
            return np.array([]), []
    
    def _safe_skew(self, data):
        """安全的偏度计算"""
        try:
            return stats.skew(data)
        except:
            return 0

class AdvancedFeatureExtractor(BaseFeatureExtractor):
    """高级特征提取器 - 118个增强特征"""
    
    def __init__(self):
        super().__init__(
            name="高精度特征提取器",
            description="提取118个增强特征，包含频域、梯度、相关性等高级特征，高精度分析",
            feature_count=118
        )
    
    def extract_features(self, df):
        """提取增强特征"""
        try:
            # 确保列名正确
            if 'Depth(m)' in df.columns:
                df = df.rename(columns={
                    'Depth(m)': 'Depth',
                    '1-2 Speed%': 'S1',
                    '1-2 Amp%': 'A1',
                    '1-3 Speed%': 'S2', 
                    '1-3 Amp%': 'A2',
                    '2-3 Speed%': 'S3',
                    '2-3 Amp%': 'A3'
                })
            
            features = []
            
            # 基础统计特征
            for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
                if col in df.columns:
                    data = df[col].values
                    features.extend([
                        np.mean(data),
                        np.std(data),
                        np.median(data),
                        np.percentile(data, 25),
                        np.percentile(data, 75),
                        np.min(data),
                        np.max(data),
                        np.ptp(data),  # 极差
                        self._safe_skew(data),
                        self._safe_kurtosis(data)
                    ])
            
            # 深度相关特征
            if 'Depth' in df.columns:
                depth = df['Depth'].values
                features.extend([
                    np.mean(depth),
                    np.max(depth),
                    len(depth),  # 测点数量
                    np.max(depth) / len(depth) if len(depth) > 0 else 0  # 深度密度
                ])
            
            # 速度-幅值关系特征
            for i in range(1, 4):
                s_col = f'S{i}'
                a_col = f'A{i}'
                if s_col in df.columns and a_col in df.columns:
                    s_data = df[s_col].values
                    a_data = df[a_col].values
                    
                    # 相关系数
                    corr = np.corrcoef(s_data, a_data)[0, 1]
                    features.append(corr if not np.isnan(corr) else 0)
                    
                    # 速度/幅值比值统计
                    ratio = s_data / (np.abs(a_data) + 1e-6)
                    features.extend([
                        np.mean(ratio),
                        np.std(ratio),
                        np.median(ratio)
                    ])
            
            # 通道间相关性
            speed_cols = ['S1', 'S2', 'S3']
            amp_cols = ['A1', 'A2', 'A3']
            
            # 速度通道间相关性
            for i in range(len(speed_cols)):
                for j in range(i+1, len(speed_cols)):
                    if speed_cols[i] in df.columns and speed_cols[j] in df.columns:
                        corr = np.corrcoef(df[speed_cols[i]], df[speed_cols[j]])[0, 1]
                        features.append(corr if not np.isnan(corr) else 0)
            
            # 幅值通道间相关性
            for i in range(len(amp_cols)):
                for j in range(i+1, len(amp_cols)):
                    if amp_cols[i] in df.columns and amp_cols[j] in df.columns:
                        corr = np.corrcoef(df[amp_cols[i]], df[amp_cols[j]])[0, 1]
                        features.append(corr if not np.isnan(corr) else 0)
            
            # 频域特征
            for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
                if col in df.columns:
                    data = df[col].values
                    if len(data) > 1:
                        # FFT特征
                        fft_vals = np.fft.fft(data)
                        fft_magnitude = np.abs(fft_vals)
                        
                        # 主频率
                        dominant_freq = np.argmax(fft_magnitude[1:len(fft_magnitude)//2])
                        features.append(dominant_freq / len(fft_magnitude))
                        
                        # 频谱重心
                        freqs = np.arange(len(fft_magnitude))
                        spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude)
                        features.append(spectral_centroid / len(fft_magnitude))
                    else:
                        features.extend([0, 0])
            
            # 梯度特征
            for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
                if col in df.columns:
                    data = df[col].values
                    if len(data) > 1:
                        gradient = np.gradient(data)
                        features.extend([
                            np.mean(np.abs(gradient)),
                            np.std(gradient),
                            np.max(np.abs(gradient))
                        ])
                    else:
                        features.extend([0, 0, 0])
            
            # 异常值特征
            for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
                if col in df.columns:
                    data = df[col].values
                    q75, q25 = np.percentile(data, [75, 25])
                    iqr = q75 - q25
                    lower_bound = q25 - 1.5 * iqr
                    upper_bound = q75 + 1.5 * iqr
                    outliers = np.sum((data < lower_bound) | (data > upper_bound))
                    features.append(outliers / len(data) if len(data) > 0 else 0)
            
            return np.array(features), [f'enhanced_feature_{i}' for i in range(len(features))]
            
        except Exception as e:
            print(f"❌ 增强特征提取失败: {e}")
            return np.array([]), []
    
    def _safe_skew(self, data):
        """安全的偏度计算"""
        try:
            return stats.skew(data)
        except:
            return 0
    
    def _safe_kurtosis(self, data):
        """安全的峰度计算"""
        try:
            return stats.kurtosis(data)
        except:
            return 0

class FeatureExtractorManager:
    """特征提取器管理器"""
    
    def __init__(self):
        self.extractors = {}
        self.current_extractor = None
        
        # 注册默认特征提取器
        self.register_extractor('standard', StandardFeatureExtractor())
        self.register_extractor('advanced', AdvancedFeatureExtractor())
        
        # 设置默认提取器
        self.set_current_extractor('standard')
    
    def register_extractor(self, key, extractor):
        """注册特征提取器"""
        if not isinstance(extractor, BaseFeatureExtractor):
            raise ValueError("Extractor must inherit from BaseFeatureExtractor")
        
        self.extractors[key] = extractor
        print(f"✅ 注册特征提取器: {extractor.name}")
    
    def set_current_extractor(self, key):
        """设置当前特征提取器"""
        if key not in self.extractors:
            raise ValueError(f"Unknown extractor: {key}")
        
        self.current_extractor = self.extractors[key]
        print(f"🔧 切换到特征提取器: {self.current_extractor.name}")
    
    def get_current_extractor(self):
        """获取当前特征提取器"""
        return self.current_extractor
    
    def get_available_extractors(self):
        """获取可用的特征提取器列表"""
        return {key: extractor.get_info() for key, extractor in self.extractors.items()}
    
    def extract_features(self, df):
        """使用当前特征提取器提取特征"""
        if self.current_extractor is None:
            raise ValueError("No current extractor set")
        
        return self.current_extractor.extract_features(df)

# 全局特征提取器管理器实例
feature_manager = FeatureExtractorManager()

def get_feature_manager():
    """获取全局特征提取器管理器"""
    return feature_manager
