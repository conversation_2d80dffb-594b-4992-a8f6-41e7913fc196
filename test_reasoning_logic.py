#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试推理逻辑
"""

def test_reasoning_logic():
    """测试推理逻辑"""
    
    def _generate_reasoning(prediction, confidence, anomaly_score, class_probabilities):
        """Generate reasoning for AI prediction"""
        print(f"🔧 _generate_reasoning 被调用:")
        print(f"  prediction: {prediction} (type: {type(prediction)})")
        print(f"  confidence: {confidence}")
        print(f"  anomaly_score: {anomaly_score}")
        print(f"  class_probabilities: {class_probabilities}")
        
        # Convert numeric prediction to Chinese name
        category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
        if isinstance(prediction, (int, float)):
            prediction_name = category_mapping.get(int(prediction), f'未知类别({prediction})')
            print(f"  转换后的prediction_name: {prediction_name}")
        else:
            prediction_name = prediction
            print(f"  保持原有prediction_name: {prediction_name}")
            
        reasoning = f"AI分析结果：{prediction_name}\n\n"

        reasoning += f"置信度分析：\n"
        reasoning += f"- 主要预测置信度：{confidence:.2%}\n"

        if confidence > 0.8:
            reasoning += "- 置信度较高，预测结果可信\n"
        elif confidence > 0.6:
            reasoning += "- 置信度中等，建议结合传统方法\n"
        else:
            reasoning += "- 置信度较低，建议以传统方法为准\n"

        reasoning += f"\n异常检测：\n"
        reasoning += f"- 异常分数：{anomaly_score:.3f}\n"
        if anomaly_score < -0.1:
            reasoning += "- 检测到异常模式，需要特别关注\n"
        else:
            reasoning += "- 数据模式正常\n"

        reasoning += f"\n各类别概率分布：\n"
        # Convert numeric class names to Chinese names in probabilities
        for class_key, prob in sorted(class_probabilities.items(), key=lambda x: x[1], reverse=True):
            if isinstance(class_key, (int, float)):
                class_name = category_mapping.get(int(class_key), f'类别{class_key}')
            else:
                class_name = class_key
            reasoning += f"- {class_name}：{prob:.2%}\n"

        print(f"  生成的reasoning前100字符: {reasoning[:100]}")
        return reasoning
    
    print("🧪 测试推理逻辑")
    print("=" * 80)
    
    # 测试用例
    test_cases = [
        {
            'prediction': 0,
            'confidence': 0.32,
            'anomaly_score': -0.063,
            'class_probabilities': {0: 0.32, 3: 0.31, 1: 0.20, 2: 0.17},
            'expected_class': 'I类桩'
        },
        {
            'prediction': 3,
            'confidence': 0.31,
            'anomaly_score': -0.087,
            'class_probabilities': {3: 0.31, 0: 0.30, 2: 0.20, 1: 0.19},
            'expected_class': 'IV类桩'
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}:")
        print(f"输入预测: {test_case['prediction']} (期望: {test_case['expected_class']})")
        
        reasoning = _generate_reasoning(
            test_case['prediction'],
            test_case['confidence'],
            test_case['anomaly_score'],
            test_case['class_probabilities']
        )
        
        print("生成的完整推理:")
        print(reasoning)
        
        # 检查是否包含中文类别名称
        if test_case['expected_class'] in reasoning:
            print(f"✅ 推理中包含期望的中文类别名称: {test_case['expected_class']}")
        else:
            print(f"❌ 推理中不包含期望的中文类别名称: {test_case['expected_class']}")
        
        # 检查是否还有数字类别
        if f"AI分析结果：{test_case['prediction']}" in reasoning:
            print(f"❌ 推理中仍然包含数字类别: {test_case['prediction']}")
        else:
            print(f"✅ 推理中不包含数字类别")
        
        print("-" * 60)

if __name__ == "__main__":
    test_reasoning_logic()
