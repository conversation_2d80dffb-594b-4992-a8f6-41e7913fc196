#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试III类桩预测问题
分析为什么AI会将III类桩误判为II类桩
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_iii_class_files():
    """分析III类桩文件的特征"""
    print("🔍 分析III类桩预测问题")
    print("=" * 80)
    
    files = [
        "training_data/III/1-2.txt",
        "training_data/III/KBZ1-51.txt"
    ]
    
    for file_path in files:
        print(f"\n📁 分析文件: {file_path}")
        print("-" * 60)
        
        try:
            # 读取数据
            df = pd.read_csv(file_path, sep='\t', encoding='utf-8')
            print(f"数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            
            # 基本统计
            print(f"\n📊 基本统计:")
            for col in df.columns[1:]:  # 跳过深度列
                values = df[col].values
                print(f"  {col}: 均值={np.mean(values):.2f}, 标准差={np.std(values):.2f}, 最小值={np.min(values):.2f}, 最大值={np.max(values):.2f}")
            
            # 异常点分析
            print(f"\n⚠️ 异常点分析:")
            analyze_anomalies(df)
            
            # 使用AI分析器预测
            print(f"\n🤖 AI预测分析:")
            ai_prediction = test_ai_prediction(df, file_path)
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")

def analyze_anomalies(df):
    """分析异常点"""
    # 传统异常判定标准
    config = {
        '正常': {'speed': (90.0, 1000.0), 'amp': (-100, 3)},
        '轻微畸变': {'speed': (80.0, 90.0), 'amp': (3, 6)},
        '明显畸变': {'speed': (70.0, 80.0), 'amp': (6, 12)},
        '严重畸变': {'speed': (0.0, 70.0), 'amp': (12, 100)}
    }
    
    total_points = 0
    anomaly_counts = {'轻微畸变': 0, '明显畸变': 0, '严重畸变': 0}
    
    for _, row in df.iterrows():
        for i in range(1, 4):
            s_col = f'{i}-{i+1 if i<3 else 3} Speed%' if i < 3 else '2-3 Speed%'
            a_col = f'{i}-{i+1 if i<3 else 3} Amp%' if i < 3 else '2-3 Amp%'
            
            # 尝试不同的列名格式
            speed_cols = [f'{i}-{i+1 if i<3 else 3} Speed%', f'1-{i+1} Speed%']
            amp_cols = [f'{i}-{i+1 if i<3 else 3} Amp%', f'1-{i+1} Amp%']
            
            speed_val = None
            amp_val = None
            
            for s_col in speed_cols:
                if s_col in df.columns:
                    speed_val = row[s_col]
                    break
            
            for a_col in amp_cols:
                if a_col in df.columns:
                    amp_val = row[a_col]
                    break
            
            if speed_val is not None and amp_val is not None and pd.notna(speed_val) and pd.notna(amp_val):
                total_points += 1
                anomaly_type = classify_anomaly_simple(speed_val, amp_val, config)
                if anomaly_type in anomaly_counts:
                    anomaly_counts[anomaly_type] += 1
    
    if total_points > 0:
        print(f"  总测点数: {total_points}")
        for anomaly_type, count in anomaly_counts.items():
            ratio = count / total_points
            print(f"  {anomaly_type}: {count}个 ({ratio:.2%})")
        
        # 计算传统方法的判定结果
        severe_ratio = anomaly_counts['严重畸变'] / total_points
        obvious_ratio = anomaly_counts['明显畸变'] / total_points
        slight_ratio = anomaly_counts['轻微畸变'] / total_points
        
        if severe_ratio > 0.3 or obvious_ratio > 0.5:
            traditional_class = 3  # IV类桩
        elif severe_ratio > 0.1 or obvious_ratio > 0.3:
            traditional_class = 2  # III类桩
        elif slight_ratio > 0.3 or obvious_ratio > 0.1:
            traditional_class = 1  # II类桩
        else:
            traditional_class = 0  # I类桩
        
        class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
        print(f"  传统方法判定: {class_names[traditional_class]}")

def classify_anomaly_simple(speed, amp, config):
    """简化的异常分类"""
    s_map = {'正常': 0, '轻微畸变': 1, '明显畸变': 2, '严重畸变': 3}
    o_types = ['严重畸变', '明显畸变', '轻微畸变', '正常']
    s_cat, a_cat = '正常', '正常'
    
    for lvl in o_types:
        if lvl in config and 'speed' in config[lvl]:
            s_min, s_max = config[lvl]['speed']
            if s_min <= speed < s_max:
                s_cat = lvl
                break
    
    for lvl in o_types:
        if lvl in config and 'amp' in config[lvl]:
            a_min, a_max = config[lvl]['amp']
            if a_min <= amp < a_max:
                a_cat = lvl
                break
    
    return s_cat if s_map.get(s_cat, -1) > s_map.get(a_cat, -1) else a_cat

def test_ai_prediction(df, file_path):
    """测试AI预测"""
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        # 获取AI分析器V2
        analyzer_v2 = get_ai_analyzer_v2()
        
        # 设置为高精度模型
        models = analyzer_v2.model_manager.get_available_models()
        optimized_model_key = None
        
        for key, model_info in models.items():
            if model_info.model_type == 'optimized' and model_info.accuracy > 0.9:
                optimized_model_key = key
                break
        
        if optimized_model_key:
            analyzer_v2.set_model(optimized_model_key)
            analyzer_v2.set_feature_extractor('advanced')
        
        # 进行预测
        result = analyzer_v2.predict(df)
        
        if result:
            predicted_class = result.get('完整性类别', -1)
            confidence = result.get('ai_confidence', 0.0)
            
            class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
            pred_name = class_names.get(predicted_class, f'未知({predicted_class})')
            
            print(f"  AI预测结果: {pred_name} (置信度: {confidence:.2%})")
            print(f"  预期结果: III类桩")
            print(f"  预测正确: {'✅' if predicted_class == 2 else '❌'}")
            
            # 获取特征信息
            if hasattr(analyzer_v2, 'feature_manager'):
                features = analyzer_v2.feature_manager.extract_features(df)
                print(f"  提取特征数: {features.shape[1] if features is not None else 'N/A'}")
            
            # 获取详细分析结果
            if 'detailed_analysis' in result:
                print(f"  详细分析: {result['detailed_analysis']}")
            
            return result
        else:
            print(f"  ❌ AI预测失败")
            return None
            
    except Exception as e:
        print(f"  ❌ AI预测异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_with_correct_samples():
    """与正确的II类桩样本对比"""
    print(f"\n🔍 与II类桩样本对比分析")
    print("=" * 80)
    
    # 分析一个II类桩样本
    ii_file = "training_data/II/KBZ1-67.txt"
    print(f"\n📁 II类桩参考样本: {ii_file}")
    
    try:
        df_ii = pd.read_csv(ii_file, sep='\t', encoding='utf-8')
        print(f"数据形状: {df_ii.shape}")
        
        # 基本统计
        print(f"\n📊 II类桩基本统计:")
        for col in df_ii.columns[1:]:
            values = df_ii[col].values
            print(f"  {col}: 均值={np.mean(values):.2f}, 标准差={np.std(values):.2f}")
        
        # 异常点分析
        print(f"\n⚠️ II类桩异常点分析:")
        analyze_anomalies(df_ii)
        
    except Exception as e:
        print(f"❌ II类桩分析失败: {e}")

def investigate_feature_extraction():
    """调查特征提取过程"""
    print(f"\n🔬 特征提取过程调查")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer_v2 = get_ai_analyzer_v2()
        
        # 分析III类桩文件
        files = [
            ("training_data/III/1-2.txt", "III类桩"),
            ("training_data/II/KBZ1-67.txt", "II类桩")
        ]
        
        for file_path, label in files:
            print(f"\n📁 分析 {label}: {file_path}")
            
            df = pd.read_csv(file_path, sep='\t', encoding='utf-8')
            
            # 提取特征
            if hasattr(analyzer_v2, 'feature_manager'):
                features = analyzer_v2.feature_manager.extract_features(df)
                if features is not None:
                    print(f"  特征向量形状: {features.shape}")
                    print(f"  特征统计: 均值={np.mean(features):.4f}, 标准差={np.std(features):.4f}")
                    print(f"  特征范围: [{np.min(features):.4f}, {np.max(features):.4f}]")
                    
                    # 显示前10个特征值
                    print(f"  前10个特征值: {features[0][:10]}")
                else:
                    print(f"  ❌ 特征提取失败")
            
    except Exception as e:
        print(f"❌ 特征提取调查失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔬 III类桩预测问题调试分析")
    print("=" * 100)
    
    # 1. 分析III类桩文件
    analyze_iii_class_files()
    
    # 2. 与II类桩对比
    compare_with_correct_samples()
    
    # 3. 调查特征提取
    investigate_feature_extraction()
    
    print(f"\n📋 调试总结")
    print("=" * 80)
    print("1. 检查了III类桩数据的基本统计特征")
    print("2. 分析了传统方法的异常点判定")
    print("3. 测试了AI模型的预测结果")
    print("4. 对比了II类桩和III类桩的特征差异")
    print("5. 调查了特征提取过程")
    
    print(f"\n💡 可能的问题原因:")
    print("- 训练数据标签可能有误")
    print("- 特征提取方法可能不够区分III类和II类")
    print("- 模型训练时III类样本过少导致学习不充分")
    print("- 数据预处理过程中可能存在问题")

if __name__ == "__main__":
    main()
