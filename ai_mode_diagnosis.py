#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI模式全面诊断脚本
检查AI模型加载、预测能力、GUI集成等问题
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import traceback
from pathlib import Path

# 强制清除模块缓存
modules_to_clear = [k for k in sys.modules.keys() if 'Pile_analyze_GZ_gui' in k]
for module in modules_to_clear:
    del sys.modules[module]

def check_model_file():
    """检查模型文件状态"""
    print("🔍 检查AI模型文件状态")
    print("=" * 80)

    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False

    print(f"✅ 模型文件存在: {model_path}")

    # 检查文件大小
    file_size = os.path.getsize(model_path)
    print(f"📊 文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")

    # 尝试直接加载模型文件
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)

        print(f"✅ 模型文件可以正常读取")
        print(f"📋 模型数据类型: {type(model_data)}")

        if hasattr(model_data, '__dict__'):
            print(f"📋 模型属性: {list(model_data.__dict__.keys())}")
        elif isinstance(model_data, dict):
            print(f"📋 模型字典键: {list(model_data.keys())}")

        return True

    except Exception as e:
        print(f"❌ 模型文件读取失败: {e}")
        return False

def check_ai_analyzer_basic():
    """检查AI分析器基本功能"""
    print("\n🔍 检查AI分析器基本功能")
    print("=" * 80)

    try:
        from Pile_analyze_GZ_gui import BuiltInAIAnalyzer

        # 创建分析器
        analyzer = BuiltInAIAnalyzer()
        print("✅ AI分析器创建成功")

        # 检查初始状态
        print(f"📋 分析器属性:")
        print(f"  - 是否有classifier: {hasattr(analyzer, 'classifier')}")
        print(f"  - 是否有anomaly_detector: {hasattr(analyzer, 'anomaly_detector')}")
        print(f"  - 是否有feature_importance: {hasattr(analyzer, 'feature_importance')}")

        if hasattr(analyzer, 'classifier') and analyzer.classifier is not None:
            print(f"  - classifier类型: {type(analyzer.classifier)}")

        return analyzer

    except Exception as e:
        print(f"❌ AI分析器创建失败: {e}")
        traceback.print_exc()
        return None

def check_model_loading(analyzer):
    """检查模型加载功能"""
    print("\n🔍 检查模型加载功能")
    print("=" * 80)

    if analyzer is None:
        print("❌ 分析器不可用，跳过模型加载测试")
        return False

    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"

    try:
        print(f"📥 尝试加载模型: {model_path}")
        success = analyzer.load_models(model_path)

        if success:
            print("✅ 模型加载成功")

            # 检查加载后的状态
            print(f"📋 加载后状态:")
            print(f"  - classifier: {type(analyzer.classifier) if hasattr(analyzer, 'classifier') and analyzer.classifier else 'None'}")
            print(f"  - anomaly_detector: {type(analyzer.anomaly_detector) if hasattr(analyzer, 'anomaly_detector') and analyzer.anomaly_detector else 'None'}")
            print(f"  - feature_importance: {len(analyzer.feature_importance) if hasattr(analyzer, 'feature_importance') and analyzer.feature_importance else 0} features")

            return True
        else:
            print("❌ 模型加载失败")
            return False

    except Exception as e:
        print(f"❌ 模型加载异常: {e}")
        traceback.print_exc()
        return False

def check_feature_extraction(analyzer):
    """检查特征提取功能"""
    print("\n🔍 检查特征提取功能")
    print("=" * 80)

    if analyzer is None:
        print("❌ 分析器不可用，跳过特征提取测试")
        return None

    # 测试数据文件
    test_files = [
        "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt",
        "F:/2025/AIpile/AIpiles_final/training_data/II/KBZ1-67.txt",
        "F:/2025/AIpile/AIpiles_final/training_data/III/1-2.txt",
        "F:/2025/AIpile/AIpiles_final/training_data/IV/KBZ1-40.txt",
    ]

    for test_file in test_files:
        if not os.path.exists(test_file):
            continue

        filename = os.path.basename(test_file)
        expected_class = os.path.basename(os.path.dirname(test_file))

        try:
            print(f"\n📁 测试文件: {filename} (期望类别: {expected_class})")

            # 读取数据
            df = pd.read_csv(test_file, sep='\t')
            print(f"  📊 数据形状: {df.shape}")
            print(f"  📋 列名: {list(df.columns)}")

            # 提取特征
            features, feature_names = analyzer.extract_features(df)
            print(f"  🔧 特征形状: {features.shape}")
            print(f"  📋 特征数量: {len(feature_names) if feature_names else 'None'}")

            if features.size > 0:
                print(f"  📊 特征统计: min={features.min():.3f}, max={features.max():.3f}, mean={features.mean():.3f}")
                return features, feature_names
            else:
                print(f"  ❌ 特征提取失败")

        except Exception as e:
            print(f"  ❌ 处理失败: {e}")

    return None, None

def check_prediction_capability(analyzer, features):
    """检查预测能力"""
    print("\n🔍 检查预测能力")
    print("=" * 80)

    if analyzer is None or features is None:
        print("❌ 分析器或特征不可用，跳过预测测试")
        return None

    try:
        print(f"🎯 进行预测...")
        print(f"  输入特征形状: {features.shape}")

        result = analyzer.predict(features)

        if result is None:
            print("❌ 预测返回None")
            return None

        print("✅ 预测成功")
        print(f"📋 预测结果:")

        for key, value in result.items():
            if isinstance(value, (int, float, np.integer, np.floating)):
                print(f"  - {key}: {value}")
            elif isinstance(value, dict):
                print(f"  - {key}: {dict(value)}")
            elif isinstance(value, str):
                print(f"  - {key}: {value[:100]}..." if len(value) > 100 else f"  - {key}: {value}")
            else:
                print(f"  - {key}: {type(value)}")

        return result

    except Exception as e:
        print(f"❌ 预测失败: {e}")
        traceback.print_exc()
        return None

def check_gui_integration():
    """检查GUI集成"""
    print("\n🔍 检查GUI集成")
    print("=" * 80)

    try:
        # 检查GUI类是否可以导入
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        print("✅ GUI类导入成功")

        # 检查AI相关方法是否存在
        gui_methods = [
            'run_ai_analysis',
            'display_ai_result',
            'create_comparison_text'
        ]

        for method in gui_methods:
            if hasattr(PileAnalyzerGZGUI, method):
                print(f"✅ GUI方法存在: {method}")
            else:
                print(f"❌ GUI方法缺失: {method}")

        return True

    except Exception as e:
        print(f"❌ GUI集成检查失败: {e}")
        traceback.print_exc()
        return False

def check_data_compatibility():
    """检查数据兼容性"""
    print("\n🔍 检查数据兼容性")
    print("=" * 80)

    test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"

    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False

    try:
        # 读取数据
        df = pd.read_csv(test_file, sep='\t')
        print(f"✅ 数据读取成功: {df.shape}")

        # 检查列名
        expected_columns = ['Depth(m)', '1-2 Speed%', '1-2 Amp%', '1-3 Speed%', '1-3 Amp%', '2-3 Speed%', '2-3 Amp%']
        actual_columns = list(df.columns)

        print(f"📋 期望列名: {expected_columns}")
        print(f"📋 实际列名: {actual_columns}")

        missing_columns = set(expected_columns) - set(actual_columns)
        extra_columns = set(actual_columns) - set(expected_columns)

        if missing_columns:
            print(f"⚠️ 缺失列: {missing_columns}")
        if extra_columns:
            print(f"⚠️ 额外列: {extra_columns}")

        if not missing_columns:
            print("✅ 数据列名兼容")

        # 检查数据类型
        print(f"📊 数据类型:")
        for col in df.columns:
            print(f"  - {col}: {df[col].dtype}")

        # 检查数据范围
        print(f"📊 数据范围:")
        for col in df.columns:
            if df[col].dtype in ['float64', 'int64']:
                print(f"  - {col}: [{df[col].min():.3f}, {df[col].max():.3f}]")

        return True

    except Exception as e:
        print(f"❌ 数据兼容性检查失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔬 AI模式全面诊断")
    print("=" * 100)

    # 1. 检查模型文件
    model_file_ok = check_model_file()

    # 2. 检查AI分析器基本功能
    analyzer = check_ai_analyzer_basic()

    # 3. 检查模型加载
    model_loaded = check_model_loading(analyzer)

    # 4. 检查特征提取
    features, feature_names = check_feature_extraction(analyzer)

    # 5. 检查预测能力
    prediction_result = check_prediction_capability(analyzer, features)

    # 6. 检查GUI集成
    gui_ok = check_gui_integration()

    # 7. 检查数据兼容性
    data_ok = check_data_compatibility()

    # 总结
    print("\n📋 诊断总结")
    print("=" * 80)
    print(f"✅ 模型文件状态: {'正常' if model_file_ok else '异常'}")
    print(f"✅ AI分析器创建: {'正常' if analyzer else '异常'}")
    print(f"✅ 模型加载: {'正常' if model_loaded else '异常'}")
    print(f"✅ 特征提取: {'正常' if features is not None else '异常'}")
    print(f"✅ 预测功能: {'正常' if prediction_result else '异常'}")
    print(f"✅ GUI集成: {'正常' if gui_ok else '异常'}")
    print(f"✅ 数据兼容性: {'正常' if data_ok else '异常'}")

    if all([model_file_ok, analyzer, model_loaded, features is not None, prediction_result, gui_ok, data_ok]):
        print("\n🎉 AI模式诊断完成 - 所有功能正常")
    else:
        print("\n⚠️ AI模式存在问题，需要进一步修复")

if __name__ == "__main__":
    main()
