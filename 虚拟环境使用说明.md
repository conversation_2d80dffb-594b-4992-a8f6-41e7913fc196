# AI桩基完整性分析系统 - 虚拟环境使用说明

## 概述

我们已经为AI桩基完整性分析系统创建了一个独立的Python虚拟环境，解决了依赖包冲突和缺失的问题。

## 虚拟环境信息

- **虚拟环境位置**: `F:\2025\AIpile\AIpiles_final\ai_pile_env`
- **Python版本**: Python 3.13
- **已安装的主要依赖包**:
  - pandas 2.2.3
  - numpy 2.2.6
  - scikit-learn 1.6.1
  - matplotlib 3.10.3
  - plotly 6.1.1
  - seaborn 0.13.2
  - torch 2.7.0+cpu (PyTorch CPU版本)
  - torchvision 0.22.0+cpu
  - shap 0.47.2
  - scipy 1.15.3

## 使用方法

### 方法一：使用批处理文件（推荐）

1. **激活虚拟环境**：
   - 双击 `activate_env.bat` 文件
   - 这会打开一个命令行窗口并自动激活虚拟环境

2. **运行程序**：
   在激活的命令行窗口中输入以下命令：
   ```
   python ai_pile_integrity_analyzer.py
   ```
   或
   ```
   python auto_train_and_classify.py
   ```

3. **退出虚拟环境**：
   在命令行窗口中输入：
   ```
   deactivate
   ```

### 方法二：使用PowerShell命令

1. **运行主程序**：
   ```powershell
   powershell -Command "& { F:\2025\AIpile\AIpiles_final\ai_pile_env\Scripts\python.exe F:\2025\AIpile\AIpiles_final\ai_pile_integrity_analyzer.py }"
   ```

2. **运行自动训练程序**：
   ```powershell
   powershell -Command "& { F:\2025\AIpile\AIpiles_final\ai_pile_env\Scripts\python.exe F:\2025\AIpile\AIpiles_final\auto_train_and_classify.py }"
   ```

### 方法三：手动激活虚拟环境

1. **打开命令提示符**
2. **激活虚拟环境**：
   ```cmd
   F:\2025\AIpile\AIpiles_final\ai_pile_env\Scripts\activate.bat
   ```
3. **运行程序**：
   ```cmd
   python ai_pile_integrity_analyzer.py
   ```

## 测试安装

运行以下命令测试所有依赖是否正确安装：
```cmd
python test_dependencies.py
```

如果看到所有依赖都显示"✓"，说明安装成功。

## 故障排除

### 问题1：找不到模块
如果出现"ModuleNotFoundError"错误，请确保：
1. 使用的是虚拟环境中的Python解释器
2. 虚拟环境已正确激活
3. 所有依赖包已正确安装

### 问题2：虚拟环境激活失败
如果无法激活虚拟环境，请：
1. 检查路径是否正确
2. 确保有足够的权限
3. 尝试重新创建虚拟环境

### 问题3：GUI界面无法显示
如果GUI界面无法正常显示，请：
1. 确保系统支持Tkinter
2. 检查显示设置
3. 尝试在不同的终端中运行

## 文件说明

- `ai_pile_env/`: 虚拟环境目录
- `activate_env.bat`: 激活虚拟环境的批处理文件
- `setup_environment.bat`: 环境设置脚本（已执行）
- `requirements.txt`: 依赖包列表
- `test_dependencies.py`: 依赖测试脚本
- `ai_pile_integrity_analyzer.py`: 主程序
- `auto_train_and_classify.py`: 自动训练程序

## 注意事项

1. **虚拟环境隔离**: 这个虚拟环境与系统Python环境完全隔离，不会影响其他Python项目
2. **依赖版本**: 所有依赖包都是最新稳定版本，确保兼容性和性能
3. **PyTorch版本**: 安装的是CPU版本的PyTorch，适合大多数应用场景
4. **路径问题**: 请确保所有路径都使用正确的格式，避免空格和特殊字符

## 更新依赖

如果需要更新依赖包，请在激活虚拟环境后运行：
```cmd
pip install --upgrade package_name
```

或更新所有包：
```cmd
pip list --outdated
pip install --upgrade package_name1 package_name2 ...
```

## 备份和恢复

### 备份虚拟环境
```cmd
pip freeze > requirements_backup.txt
```

### 恢复虚拟环境
```cmd
pip install -r requirements_backup.txt
```
