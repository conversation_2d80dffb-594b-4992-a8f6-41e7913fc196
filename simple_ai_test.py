#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的AI测试
"""

import os
import sys

print("🧪 简单AI测试开始")

# 检查训练数据目录
training_data_dir = "F:/2025/AIpile/AIpiles_final/training_data"
print(f"训练数据目录存在: {os.path.exists(training_data_dir)}")

if os.path.exists(training_data_dir):
    subdirs = [d for d in os.listdir(training_data_dir) if os.path.isdir(os.path.join(training_data_dir, d))]
    print(f"子目录: {subdirs}")
    
    for subdir in subdirs:
        subdir_path = os.path.join(training_data_dir, subdir)
        files = [f for f in os.listdir(subdir_path) if f.endswith('.txt')]
        print(f"{subdir}: {len(files)} 个文件")

# 测试AI分析器导入
try:
    sys.path.insert(0, 'F:/2025/AIpile/AIpiles_final')
    from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
    print("✅ AI分析器导入成功")
    
    analyzer = BuiltInAIAnalyzer()
    print("✅ AI分析器创建成功")
    
except Exception as e:
    print(f"❌ AI分析器导入失败: {e}")

print("🧪 简单AI测试完成")
