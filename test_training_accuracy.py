#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试auto_train_and_classify.py训练的模型在训练数据上的准确率
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import traceback

def collect_training_data():
    """收集训练数据"""
    print("📊 收集训练数据")
    print("=" * 80)
    
    training_data_dir = "F:/2025/AIpile/AIpiles_final/training_data"
    
    if not os.path.exists(training_data_dir):
        print(f"❌ 训练数据目录不存在: {training_data_dir}")
        return None, None, None
    
    features_list = []
    labels_list = []
    file_paths = []
    
    # 类别映射
    class_mapping = {'I': 0, 'II': 1, 'III': 2, 'IV': 3}
    
    for class_name, class_label in class_mapping.items():
        class_dir = os.path.join(training_data_dir, class_name)
        
        if not os.path.exists(class_dir):
            print(f"⚠️ 类别目录不存在: {class_dir}")
            continue
        
        files = [f for f in os.listdir(class_dir) if f.endswith('.txt')]
        print(f"📁 {class_name}类桩: 找到 {len(files)} 个文件")
        
        for file in files:
            file_path = os.path.join(class_dir, file)
            
            try:
                # 读取数据
                df = pd.read_csv(file_path, sep='\t')
                
                # 检查列名并标准化
                expected_columns = ['Depth(m)', '1-2 Speed%', '1-2 Amp%', '1-3 Speed%', '1-3 Amp%', '2-3 Speed%', '2-3 Amp%']
                if list(df.columns) == expected_columns:
                    # 重命名列以匹配auto_train_and_classify.py的期望
                    df.columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
                
                # 使用auto_train_and_classify.py的预处理方法
                from auto_train_and_classify import AutoTrainAndClassify
                trainer = AutoTrainAndClassify()
                
                # 预处理序列数据
                sequence_data = trainer.preprocess_sequence_data(df)
                
                # 提取高级特征
                features = trainer.extract_advanced_features(sequence_data)
                
                if features.size > 0:
                    features_list.append(features)
                    labels_list.append(class_label)
                    file_paths.append(file_path)
                
            except Exception as e:
                print(f"⚠️ 处理文件失败 {file}: {e}")
    
    if len(features_list) > 0:
        features_array = np.array(features_list)
        labels_array = np.array(labels_list)
        
        print(f"✅ 收集完成:")
        print(f"  - 样本数量: {len(features_list)}")
        print(f"  - 特征维度: {features_array.shape[1]}")
        print(f"  - 类别分布: {dict(zip(*np.unique(labels_array, return_counts=True)))}")
        
        return features_array, labels_array, file_paths
    else:
        print("❌ 没有收集到有效数据")
        return None, None, None

def test_simple_model_training():
    """测试简单模型训练和预测准确率"""
    print("\n🤖 测试简单模型训练")
    print("=" * 80)
    
    # 收集数据
    features, labels, file_paths = collect_training_data()
    
    if features is None:
        print("❌ 无法收集训练数据")
        return
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.preprocessing import StandardScaler
        
        # 数据预处理
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 分割数据 (80% 训练, 20% 测试)
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, labels, test_size=0.2, random_state=42, stratify=labels
        )
        
        print(f"📊 数据分割:")
        print(f"  - 训练集: {X_train.shape[0]} 样本")
        print(f"  - 测试集: {X_test.shape[0]} 样本")
        
        # 训练随机森林模型
        model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        print("🔧 训练模型...")
        model.fit(X_train, y_train)
        
        # 在训练集上预测
        train_predictions = model.predict(X_train)
        train_accuracy = accuracy_score(y_train, train_predictions)
        
        # 在测试集上预测
        test_predictions = model.predict(X_test)
        test_accuracy = accuracy_score(y_test, test_predictions)
        
        print(f"\n📊 模型性能:")
        print(f"  - 训练集准确率: {train_accuracy:.2%}")
        print(f"  - 测试集准确率: {test_accuracy:.2%}")
        
        # 详细分类报告
        class_names = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
        print(f"\n📋 测试集分类报告:")
        print(classification_report(y_test, test_predictions, target_names=class_names))
        
        # 混淆矩阵
        cm = confusion_matrix(y_test, test_predictions)
        print(f"\n📊 混淆矩阵:")
        print("     ", "  ".join([f"{name:8s}" for name in class_names]))
        for i, row in enumerate(cm):
            print(f"{class_names[i]:8s}", "  ".join([f"{val:8d}" for val in row]))
        
        # 保存模型用于后续测试
        model_path = "F:/2025/AIpile/AIpiles_final/test_model.pkl"
        with open(model_path, 'wb') as f:
            pickle.dump({'model': model, 'scaler': scaler}, f)
        print(f"\n💾 模型已保存: {model_path}")
        
        return train_accuracy, test_accuracy
        
    except Exception as e:
        print(f"❌ 模型训练失败: {e}")
        traceback.print_exc()
        return None, None

def test_full_dataset_prediction():
    """测试在完整数据集上的预测准确率"""
    print("\n🎯 测试完整数据集预测")
    print("=" * 80)
    
    model_path = "F:/2025/AIpile/AIpiles_final/test_model.pkl"
    
    if not os.path.exists(model_path):
        print("❌ 模型文件不存在，请先运行训练")
        return
    
    try:
        # 加载模型
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        model = model_data['model']
        scaler = model_data['scaler']
        
        print("✅ 模型加载成功")
        
        # 收集完整数据集
        features, labels, file_paths = collect_training_data()
        
        if features is None:
            print("❌ 无法收集数据")
            return
        
        # 预处理
        features_scaled = scaler.transform(features)
        
        # 预测
        predictions = model.predict(features_scaled)
        probabilities = model.predict_proba(features_scaled)
        
        # 计算准确率
        accuracy = accuracy_score(labels, predictions)
        
        print(f"📊 完整数据集预测结果:")
        print(f"  - 总样本数: {len(labels)}")
        print(f"  - 预测准确率: {accuracy:.2%}")
        
        # 按类别分析
        class_names = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
        
        print(f"\n📋 按类别分析:")
        for class_idx in range(4):
            class_mask = labels == class_idx
            if np.sum(class_mask) > 0:
                class_accuracy = accuracy_score(labels[class_mask], predictions[class_mask])
                class_count = np.sum(class_mask)
                correct_count = np.sum(predictions[class_mask] == labels[class_mask])
                
                print(f"  - {class_names[class_idx]}: {correct_count}/{class_count} = {class_accuracy:.2%}")
        
        # 显示一些错误预测的例子
        wrong_predictions = predictions != labels
        if np.sum(wrong_predictions) > 0:
            print(f"\n❌ 错误预测示例 (共{np.sum(wrong_predictions)}个):")
            wrong_indices = np.where(wrong_predictions)[0]
            
            for i, idx in enumerate(wrong_indices[:10]):  # 只显示前10个
                file_name = os.path.basename(file_paths[idx])
                true_class = class_names[labels[idx]]
                pred_class = class_names[predictions[idx]]
                confidence = probabilities[idx][predictions[idx]]
                
                print(f"  {i+1:2d}. {file_name:20s} | 真实: {true_class:8s} | 预测: {pred_class:8s} | 置信度: {confidence:.2%}")
        
        return accuracy
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        traceback.print_exc()
        return None

def analyze_overfitting():
    """分析过拟合情况"""
    print("\n🔍 分析过拟合情况")
    print("=" * 80)
    
    # 收集数据
    features, labels, file_paths = collect_training_data()
    
    if features is None:
        return
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import cross_val_score, StratifiedKFold
        from sklearn.preprocessing import StandardScaler
        
        # 数据预处理
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 使用交叉验证评估模型
        model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        # 5折交叉验证
        cv_scores = cross_val_score(model, features_scaled, labels, cv=5, scoring='accuracy')
        
        # 训练完整模型
        model.fit(features_scaled, labels)
        train_score = model.score(features_scaled, labels)
        
        print(f"📊 过拟合分析:")
        print(f"  - 训练集准确率: {train_score:.2%}")
        print(f"  - 交叉验证平均准确率: {cv_scores.mean():.2%} ± {cv_scores.std():.2%}")
        print(f"  - 交叉验证分数: {[f'{score:.2%}' for score in cv_scores]}")
        
        overfitting_gap = train_score - cv_scores.mean()
        print(f"  - 过拟合程度: {overfitting_gap:.2%}")
        
        if overfitting_gap > 0.1:
            print("⚠️ 存在明显过拟合")
        elif overfitting_gap > 0.05:
            print("⚠️ 存在轻微过拟合")
        else:
            print("✅ 过拟合程度较低")
        
        return train_score, cv_scores.mean()
        
    except Exception as e:
        print(f"❌ 过拟合分析失败: {e}")
        return None, None

def main():
    """主函数"""
    print("🔬 测试auto_train_and_classify.py训练模型的准确率")
    print("=" * 100)
    
    # 1. 测试简单模型训练
    train_acc, test_acc = test_simple_model_training()
    
    # 2. 测试完整数据集预测
    full_acc = test_full_dataset_prediction()
    
    # 3. 分析过拟合
    train_cv_acc, cv_acc = analyze_overfitting()
    
    # 总结
    print("\n📋 测试总结")
    print("=" * 80)
    
    if train_acc and test_acc:
        print(f"✅ 训练/测试分割模式:")
        print(f"  - 训练集准确率: {train_acc:.2%}")
        print(f"  - 测试集准确率: {test_acc:.2%}")
    
    if full_acc:
        print(f"✅ 完整数据集预测准确率: {full_acc:.2%}")
    
    if train_cv_acc and cv_acc:
        print(f"✅ 交叉验证结果:")
        print(f"  - 训练集准确率: {train_cv_acc:.2%}")
        print(f"  - 交叉验证准确率: {cv_acc:.2%}")
    
    print(f"\n🎯 回答您的问题:")
    
    if full_acc:
        if full_acc >= 0.95:
            print(f"📊 使用训练数据预测的准确率约为 {full_acc:.1%}")
            print("✅ 准确率很高，但这是在训练数据上的表现")
            print("⚠️ 这种高准确率可能存在过拟合，实际应用中可能会降低")
        elif full_acc >= 0.8:
            print(f"📊 使用训练数据预测的准确率约为 {full_acc:.1%}")
            print("✅ 准确率较好，模型学习效果不错")
        else:
            print(f"📊 使用训练数据预测的准确率约为 {full_acc:.1%}")
            print("⚠️ 准确率较低，可能需要改进特征工程或模型参数")
    
    if cv_acc:
        print(f"📊 更可靠的交叉验证准确率约为 {cv_acc:.1%}")
        print("💡 交叉验证结果更能反映模型的真实性能")
    
    print(f"\n💡 建议:")
    print("1. 在训练数据上的高准确率是正常的，但要注意过拟合")
    print("2. 交叉验证能更好地评估模型的泛化能力")
    print("3. 最终评估应该使用完全独立的测试数据")
    print("4. 如果准确率不是100%，说明数据中存在一定的噪声或复杂性")

if __name__ == "__main__":
    main()
