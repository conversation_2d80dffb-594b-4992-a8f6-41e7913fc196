#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Quick Start - AI Pile Integrity Analysis System
快速启动 - AI桩基完整性分析系统

A simplified, user-friendly interface for quick setup and training.
This replaces the terminal-based interaction with a clean GUI.

Features:
- One-click setup
- Automatic data generation
- Progress tracking
- Simple configuration

Author: AI Pile Integrity Analysis System
Version: 3.0 (Quick Start)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
import time

class QuickStartGUI:
    """Quick start GUI for AI Pile Integrity Analysis"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Quick Start - AI Pile Analysis")
        self.root.geometry("700x500")
        self.root.configure(bg='#f8f9fa')
        
        # Variables
        self.generate_data = tk.BooleanVar(value=True)
        self.samples_count = tk.IntVar(value=50)
        self.training_mode = tk.StringVar(value="quick")
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="Ready to start")
        
        self.create_interface()
        
    def create_interface(self):
        """Create the interface"""
        # Header
        header = tk.Frame(self.root, bg='#2c3e50', height=80)
        header.pack(fill='x')
        header.pack_propagate(False)
        
        tk.Label(header,
                text="🚀 Quick Start - AI Pile Analysis",
                font=('Segoe UI', 18, 'bold'),
                fg='white',
                bg='#2c3e50').pack(pady=20)
        
        # Main content
        main = tk.Frame(self.root, bg='#f8f9fa')
        main.pack(fill='both', expand=True, padx=30, pady=20)
        
        # Status card
        self.create_status_card(main)
        
        # Configuration card
        self.create_config_card(main)
        
        # Control card
        self.create_control_card(main)
        
    def create_status_card(self, parent):
        """Create status information card"""
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card.pack(fill='x', pady=(0, 15))
        
        # Header
        header = tk.Frame(card, bg='#fff3cd')
        header.pack(fill='x')
        
        tk.Label(header,
                text="⚠️ Training Data Status",
                font=('Segoe UI', 12, 'bold'),
                bg='#fff3cd',
                fg='#856404').pack(pady=10)
        
        # Content
        content = tk.Frame(card, bg='white')
        content.pack(fill='x', padx=20, pady=15)
        
        tk.Label(content,
                text="Current training data status: Insufficient training data detected.",
                font=('Segoe UI', 11),
                bg='white',
                fg='#856404').pack(anchor='w')
        
        tk.Label(content,
                text="The system needs more training data to function properly.",
                font=('Segoe UI', 10),
                bg='white',
                fg='#666666').pack(anchor='w', pady=(5, 0))
        
    def create_config_card(self, parent):
        """Create configuration card"""
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card.pack(fill='x', pady=(0, 15))
        
        # Header
        tk.Label(card,
                text="⚙️ Configuration",
                font=('Segoe UI', 12, 'bold'),
                bg='white',
                fg='#2c3e50').pack(anchor='w', padx=20, pady=(15, 10))
        
        # Content
        content = tk.Frame(card, bg='white')
        content.pack(fill='both', expand=True, padx=20, pady=(0, 15))
        
        # Generate synthetic data option
        tk.Checkbutton(content,
                      text="✅ Generate synthetic data for training",
                      variable=self.generate_data,
                      font=('Segoe UI', 11, 'bold'),
                      bg='white',
                      command=self.on_generate_toggle).pack(anchor='w', pady=(0, 10))
        
        # Samples per class
        samples_frame = tk.Frame(content, bg='white')
        samples_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(samples_frame,
                text="Samples per class:",
                font=('Segoe UI', 10),
                bg='white').pack(side='left')
        
        tk.Spinbox(samples_frame,
                  from_=10, to=200,
                  textvariable=self.samples_count,
                  width=10,
                  font=('Segoe UI', 10)).pack(side='left', padx=(10, 0))
        
        tk.Label(samples_frame,
                text="(Recommended: 50-100)",
                font=('Segoe UI', 9),
                fg='#666666',
                bg='white').pack(side='left', padx=(10, 0))
        
        # Training mode
        tk.Label(content,
                text="Training Mode:",
                font=('Segoe UI', 10, 'bold'),
                bg='white').pack(anchor='w', pady=(0, 5))
        
        modes = [
            ("Quick training (recommended)", "quick"),
            ("Advanced training (better accuracy)", "advanced"),
            ("Research mode (full analysis)", "research")
        ]
        
        for text, value in modes:
            tk.Radiobutton(content,
                          text=text,
                          variable=self.training_mode,
                          value=value,
                          font=('Segoe UI', 10),
                          bg='white').pack(anchor='w', pady=2)
        
    def create_control_card(self, parent):
        """Create control card"""
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card.pack(fill='x')
        
        # Header
        tk.Label(card,
                text="🚀 Training Control",
                font=('Segoe UI', 12, 'bold'),
                bg='white',
                fg='#2c3e50').pack(anchor='w', padx=20, pady=(15, 10))
        
        # Progress
        progress_frame = tk.Frame(card, bg='white')
        progress_frame.pack(fill='x', padx=20, pady=(0, 15))
        
        tk.Label(progress_frame,
                text="Progress:",
                font=('Segoe UI', 10),
                bg='white').pack(anchor='w', pady=(0, 5))
        
        self.progress_bar = ttk.Progressbar(progress_frame,
                                           variable=self.progress,
                                           maximum=100)
        self.progress_bar.pack(fill='x', pady=(0, 5))
        
        self.status_label = tk.Label(progress_frame,
                                    textvariable=self.status,
                                    font=('Segoe UI', 9),
                                    fg='#666666',
                                    bg='white')
        self.status_label.pack(anchor='w')
        
        # Buttons
        button_frame = tk.Frame(card, bg='white')
        button_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        self.start_button = tk.Button(button_frame,
                                     text="🚀 Start Training System",
                                     command=self.start_training,
                                     font=('Segoe UI', 12, 'bold'),
                                     bg='#28a745',
                                     fg='white',
                                     padx=20,
                                     pady=10,
                                     relief='flat',
                                     cursor='hand2')
        self.start_button.pack(side='left')
        
        tk.Button(button_frame,
                 text="Cancel",
                 command=self.root.quit,
                 font=('Segoe UI', 11),
                 bg='#6c757d',
                 fg='white',
                 padx=15,
                 pady=8,
                 relief='flat',
                 cursor='hand2').pack(side='right')
        
    def on_generate_toggle(self):
        """Handle generate data toggle"""
        # This could enable/disable related controls if needed
        pass
        
    def start_training(self):
        """Start the training process"""
        # Disable start button
        self.start_button.configure(state='disabled', text="Training in Progress...")
        
        # Start training in separate thread
        thread = threading.Thread(target=self.run_training)
        thread.daemon = True
        thread.start()
        
    def run_training(self):
        """Run the actual training process"""
        try:
            # Step 1: Initialize
            self.update_progress(10, "Initializing AI training system...")
            time.sleep(1)
            
            # Step 2: Import modules
            self.update_progress(20, "Loading AI modules...")
            from auto_train_and_classify import AutoTrainAndClassify
            time.sleep(1)
            
            # Step 3: Create system
            self.update_progress(30, "Creating training system...")
            auto_system = AutoTrainAndClassify()
            time.sleep(1)
            
            # Step 4: Generate synthetic data if requested
            if self.generate_data.get():
                self.update_progress(50, f"Generating {self.samples_count.get()} samples per class...")
                auto_system.generate_synthetic_data_files(samples_per_class=self.samples_count.get())
                time.sleep(2)
            
            # Step 5: Start training
            self.update_progress(70, "Training AI models...")
            
            if self.training_mode.get() == "quick":
                auto_system.run_traditional_training()
            elif self.training_mode.get() == "advanced":
                auto_system.run_advanced_training()
            elif self.training_mode.get() == "research":
                results = auto_system.run_advanced_training()
                auto_system.generate_research_report(results)
            
            # Step 6: Complete
            self.update_progress(100, "Training completed successfully!")
            
            # Show success message
            self.root.after(0, lambda: messagebox.showinfo("Success", 
                                                          "🎉 Training completed successfully!\n\n"
                                                          "The AI model is now ready for pile integrity analysis.\n"
                                                          "You can now use the system to analyze pile data."))
            
        except Exception as e:
            error_msg = f"Training failed: {str(e)}"
            self.update_progress(0, error_msg)
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
            
        finally:
            # Re-enable button
            self.root.after(0, lambda: self.start_button.configure(state='normal', text="🚀 Start Training System"))
            
    def update_progress(self, value, status):
        """Update progress bar and status"""
        self.root.after(0, lambda: self.progress.set(value))
        self.root.after(0, lambda: self.status.set(status))
        
    def run(self):
        """Run the application"""
        self.root.eval('tk::PlaceWindow . center')
        self.root.mainloop()

def main():
    """Main function"""
    try:
        app = QuickStartGUI()
        app.run()
    except Exception as e:
        print(f"Error starting Quick Start: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
