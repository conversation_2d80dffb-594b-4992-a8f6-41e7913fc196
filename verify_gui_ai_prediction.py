#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证GUI中AI预测结果
检查是否存在GUI和脚本预测不一致的问题
"""

import pandas as pd
import numpy as np
import sys
import os

def test_gui_ai_prediction():
    """测试GUI中的AI预测"""
    print("🔍 验证GUI中的AI预测结果")
    print("=" * 80)
    
    # 测试文件
    test_files = [
        ("training_data/III/1-2.txt", "III类桩", 2),
        ("training_data/III/KBZ1-51.txt", "III类桩", 2),
        ("training_data/II/KBZ1-67.txt", "II类桩", 1),
        ("training_data/I/3-0.txt", "I类桩", 0)
    ]
    
    try:
        # 导入GUI模块
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        print("✅ 成功导入GUI模块")
        
        # 创建GUI实例（不显示界面）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        gui = PileAnalyzerGZGUI(root)
        
        print("✅ 成功创建GUI实例")
        
        # 测试每个文件
        for file_path, expected_label, expected_class in test_files:
            print(f"\n📁 测试文件: {file_path}")
            print(f"   预期类别: {expected_label}")
            
            if not os.path.exists(file_path):
                print(f"   ❌ 文件不存在")
                continue
            
            try:
                # 读取数据
                df = pd.read_csv(file_path, sep='\t', encoding='utf-8')
                print(f"   📊 数据形状: {df.shape}")
                
                # 模拟GUI的数据加载过程
                gui.df = df
                
                # 切换到AI System V2.0
                if hasattr(gui, 'ai_analyzer_v2'):
                    # 确保使用高精度模型
                    models = gui.ai_analyzer_v2.model_manager.get_available_models()
                    for key, model_info in models.items():
                        if model_info.model_type == 'optimized' and model_info.accuracy > 0.9:
                            gui.ai_analyzer_v2.set_model(key)
                            gui.ai_analyzer_v2.set_feature_extractor('advanced')
                            print(f"   🎯 使用模型: {model_info.name} ({model_info.accuracy:.1%})")
                            break
                    
                    # 进行AI分析
                    result = gui.ai_analyzer_v2.predict(df)
                    
                    if result:
                        predicted_class = result.get('完整性类别', -1)
                        confidence = result.get('ai_confidence', 0.0)
                        
                        class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                        pred_name = class_names.get(predicted_class, f'未知({predicted_class})')
                        
                        print(f"   🤖 AI预测: {pred_name} (置信度: {confidence:.2%})")
                        print(f"   ✅ 预测正确: {'是' if predicted_class == expected_class else '否'}")
                        
                        if predicted_class != expected_class:
                            print(f"   ⚠️ 预测错误! 预期{expected_label}，实际预测{pred_name}")
                    else:
                        print(f"   ❌ AI预测失败")
                else:
                    print(f"   ❌ AI分析器V2未初始化")
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_model_consistency():
    """检查模型一致性"""
    print(f"\n🔍 检查模型一致性")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer = get_ai_analyzer_v2()
        
        # 获取模型信息
        models = analyzer.model_manager.get_available_models()
        print(f"📊 可用模型数量: {len(models)}")
        
        for key, model_info in models.items():
            print(f"  - {model_info.name}: {model_info.accuracy:.1%} ({model_info.model_type})")
        
        # 检查当前使用的模型
        current_model = analyzer.get_current_model_info()
        if current_model:
            print(f"\n🎯 当前模型: {current_model['name']} ({current_model['accuracy']:.1%})")
        
        # 检查特征提取器
        extractors = analyzer.feature_manager.get_available_extractors()
        print(f"\n📊 可用特征提取器: {len(extractors)}")
        for name, info in extractors.items():
            print(f"  - {name}: {info['feature_count']}特征")
        
        current_extractor = analyzer.feature_manager.get_current_extractor_info()
        if current_extractor:
            print(f"\n🎯 当前特征提取器: {current_extractor['name']} ({current_extractor['feature_count']}特征)")
        
    except Exception as e:
        print(f"❌ 模型一致性检查失败: {e}")

def test_direct_prediction():
    """直接测试预测功能"""
    print(f"\n🔍 直接测试预测功能")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer = get_ai_analyzer_v2()
        
        # 设置高精度模型
        models = analyzer.model_manager.get_available_models()
        for key, model_info in models.items():
            if model_info.model_type == 'optimized' and model_info.accuracy > 0.9:
                analyzer.set_model(key)
                analyzer.set_feature_extractor('advanced')
                print(f"🎯 使用模型: {model_info.name}")
                break
        
        # 测试III类桩文件
        test_files = [
            "training_data/III/1-2.txt",
            "training_data/III/KBZ1-51.txt"
        ]
        
        for file_path in test_files:
            print(f"\n📁 测试: {file_path}")
            
            df = pd.read_csv(file_path, sep='\t', encoding='utf-8')
            result = analyzer.predict(df)
            
            if result:
                predicted_class = result.get('完整性类别', -1)
                confidence = result.get('ai_confidence', 0.0)
                
                class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                pred_name = class_names.get(predicted_class, f'未知({predicted_class})')
                
                print(f"  预测结果: {pred_name} (置信度: {confidence:.2%})")
                print(f"  预期结果: III类桩")
                print(f"  是否正确: {'✅' if predicted_class == 2 else '❌'}")
                
                # 显示详细结果
                if 'detailed_analysis' in result:
                    print(f"  详细分析: {result['detailed_analysis']}")
            else:
                print(f"  ❌ 预测失败")
                
    except Exception as e:
        print(f"❌ 直接预测测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔬 GUI AI预测验证")
    print("=" * 100)
    
    # 1. 检查模型一致性
    check_model_consistency()
    
    # 2. 直接测试预测
    test_direct_prediction()
    
    # 3. 测试GUI预测
    test_gui_ai_prediction()
    
    print(f"\n📋 验证总结")
    print("=" * 80)
    print("如果AI预测结果确实是III类桩，那么问题可能在于:")
    print("1. 您在GUI中使用的不是高精度模型(94%)")
    print("2. GUI界面显示有误")
    print("3. 数据加载过程中出现问题")
    print("4. 特征提取器设置不正确")
    
    print(f"\n💡 建议解决方案:")
    print("1. 在GUI中确认选择了'高精度AI模型 v1.0 (94.0%)'")
    print("2. 确认特征提取器设置为'高精度特征提取器'")
    print("3. 重新加载数据文件进行分析")
    print("4. 检查GUI界面的结果显示区域")

if __name__ == "__main__":
    main()
