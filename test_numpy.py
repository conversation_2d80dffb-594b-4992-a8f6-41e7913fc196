#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test NumPy installation and compatibility
"""

try:
    import numpy as np
    print(f"✅ NumPy imported successfully!")
    print(f"NumPy version: {np.__version__}")
    
    # Test basic operations
    arr = np.array([1, 2, 3, 4, 5])
    print(f"Test array: {arr}")
    print(f"Array mean: {np.mean(arr)}")
    
    # Test if numpy._core exists (this was the issue)
    try:
        import numpy._core
        print("✅ numpy._core is available")
    except ImportError:
        print("⚠️ numpy._core not available, but this might be OK for newer versions")
    
    print("✅ NumPy is working correctly!")
    
except ImportError as e:
    print(f"❌ NumPy import failed: {e}")
except Exception as e:
    print(f"❌ NumPy test failed: {e}")

# Test other required packages
packages_to_test = ['pandas', 'matplotlib', 'sklearn', 'tkinter']

for package in packages_to_test:
    try:
        if package == 'sklearn':
            import sklearn
            print(f"✅ {package} version: {sklearn.__version__}")
        elif package == 'tkinter':
            import tkinter as tk
            print(f"✅ {package} is available")
        else:
            module = __import__(package)
            print(f"✅ {package} version: {module.__version__}")
    except ImportError as e:
        print(f"❌ {package} import failed: {e}")
    except Exception as e:
        print(f"⚠️ {package} test warning: {e}")

print("\n🔍 Testing pickle compatibility...")
try:
    import pickle
    import numpy as np
    
    # Create a test array and pickle it
    test_data = np.array([[1, 2, 3], [4, 5, 6]])
    pickled_data = pickle.dumps(test_data)
    unpickled_data = pickle.loads(pickled_data)
    
    print("✅ Pickle with NumPy arrays works correctly!")
    
except Exception as e:
    print(f"❌ Pickle test failed: {e}")

print("\n🎯 All tests completed!")
