#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI Pile Integrity Analysis System - Main Launcher
AI桩基完整性分析系统 - 主启动器

This is the main entry point for the AI Pile Integrity Analysis System.
It provides a clean, modern interface for users to choose their preferred workflow.

Features:
- Clean startup interface
- Multiple workflow options
- Easy navigation
- Professional appearance

Author: AI Pile Integrity Analysis System
Version: 3.0 (Unified Launcher)
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

class MainLauncher:
    """Main launcher for AI Pile Integrity Analysis System"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AI Pile Integrity Analysis System")
        self.root.geometry("800x600")
        self.root.configure(bg='#f8f9fa')
        
        # Modern color scheme
        self.colors = {
            'primary': '#2c3e50',
            'secondary': '#3498db', 
            'success': '#27ae60',
            'warning': '#f39c12',
            'light': '#ecf0f1',
            'white': '#ffffff',
            'text': '#2c3e50'
        }
        
        self.setup_styles()
        self.create_interface()
        
    def setup_styles(self):
        """Setup modern styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        style.configure('Title.TLabel',
                       font=('Segoe UI', 24, 'bold'),
                       foreground=self.colors['primary'],
                       background='#f8f9fa')
        
        style.configure('Subtitle.TLabel',
                       font=('Segoe UI', 12),
                       foreground='#666666',
                       background='#f8f9fa')
        
        style.configure('Card.TFrame',
                       background=self.colors['white'],
                       relief='solid',
                       borderwidth=1)
        
        style.configure('Option.TButton',
                       font=('Segoe UI', 12, 'bold'),
                       padding=(30, 15))
        
    def create_interface(self):
        """Create the main interface"""
        # Header
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=120)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Logo and title
        title_label = tk.Label(header_frame,
                              text="🏗️ AI Pile Integrity Analysis System",
                              font=('Segoe UI', 22, 'bold'),
                              fg=self.colors['white'],
                              bg=self.colors['primary'])
        title_label.pack(pady=(30, 5))
        
        subtitle_label = tk.Label(header_frame,
                                 text="Professional AI-Powered Pile Integrity Assessment Solution",
                                 font=('Segoe UI', 11),
                                 fg=self.colors['light'],
                                 bg=self.colors['primary'])
        subtitle_label.pack()
        
        # Main content
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=40, pady=30)
        
        # Welcome message
        welcome_frame = ttk.Frame(main_frame, style='Card.TFrame', padding=30)
        welcome_frame.pack(fill='x', pady=(0, 30))
        
        welcome_title = ttk.Label(welcome_frame,
                                 text="Welcome to AI Pile Integrity Analysis System",
                                 style='Title.TLabel')
        welcome_title.pack(pady=(0, 10))
        
        welcome_text = ttk.Label(welcome_frame,
                                text="Choose your preferred workflow to get started with pile integrity analysis.\n"
                                     "Our system provides comprehensive AI-powered analysis with modern tools.",
                                style='Subtitle.TLabel')
        welcome_text.pack()
        
        # Options grid
        options_frame = tk.Frame(main_frame, bg='#f8f9fa')
        options_frame.pack(fill='both', expand=True)
        
        # Create option cards
        self.create_option_card(options_frame, 
                               "🚀 Quick Start Training",
                               "Set up and train AI models with guided workflow",
                               "Perfect for first-time users and quick setup",
                               self.launch_modern_gui,
                               row=0, col=0)
        
        self.create_option_card(options_frame,
                               "📊 Data Generation",
                               "Generate synthetic training data for AI models", 
                               "Create high-quality synthetic pile data",
                               self.launch_data_generator,
                               row=0, col=1)
        
        self.create_option_card(options_frame,
                               "🔬 Advanced Analysis",
                               "Full-featured analysis with all AI capabilities",
                               "Complete analysis suite for professionals",
                               self.launch_advanced_analysis,
                               row=1, col=0)
        
        self.create_option_card(options_frame,
                               "📈 Research Mode",
                               "Comprehensive analysis with detailed reports",
                               "In-depth analysis for research purposes",
                               self.launch_research_mode,
                               row=1, col=1)
        
        # Footer
        footer_frame = tk.Frame(self.root, bg=self.colors['light'], height=50)
        footer_frame.pack(fill='x', side='bottom')
        footer_frame.pack_propagate(False)
        
        footer_label = tk.Label(footer_frame,
                               text="AI Pile Integrity Analysis System v3.0 | Professional Grade Solution",
                               font=('Segoe UI', 9),
                               fg=self.colors['text'],
                               bg=self.colors['light'])
        footer_label.pack(pady=15)
        
    def create_option_card(self, parent, title, description, subtitle, command, row, col):
        """Create an option card"""
        card_frame = ttk.Frame(parent, style='Card.TFrame', padding=25)
        card_frame.grid(row=row, column=col, padx=15, pady=15, sticky='nsew')
        
        # Configure grid weights
        parent.grid_rowconfigure(row, weight=1)
        parent.grid_columnconfigure(col, weight=1)
        
        # Icon and title
        title_label = tk.Label(card_frame,
                              text=title,
                              font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['white'])
        title_label.pack(pady=(0, 10))
        
        # Description
        desc_label = tk.Label(card_frame,
                             text=description,
                             font=('Segoe UI', 11),
                             fg=self.colors['text'],
                             bg=self.colors['white'],
                             wraplength=250)
        desc_label.pack(pady=(0, 5))
        
        # Subtitle
        subtitle_label = tk.Label(card_frame,
                                 text=subtitle,
                                 font=('Segoe UI', 9),
                                 fg='#666666',
                                 bg=self.colors['white'],
                                 wraplength=250)
        subtitle_label.pack(pady=(0, 15))
        
        # Launch button
        launch_button = ttk.Button(card_frame,
                                  text="Launch",
                                  command=command,
                                  style='Option.TButton')
        launch_button.pack()
        
    def launch_modern_gui(self):
        """Launch the modern training GUI"""
        try:
            self.root.withdraw()  # Hide launcher
            from modern_pile_gui import ModernPileGUI
            app = ModernPileGUI()
            app.run()
            self.root.deiconify()  # Show launcher again when done
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Modern GUI:\n{str(e)}")
            self.root.deiconify()
            
    def launch_data_generator(self):
        """Launch the data generator"""
        try:
            self.root.withdraw()
            from generate_synthetic_data import show_quick_start
            show_quick_start()
            self.root.deiconify()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Data Generator:\n{str(e)}")
            self.root.deiconify()
            
    def launch_advanced_analysis(self):
        """Launch advanced analysis"""
        try:
            self.root.withdraw()
            from ai_pile_integrity_analyzer import main as analyzer_main
            analyzer_main()
            self.root.deiconify()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Advanced Analysis:\n{str(e)}")
            self.root.deiconify()
            
    def launch_research_mode(self):
        """Launch research mode"""
        try:
            self.root.withdraw()
            from auto_train_and_classify import AutoTrainAndClassify
            
            # Create and run in research mode
            auto_system = AutoTrainAndClassify()
            results = auto_system.run_advanced_training()
            auto_system.generate_research_report(results)
            
            messagebox.showinfo("Research Mode", "Research analysis completed successfully!")
            self.root.deiconify()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Research Mode:\n{str(e)}")
            self.root.deiconify()
            
    def run(self):
        """Run the launcher"""
        # Center the window
        self.root.eval('tk::PlaceWindow . center')
        self.root.mainloop()

def main():
    """Main function"""
    try:
        # Check if we're in the right directory
        if not os.path.exists('ai_pile_integrity_analyzer.py'):
            current_dir = os.path.dirname(os.path.abspath(__file__))
            os.chdir(current_dir)
            
        launcher = MainLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error starting launcher: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
