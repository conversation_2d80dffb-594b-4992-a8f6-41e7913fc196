#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试GZ方法实现的正确性
"""

import pandas as pd
from generate_synthetic_data import GZBasedSyntheticDataGenerator

def test_gz_method():
    """测试GZ方法的实现"""
    generator = GZBasedSyntheticDataGenerator()
    
    # 测试一些已知的参数组合
    test_cases = [
        # (Sp, Ad, Bi_ratio, expected_I_ji, description)
        (105, -1, 0.9, 1, "I类桩：高波速，低波幅差，高波形质量"),
        (95, 0, 0.9, 1, "I类桩：边界情况"),
        (90, 2, 0.7, 2, "II类桩：中等波速，中等波幅差"),
        (80, 6, 0.4, 3, "III类桩：低波速，高波幅差"),
        (60, 10, 0.2, 4, "IV类桩：很低波速，很高波幅差"),
        (50, 15, 0.1, 4, "IV类桩：极低波速，极高波幅差"),
    ]
    
    print("🧪 测试GZ方法I(j,i)值计算:")
    print("=" * 80)
    
    for sp, ad, bi_ratio, expected, description in test_cases:
        calculated = generator.calculate_I_ji(sp, ad, bi_ratio)
        status = "✅" if calculated == expected else "❌"
        print(f"{status} Sp={sp:3d}%, Ad={ad:2d}dB, Bi={bi_ratio:.1f} -> I(j,i)={calculated} (期望:{expected}) - {description}")
    
    print("\n🧪 测试K(i)值计算:")
    print("=" * 80)
    
    # 测试K值计算
    k_test_cases = [
        ([1, 1, 1], 1, "所有剖面都是I(j,i)=1"),
        ([2, 2, 2], 2, "所有剖面都是I(j,i)=2"),
        ([1, 2, 1], 1, "混合情况：主要是1"),
        ([2, 3, 2], 2, "混合情况：主要是2"),
        ([3, 4, 3], 3, "混合情况：主要是3"),
        ([4, 4, 4], 4, "所有剖面都是I(j,i)=4"),
    ]
    
    for i_ji_values, expected, description in k_test_cases:
        calculated = generator.calculate_K_i(i_ji_values)
        status = "✅" if calculated == expected else "❌"
        print(f"{status} I(j,i)={i_ji_values} -> K(i)={calculated} (期望:{expected}) - {description}")
    
    print("\n🧪 测试最终类别判定:")
    print("=" * 80)
    
    # 测试最终类别判定
    category_test_cases = [
        ([1, 1, 1, 1, 1], "I类桩", "所有K值都是1"),
        ([1, 2, 1, 1, 1], "II类桩", "有K=2但无K=3,4"),
        ([1, 2, 3, 1, 1], "III类桩", "有K=3但无K=4"),
        ([1, 2, 4, 1, 1], "IV类桩", "有K=4"),
        ([3, 3, 3, 3, 3, 3], "IV类桩", "连续6个K=3"),
        ([2, 2, 2, 2, 2, 2], "III类桩", "连续6个K=2"),
    ]
    
    for k_values, expected, description in category_test_cases:
        calculated = generator.determine_final_category(k_values)
        status = "✅" if calculated == expected else "❌"
        print(f"{status} K值={k_values} -> {calculated} (期望:{expected}) - {description}")

def test_data_generation():
    """测试数据生成"""
    generator = GZBasedSyntheticDataGenerator()
    
    print("\n🧪 测试数据生成:")
    print("=" * 80)
    
    for pile_class in ['I类桩', 'II类桩', 'III类桩', 'IV类桩']:
        print(f"\n📊 测试 {pile_class} 数据生成...")
        
        # 生成多个样本进行测试
        correct_count = 0
        total_count = 5
        
        for i in range(total_count):
            df = generator.generate_synthetic_pile_data(pile_class, num_depths=50)
            predicted = generator.verify_generated_data(df)
            
            if predicted == pile_class:
                correct_count += 1
                print(f"  ✅ 样本 {i+1}: 正确预测为 {predicted}")
            else:
                print(f"  ❌ 样本 {i+1}: 预测为 {predicted}，期望 {pile_class}")
        
        accuracy = correct_count / total_count * 100
        print(f"📈 {pile_class} 准确率: {accuracy:.1f}% ({correct_count}/{total_count})")

if __name__ == "__main__":
    test_gz_method()
    test_data_generation()
