#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动GUI并确保使用正确的AI设置
自动配置为94%准确率的高精度模型
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def launch_gui_with_optimal_settings():
    """启动GUI并配置最优设置"""
    print("🚀 启动GUI并配置最优AI设置")
    print("=" * 80)

    try:
        # 导入GUI模块
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI

        print("✅ 成功导入GUI模块")

        # 创建GUI实例（会自动创建窗口和设置界面）
        gui = PileAnalyzerGZGUI()
        root = gui.root

        print("✅ 成功创建GUI实例")

        # 自动配置最优设置
        def configure_optimal_settings():
            try:
                print("🔧 配置最优AI设置...")

                # 1. 切换到AI System V2.0
                if hasattr(gui, 'ai_mode_var'):
                    gui.ai_mode_var.set("🚀 AI System V2.0")
                    gui.on_ai_mode_changed()
                    print("✅ 切换到AI System V2.0")

                # 2. 设置高精度模型
                if hasattr(gui, 'ai_analyzer_v2'):
                    models = gui.ai_analyzer_v2.model_manager.get_available_models()

                    # 查找94%准确率的高精度模型
                    target_model_key = None
                    for key, model_info in models.items():
                        if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                            target_model_key = key
                            break

                    if target_model_key:
                        # 设置模型
                        gui.ai_analyzer_v2.set_model(target_model_key)
                        gui.ai_analyzer_v2.set_feature_extractor('advanced')

                        # 更新界面显示
                        if hasattr(gui, 'selected_model_var'):
                            model_info = models[target_model_key]
                            display_name = f"{model_info.name} ({model_info.accuracy:.1%})"
                            gui.selected_model_var.set(display_name)

                        print(f"✅ 设置高精度模型: {model_info.name} ({model_info.accuracy:.1%})")
                        print(f"✅ 设置高精度特征提取器: 118特征")

                        # 显示成功消息
                        messagebox.showinfo(
                            "设置完成",
                            f"✅ 已自动配置最优AI设置:\n\n"
                            f"🤖 模型: {model_info.name}\n"
                            f"📊 准确率: {model_info.accuracy:.1%}\n"
                            f"🔧 特征提取器: 高精度 (118特征)\n\n"
                            f"现在可以加载III类桩数据进行测试:\n"
                            f"• training_data/III/1-2.txt\n"
                            f"• training_data/III/KBZ1-51.txt\n\n"
                            f"预期结果: III类桩 (98%+置信度)"
                        )
                    else:
                        print("⚠️ 未找到94%准确率模型")
                        messagebox.showwarning(
                            "模型未找到",
                            "未找到94%准确率的高精度模型\n请检查模型文件是否存在"
                        )

            except Exception as e:
                print(f"❌ 配置设置失败: {e}")
                messagebox.showerror("配置失败", f"自动配置失败: {str(e)}")

        # 延迟配置，确保GUI完全加载
        root.after(1000, configure_optimal_settings)

        # 添加测试按钮
        def add_test_buttons():
            try:
                if hasattr(gui, 'control_frame'):
                    # 创建测试框架
                    test_frame = tk.LabelFrame(gui.control_frame, text="🧪 III类桩测试",
                                             font=('Segoe UI', 10, 'bold'),
                                             fg='blue', padx=10, pady=5)
                    test_frame.pack(fill='x', pady=(10, 0))

                    def test_iii_file_1():
                        test_file_path = "training_data/III/1-2.txt"
                        if os.path.exists(test_file_path):
                            gui.load_data_file(test_file_path)
                            messagebox.showinfo("测试文件", f"已加载: {test_file_path}\n预期结果: III类桩")
                        else:
                            messagebox.showerror("文件不存在", f"找不到文件: {test_file_path}")

                    def test_iii_file_2():
                        test_file_path = "training_data/III/KBZ1-51.txt"
                        if os.path.exists(test_file_path):
                            gui.load_data_file(test_file_path)
                            messagebox.showinfo("测试文件", f"已加载: {test_file_path}\n预期结果: III类桩")
                        else:
                            messagebox.showerror("文件不存在", f"找不到文件: {test_file_path}")

                    # 测试按钮
                    btn1 = tk.Button(test_frame, text="📁 测试 1-2.txt",
                                   command=test_iii_file_1,
                                   bg='lightblue', font=('Segoe UI', 9))
                    btn1.pack(side='left', padx=(0, 5))

                    btn2 = tk.Button(test_frame, text="📁 测试 KBZ1-51.txt",
                                   command=test_iii_file_2,
                                   bg='lightgreen', font=('Segoe UI', 9))
                    btn2.pack(side='left', padx=(5, 0))

                    print("✅ 添加测试按钮")

            except Exception as e:
                print(f"⚠️ 添加测试按钮失败: {e}")

        # 延迟添加测试按钮
        root.after(2000, add_test_buttons)

        print("🎯 GUI启动完成，正在配置最优设置...")
        print("\n📋 使用说明:")
        print("1. 等待自动配置完成（会显示确认对话框）")
        print("2. 点击测试按钮加载III类桩文件")
        print("3. 点击'🚀 开始AI分析'按钮")
        print("4. 查看结果应该显示'III类桩'")
        print("5. 如果显示其他结果，请检查模型选择")

        # 启动GUI
        root.mainloop()

    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔬 III类桩预测问题解决方案")
    print("=" * 100)

    print("📋 问题分析结果:")
    print("✅ AI System V2.0 实际预测正确 (III类桩)")
    print("✅ 置信度极高 (98.7%+)")
    print("⚠️ 问题可能在GUI设置或显示")

    print(f"\n🚀 解决方案:")
    print("1. 自动启动GUI并配置最优设置")
    print("2. 确保使用94%准确率高精度模型")
    print("3. 确保使用高精度特征提取器")
    print("4. 提供便捷的测试按钮")

    print(f"\n⚡ 启动优化GUI...")
    launch_gui_with_optimal_settings()

if __name__ == "__main__":
    main()
