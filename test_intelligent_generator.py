#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试智能合成数据生成器
"""

import os
import sys
from intelligent_synthetic_data_generator_gui import IntelligentSyntheticDataGenerator

def test_learning():
    """测试学习功能"""
    print("🧪 测试智能数据生成器学习功能")
    print("=" * 60)
    
    # 创建生成器
    generator = IntelligentSyntheticDataGenerator()
    
    # 测试数据目录
    data_directory = "training_data"
    
    if not os.path.exists(data_directory):
        print("❌ 测试数据目录不存在，请先运行数据生成脚本")
        return
    
    print(f"📁 使用数据目录: {data_directory}")
    
    # 学习数据分布
    print("\n🧠 开始学习数据分布...")
    
    def progress_callback(progress, message):
        print(f"  [{progress:5.1f}%] {message}")
    
    try:
        learning_results = generator.learn_from_existing_data(data_directory, progress_callback)
        
        print("\n📊 学习结果:")
        print(f"  状态: {learning_results['learning_status']}")
        print(f"  总样本数: {learning_results['total_samples']}")
        
        for pile_class, info in learning_results['class_distributions'].items():
            print(f"\n📋 {pile_class}:")
            print(f"  样本文件数: {info['sample_count']}")
            print(f"  数据点数: {info['total_points']}")
            print(f"  平均波速: {info['feature_means']['Speed_Mean']:.2f}%")
            print(f"  平均波幅: {info['feature_means']['Amp_Mean']:.2f}dB")
        
        # 测试数据生成
        print("\n🚀 测试数据生成...")
        
        for pile_class in ["I类桩", "II类桩", "III类桩", "IV类桩"]:
            if pile_class in generator.learned_distributions:
                print(f"\n生成 {pile_class} 数据...")
                
                try:
                    generated_data = generator.generate_intelligent_data(pile_class, 2, progress_callback)
                    print(f"  ✅ 成功生成 {len(generated_data)} 行数据")
                    
                    # 验证数据
                    predicted_class = generator.gz_calculator.verify_generated_data(generated_data)
                    print(f"  🎯 GZ验证结果: {predicted_class}")
                    
                    if predicted_class == pile_class:
                        print(f"  ✅ 验证通过！")
                    else:
                        print(f"  ⚠️  验证不匹配，预期: {pile_class}, 实际: {predicted_class}")
                
                except Exception as e:
                    print(f"  ❌ 生成失败: {e}")
            else:
                print(f"  ⚠️  {pile_class} 未学习到分布信息")
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 学习失败: {e}")
        import traceback
        traceback.print_exc()

def test_gz_calculator():
    """测试GZ计算器"""
    print("\n🧮 测试GZ计算器")
    print("=" * 60)
    
    from intelligent_synthetic_data_generator_gui import GZMethodCalculator
    calculator = GZMethodCalculator()
    
    # 测试I(j,i)计算
    test_cases = [
        (105, -1, 0.9, 1, "I类桩标准情况"),
        (90, 2, 0.7, 2, "II类桩标准情况"),
        (80, 6, 0.4, 3, "III类桩标准情况"),
        (60, 10, 0.2, 4, "IV类桩标准情况"),
    ]
    
    print("I(j,i)值计算测试:")
    for sp, ad, bi, expected, desc in test_cases:
        result = calculator.calculate_I_ji(sp, ad, bi)
        status = "✅" if result == expected else "❌"
        print(f"  {status} Sp={sp:3d}%, Ad={ad:2d}dB, Bi={bi:.1f} -> I(j,i)={result} (期望:{expected}) - {desc}")
    
    # 测试数据验证
    print("\n数据验证测试:")
    import pandas as pd
    import numpy as np
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'Depth': [10, 11, 12],
        'S1': [105, 104, 106],
        'A1': [-1, 0, -2],
        'S2': [103, 105, 104],
        'A2': [0, -1, 1],
        'S3': [107, 106, 105],
        'A3': [-2, 1, 0]
    })
    
    predicted = calculator.verify_generated_data(test_data)
    print(f"  测试数据验证结果: {predicted}")

if __name__ == "__main__":
    test_gz_calculator()
    test_learning()
