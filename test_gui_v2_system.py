#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试重构后的GUI系统 - AI System V2.0
"""

import os
import sys
import pandas as pd
import numpy as np

def test_v2_modules():
    """测试V2.0模块"""
    print("🧪 测试AI系统V2.0模块")
    print("=" * 80)
    
    try:
        # 测试增强特征提取器
        print("1. 测试增强特征提取器...")
        from enhanced_feature_extractor import get_feature_manager
        
        feature_manager = get_feature_manager()
        extractors = feature_manager.get_available_extractors()
        
        print(f"✅ 可用特征提取器: {len(extractors)} 个")
        for key, info in extractors.items():
            print(f"  - {info['name']}: {info['feature_count']} 特征")
        
        # 测试模型管理器
        print("\n2. 测试模型管理器...")
        from model_manager import get_model_manager
        
        model_manager = get_model_manager()
        models = model_manager.get_available_models()
        
        print(f"✅ 可用模型: {len(models)} 个")
        for key, model_info in models.items():
            print(f"  - {model_info.name}: {model_info.accuracy:.1%} 准确率")
        
        # 测试AI分析器V2
        print("\n3. 测试AI分析器V2...")
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer_v2 = get_ai_analyzer_v2()
        system_info = analyzer_v2.get_system_info()
        
        print(f"✅ AI分析器V2初始化成功")
        print(f"  - 当前特征提取器: {system_info['current_extractor']['name']}")
        print(f"  - 当前模型: {system_info['current_model']['name'] if system_info['current_model'] else 'None'}")
        
        return True
        
    except Exception as e:
        print(f"❌ V2.0模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_extraction():
    """测试特征提取功能"""
    print("\n🔍 测试特征提取功能")
    print("=" * 80)
    
    try:
        from enhanced_feature_extractor import get_feature_manager
        
        # 创建测试数据
        test_data = {
            'Depth(m)': [0.5, 1.0, 1.5, 2.0, 2.5],
            '1-2 Speed%': [95, 98, 92, 88, 85],
            '1-2 Amp%': [2, 3, 5, 8, 12],
            '1-3 Speed%': [96, 97, 90, 86, 83],
            '1-3 Amp%': [1, 2, 4, 7, 10],
            '2-3 Speed%': [94, 99, 91, 87, 84],
            '2-3 Amp%': [3, 4, 6, 9, 11]
        }
        
        df = pd.DataFrame(test_data)
        print(f"📊 测试数据: {df.shape}")
        
        feature_manager = get_feature_manager()
        
        # 测试标准特征提取器
        print("\n测试标准特征提取器:")
        feature_manager.set_current_extractor('standard')
        features_std, names_std = feature_manager.extract_features(df)
        print(f"✅ 标准特征: {len(features_std)} 个")
        
        # 测试高级特征提取器
        print("\n测试高级特征提取器:")
        feature_manager.set_current_extractor('advanced')
        features_adv, names_adv = feature_manager.extract_features(df)
        print(f"✅ 高级特征: {len(features_adv)} 个")
        
        print(f"\n📈 特征对比:")
        print(f"  - 标准特征提取器: {len(features_std)} 特征")
        print(f"  - 高级特征提取器: {len(features_adv)} 特征")
        print(f"  - 特征增加: {len(features_adv) - len(features_std)} 个 ({(len(features_adv) - len(features_std)) / len(features_std) * 100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_management():
    """测试模型管理功能"""
    print("\n🤖 测试模型管理功能")
    print("=" * 80)
    
    try:
        from model_manager import get_model_manager
        
        model_manager = get_model_manager()
        
        # 获取模型统计信息
        stats = model_manager.get_model_statistics()
        print(f"📊 模型统计:")
        print(f"  - 总模型数: {stats['total_models']}")
        print(f"  - 已加载模型: {stats['loaded_models']}")
        print(f"  - 当前模型: {stats['current_model']}")
        print(f"  - 模型类型分布: {stats['model_types']}")
        
        # 测试模型切换
        models = model_manager.get_available_models()
        
        if len(models) > 1:
            print(f"\n🔄 测试模型切换:")
            
            for i, (key, model_info) in enumerate(models.items()):
                if i >= 2:  # 只测试前两个模型
                    break
                
                try:
                    print(f"  切换到: {model_info.name}")
                    model_manager.set_current_model(key)
                    
                    current_info = model_manager.get_current_model_info()
                    if current_info and current_info.name == model_info.name:
                        print(f"  ✅ 切换成功")
                    else:
                        print(f"  ❌ 切换失败")
                        
                except Exception as e:
                    print(f"  ❌ 切换失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_prediction():
    """测试AI预测功能"""
    print("\n🎯 测试AI预测功能")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        # 创建测试数据
        test_data = {
            'Depth(m)': [0.5, 1.0, 1.5, 2.0, 2.5],
            '1-2 Speed%': [95, 98, 92, 88, 85],
            '1-2 Amp%': [2, 3, 5, 8, 12],
            '1-3 Speed%': [96, 97, 90, 86, 83],
            '1-3 Amp%': [1, 2, 4, 7, 10],
            '2-3 Speed%': [94, 99, 91, 87, 84],
            '2-3 Amp%': [3, 4, 6, 9, 11]
        }
        
        df = pd.DataFrame(test_data)
        
        analyzer_v2 = get_ai_analyzer_v2()
        
        # 获取可用模型
        models = analyzer_v2.model_manager.get_available_models()
        
        print(f"📊 测试数据: {df.shape}")
        print(f"🤖 可用模型: {len(models)} 个")
        
        # 测试每个模型的预测
        for key, model_info in models.items():
            print(f"\n测试模型: {model_info.name}")
            
            try:
                # 设置模型
                analyzer_v2.set_model(key)
                
                # 进行预测
                result = analyzer_v2.predict(df)
                
                if result:
                    category = result.get('完整性类别', 'N/A')
                    confidence = result.get('ai_confidence', 0.0)
                    prediction_time = result.get('prediction_time', 0.0)
                    
                    # 转换数字类别为中文名称
                    category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                    if isinstance(category, (int, float, np.integer, np.floating)):
                        category_name = category_mapping.get(int(category), f'未知类别({category})')
                    else:
                        category_name = category
                    
                    print(f"  ✅ 预测成功:")
                    print(f"    - 类别: {category_name}")
                    print(f"    - 置信度: {confidence:.2%}")
                    if prediction_time > 0:
                        print(f"    - 预测时间: {prediction_time:.3f}s")
                    
                    # 显示类别概率
                    class_probs = result.get('class_probabilities', {})
                    if class_probs:
                        print(f"    - 类别概率:")
                        for class_key, prob in class_probs.items():
                            if isinstance(class_key, (int, float, np.integer, np.floating)):
                                class_name = category_mapping.get(int(class_key), f'类别{class_key}')
                            else:
                                class_name = class_key
                            print(f"      {class_name}: {prob:.2%}")
                else:
                    print(f"  ❌ 预测失败")
                    
            except Exception as e:
                print(f"  ❌ 模型测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成")
    print("=" * 80)
    
    try:
        # 检查GUI模块是否可以导入V2.0系统
        print("检查GUI模块导入...")
        
        # 强制清除模块缓存
        modules_to_clear = [k for k in sys.modules.keys() if 'Pile_analyze_GZ_gui' in k]
        for module in modules_to_clear:
            del sys.modules[module]
        
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        print("✅ GUI模块导入成功")
        
        # 检查V2.0相关属性
        gui_class = PileAnalyzerGZGUI
        
        v2_methods = [
            'switch_ai_system',
            'refresh_model_list',
            'refresh_extractor_list',
            'on_model_selected',
            'on_extractor_selected',
            'update_model_info_display'
        ]
        
        print("检查V2.0方法:")
        for method in v2_methods:
            if hasattr(gui_class, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        print("✅ GUI集成检查完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔬 AI系统V2.0重构测试")
    print("=" * 100)
    
    # 测试结果
    results = {}
    
    # 1. 测试V2.0模块
    results['modules'] = test_v2_modules()
    
    # 2. 测试特征提取
    results['features'] = test_feature_extraction()
    
    # 3. 测试模型管理
    results['models'] = test_model_management()
    
    # 4. 测试AI预测
    results['prediction'] = test_ai_prediction()
    
    # 5. 测试GUI集成
    results['gui'] = test_gui_integration()
    
    # 总结
    print("\n📋 测试总结")
    print("=" * 80)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name.capitalize()}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过 ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 AI系统V2.0重构成功！")
        print("\n🚀 新功能特性:")
        print("✅ 多种特征提取方法 (54特征 vs 118特征)")
        print("✅ 模型版本管理 (标准模型 vs 高精度模型)")
        print("✅ 用户友好的模型选择界面")
        print("✅ 实时模型信息显示")
        print("✅ 自动兼容性检查")
        print("✅ 性能监控和计时")
        print("✅ 向后兼容V1.0系统")
        
        print(f"\n📖 使用指南:")
        print("1. 启动GUI: python Pile_analyze_GZ_gui.py")
        print("2. 选择AI系统版本 (V1.0 或 V2.0)")
        print("3. 在V2.0中选择模型和特征提取器")
        print("4. 加载数据并运行AI分析")
        print("5. 享受95%以上的高精度分析！")
        
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ AI系统V2.0基本功能正常")
        print("⚠️ 部分功能需要进一步完善")
    else:
        print("\n❌ AI系统V2.0存在较多问题")
        print("💡 建议检查依赖环境和模型文件")

if __name__ == "__main__":
    main()
