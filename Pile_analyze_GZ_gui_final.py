#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Professional GUI for Pile Integrity Analyzer (GZ Method)
桩基完整性分析系统专业GUI (GZ方法)

This GUI provides a commercial-grade interface for:
- Traditional pile integrity analysis using GZ method
- AI-enhanced analysis with machine learning
- Comparative analysis between methods
- Advanced visualization and reporting
- Model training and configuration

Author: Pile Integrity Analysis System (GZ Method)
Version: 1.1 (Fixed Prediction Logic)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from matplotlib.ticker import MaxNLocator
import matplotlib.colors as mcolors
import matplotlib.font_manager as fm
import seaborn as sns
from datetime import datetime
import time
import traceback # Added for better error logging

# Import AI and ML libraries for built-in AI functionality
import pickle
import joblib
from sklearn.ensemble import IsolationForest, RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# --- Font Configuration for Chinese Characters ---
def configure_chinese_fonts():
    """
    Configure matplotlib to properly display Chinese characters.
    Tries multiple Chinese fonts and provides fallback options.
    """
    # List of Chinese fonts to try, in order of preference
    chinese_fonts = [
        'Microsoft YaHei',  # 微软雅黑 (common on Windows)
        'SimHei',           # 黑体 (common on Windows)
        'SimSun',           # 宋体 (common on Windows)
        'KaiTi',            # 楷体 (common on Windows)
        'FangSong',         # 仿宋 (common on Windows)
        'PingFang SC',      # 苹方 (common on macOS)
        'Hiragino Sans GB', # 冬青黑体 (common on macOS)
        'WenQuanYi Micro Hei', # 文泉驿微米黑 (common on Linux)
        'Noto Sans CJK SC', # Google Noto (cross-platform)
        'Source Han Sans SC' # Adobe Source Han Sans (cross-platform)
    ]

    available_fonts = [f.name for f in fm.fontManager.ttflist]
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break

    if selected_font:
        try:
            plt.rcParams['font.sans-serif'] = [selected_font] + plt.rcParams['font.sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            test_fig, test_ax = plt.subplots(figsize=(1, 1))
            test_ax.text(0.5, 0.5, '测试', fontsize=12, ha='center', va='center')
            plt.close(test_fig)
            print(f"成功配置中文字体: {selected_font}")
            return True
        except Exception as e:
            print(f"警告: 配置字体 {selected_font} 时出错: {e}")

    print("警告: 未找到可用的中文字体。图表中的中文可能显示为方块。")
    print("建议安装以下字体之一以获得更好的中文显示效果:")
    for font in chinese_fonts[:5]:
        print(f"  - {font}")
    try:
        plt.rcParams['axes.unicode_minus'] = False
    except:
        pass
    return False

# --- GZ Method Core Calculation Logic ---
DEFAULT_GZ_CONFIG = {
    'Sp_conditions': {
        'ge_100': lambda sp: sp >= 100, '85_lt_100': lambda sp: 85 <= sp < 100,
        '75_lt_85': lambda sp: 75 <= sp < 85, '65_lt_75': lambda sp: 65 <= sp < 75,
        'lt_65': lambda sp: sp < 65, 'ge_85': lambda sp: sp >= 85,
        'ge_75': lambda sp: sp >= 75, 'ge_65': lambda sp: sp >= 65,
    },
    'Ad_conditions': {
        'le_0': lambda ad: ad <= 0, 'gt_0_le_4': lambda ad: 0 < ad <= 4,
        'gt_4_le_8': lambda ad: 4 < ad <= 8, 'gt_8_le_12': lambda ad: 8 < ad <= 12,
        'gt_12': lambda ad: ad > 12, 'le_4': lambda ad: ad <=4,
        'le_8': lambda ad: ad <=8, 'le_12': lambda ad: ad <=12,
    },
    'Bi_ratio_conditions': {
        'gt_08': lambda br: br > 0.8, 'gt_05_le_08': lambda br: 0.5 < br <= 0.8,
        'gt_05': lambda br: br > 0.5, 'gt_025_le_05': lambda br: 0.25 < br <= 0.5,
        'gt_025': lambda br: br > 0.25, 'le_025': lambda br: br <= 0.25,
    }
}

def calculate_I_ji(Sp, Ad, Bi_ratio, config=DEFAULT_GZ_CONFIG):
    sp_cond = config['Sp_conditions']
    ad_cond = config['Ad_conditions']
    br_cond = config['Bi_ratio_conditions']
    if br_cond['gt_08'](Bi_ratio):
        if sp_cond['ge_100'](Sp) and ad_cond['le_0'](Ad): return 1
        if sp_cond['85_lt_100'](Sp) and ad_cond['le_0'](Ad): return 1
        if sp_cond['ge_100'](Sp) and ad_cond['gt_0_le_4'](Ad): return 1
    if br_cond['gt_05_le_08'](Bi_ratio) and \
       sp_cond['85_lt_100'](Sp) and ad_cond['gt_0_le_4'](Ad): return 2
    if br_cond['gt_05'](Bi_ratio):
        if sp_cond['75_lt_85'](Sp) and ad_cond['le_4'](Ad): return 2
        if sp_cond['ge_85'](Sp) and ad_cond['gt_4_le_8'](Ad): return 2
    if br_cond['gt_025_le_05'](Bi_ratio) and \
       sp_cond['75_lt_85'](Sp) and ad_cond['gt_4_le_8'](Ad): return 3
    if br_cond['gt_025'](Bi_ratio):
        if sp_cond['65_lt_75'](Sp) and ad_cond['le_8'](Ad): return 3
        if sp_cond['ge_75'](Sp) and ad_cond['gt_8_le_12'](Ad): return 3
    if br_cond['le_025'](Bi_ratio):
        if sp_cond['65_lt_75'](Sp) and ad_cond['gt_8_le_12'](Ad): return 4
        if sp_cond['lt_65'](Sp) and ad_cond['le_12'](Ad): return 4
        if sp_cond['ge_65'](Sp) and ad_cond['gt_12'](Ad): return 4
    if br_cond['gt_08'](Bi_ratio):
        if (sp_cond['75_lt_85'](Sp) and ad_cond['le_4'](Ad)) or \
           (sp_cond['ge_85'](Sp) and ad_cond['gt_4_le_8'](Ad)): return 2
        if (sp_cond['65_lt_75'](Sp) and ad_cond['le_8'](Ad)) or \
           (sp_cond['ge_75'](Sp) and ad_cond['gt_8_le_12'](Ad)): return 3
        if (sp_cond['lt_65'](Sp)) or (ad_cond['gt_12'](Ad)): return 4
    if Bi_ratio <= 0.25: return 4
    if 0.25 < Bi_ratio <= 0.5: return 3
    if 0.5 < Bi_ratio <= 0.8: return 2
    print(f"Warning: Unclassified I(j,i) for Sp={Sp}, Ad={Ad}, Bi_ratio={Bi_ratio}. Defaulting to 2.")
    return 2

def calculate_K_i(I_ji_values_at_depth):
    if not I_ji_values_at_depth: return 0
    valid_I_ji = [i_val for i_val in I_ji_values_at_depth if i_val in [1, 2, 3, 4]]
    if not valid_I_ji: return 0
    sum_I_ji_sq = sum(i_val**2 for i_val in valid_I_ji)
    sum_I_ji = sum(valid_I_ji)
    if sum_I_ji == 0: return 0
    K_i_float = (sum_I_ji_sq / sum_I_ji) + 0.5
    return int(K_i_float)

def check_consecutive_K(K_values_with_depths, target_K, num_consecutive=6, depth_interval=0.1):
    if num_consecutive <= 0: return False, -1
    sorted_k_data = sorted(K_values_with_depths.items())
    if len(sorted_k_data) < num_consecutive: return False, -1
    for i in range(len(sorted_k_data) - num_consecutive + 1):
        window = sorted_k_data[i : i + num_consecutive]
        all_target_K = all(item[1] == target_K for item in window)
        if not all_target_K: continue
        start_depth_of_window = window[0][0]
        end_depth_of_window = window[-1][0]
        actual_span = end_depth_of_window - start_depth_of_window
        expected_span = (num_consecutive - 1) * depth_interval
        if abs(actual_span - expected_span) < (depth_interval / 2.0):
            return True, start_depth_of_window
    return False, -1

def determine_final_category(K_values_map_with_depths):
    report_details = []
    if not K_values_map_with_depths:
        return "N/A", ["没有计算K值。"]
    K_values_list = list(K_values_map_with_depths.values())
    has_K4 = any(k == 4 for k in K_values_list)
    has_K3 = any(k == 3 for k in K_values_list)
    has_K2 = any(k == 2 for k in K_values_list)

    if has_K4:
        report_details.append("桩身存在K(i)=4的检测横截面。")
        return "IV类桩", report_details
    consecutive_K3_found, k3_start_depth = check_consecutive_K(K_values_map_with_depths, target_K=3, num_consecutive=6)
    if consecutive_K3_found:
        report_details.append(f"在深度 {k3_start_depth:.2f}m 开始的约50cm范围内K(i)值均为3。")
        return "IV类桩", report_details
    if has_K3:
        num_K3 = K_values_list.count(3)
        if num_K3 == 1:
            report_details.append("所有检测截面仅存在一个K(i)=3的情况，且不存在Ki=4。")
            return "III类桩", report_details
        if num_K3 > 1:
            k3_depths = sorted([d for d, k in K_values_map_with_depths.items() if k == 3])
            adjacent_k3_too_close = False
            for i in range(len(k3_depths) - 1):
                if (k3_depths[i+1] - k3_depths[i]) < 0.5:
                    adjacent_k3_too_close = True
                    report_details.append(f"存在相邻K(i)=3的截面距离小于50cm (例如深度 {k3_depths[i]:.2f}m 和 {k3_depths[i+1]:.2f}m)。")
                    break
            if not adjacent_k3_too_close:
                 report_details.append("所有检测截面存在多个K(i)=3，无Ki=4，且任意两个相邻Ki=3截面距离≥50cm。")
                 return "III类桩", report_details
            else:
                 report_details.append("存在多个K(i)=3，无Ki=4，但部分相邻Ki=3截面距离<50cm (未形成IV类条件)。")
                 return "III类桩", report_details
        report_details.append("桩身存在K(i)=3的检测横截面 (未满足IV类条件，且不符合特定III类细则)。")
        return "III类桩", report_details
    consecutive_K2_found, k2_start_depth = check_consecutive_K(K_values_map_with_depths, target_K=2, num_consecutive=6)
    if consecutive_K2_found and not has_K3 and not has_K4:
        report_details.append(f"在深度 {k2_start_depth:.2f}m 开始的约50cm范围内K(i)值均为2，且无Ki=3, Ki=4。")
        return "III类桩", report_details
    if has_K2 and not has_K3 and not has_K4:
        num_K2 = K_values_list.count(2)
        if num_K2 == 1:
            report_details.append("所有检测截面仅存在一个K(i)=2，且无Ki=3, Ki=4。")
            return "II类桩", report_details
        if num_K2 > 1:
            report_details.append("所有检测截面存在多个K(i)=2，无Ki=3, Ki=4，且不存在某深度50cm范围内K(i)值均为2。")
            return "II类桩", report_details
    if all(k == 1 for k in K_values_list):
        report_details.append("桩身各检测横截面完整性类别指数K(i)均为1。")
        return "I类桩", report_details
    report_details.append("未能明确分类，或数据不满足任何明确的I-IV类桩条件。请检查K值分布。")
    return "未定类别", report_details

# --- Built-in AI Analysis Engine ---
class BuiltInAIAnalyzer:
    def __init__(self, config=None):
        self.config = config or {
            '正常': {'speed': (90.0, 1000.0), 'amp': (-100, 3)},
            '轻微畸变': {'speed': (80.0, 90.0), 'amp': (3, 6)},
            '明显畸变': {'speed': (70.0, 80.0), 'amp': (6, 12)},
            '严重畸变': {'speed': (0.0, 70.0), 'amp': (12, 100)},
            'continuous_threshold': 0.5
        }
        self.classifier_model = None
        self.anomaly_detector = None
        self.scaler = None
        self.feature_importance = {}
        self.training_data = []
        self.feature_extractor = None # For models with embedded feature extractors
        self.feature_selector = None  # For models with embedded feature selectors
        self.preprocessor = None      # For models with a full preprocessing pipeline
        self.is_optimized_model = False # Flag to indicate if a special model is loaded
        self.loaded_model_path = None # Track path of loaded model for V1 logic

        self._initialize_models()
        self._initial_training()

    def _initialize_models(self):
        try:
            self.classifier_model = RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42, class_weight='balanced')
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
            self.scaler = StandardScaler()
            print("✅ AI models initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize AI models: {str(e)}")

    def _initial_training(self):
        try:
            print("🔧 Performing initial AI model training...")
            success = self.train_models() # Train with synthetic data
            if success:
                print("✅ Initial AI model training completed")
            else:
                print("⚠️ Initial AI model training failed, will train on first use")
        except Exception as e:
            print(f"⚠️ Initial training error: {str(e)}, will train on first use")

    def _standardize_column_names(self, df_orig):
        df = df_orig.copy()
        column_mapping = {
            'Depth(m)': 'Depth', '1-2 Speed%': 'S1', '1-2 Amp%': 'A1',
            '1-3 Speed%': 'S2', '1-3 Amp%': 'A2', '2-3 Speed%': 'S3', '2-3 Amp%': 'A3'
        }
        df.rename(columns=column_mapping, inplace=True)
        return df

    def extract_features(self, data_df_orig):
        """
        Extracts features from pile integrity data.
        Prioritizes using self.feature_extractor if an optimized model is loaded.
        Otherwise, falls back to standard GUI feature extraction.
        """
        try:
            data_df = self._standardize_column_names(data_df_orig.copy())

            # Path 1: Model has its own feature_extractor
            if hasattr(self, 'is_optimized_model') and self.is_optimized_model and \
               hasattr(self, 'feature_extractor') and self.feature_extractor is not None:
                print("🔧 Using model's specific feature_extractor.")
                # Assuming self.feature_extractor has an 'extract_features' method
                # or is a scikit-learn compatible transformer.
                if hasattr(self.feature_extractor, 'extract_features') and callable(self.feature_extractor.extract_features):
                    # This interface matches how optimized_auto_train_classify_gui might save it
                    extracted_features, feature_names = self.feature_extractor.extract_features(data_df) # Pass standardized df
                    if extracted_features.ndim == 1:
                        extracted_features = extracted_features.reshape(1, -1)
                    print(f"✅ Model's feature_extractor produced: {extracted_features.shape[1]} features.")
                    return extracted_features, feature_names
                elif hasattr(self.feature_extractor, 'transform') and callable(self.feature_extractor.transform):
                    # Handle scikit-learn transformer case (might need data prep)
                    print("🔧 Model's feature_extractor is a transformer. Applying transform.")
                    # This path needs careful testing based on how feature_extractor is saved.
                    # For now, assume it can take the standardized DataFrame.
                    extracted_features = self.feature_extractor.transform(data_df)
                    if extracted_features.ndim == 1:
                        extracted_features = extracted_features.reshape(1, -1)
                    # Feature names might not be available directly from a generic transformer
                    feature_names = [f"feature_{i}" for i in range(extracted_features.shape[1])]
                    if hasattr(self.feature_extractor, 'get_feature_names_out'):
                        try:
                            feature_names = self.feature_extractor.get_feature_names_out()
                        except: # Fallback if get_feature_names_out fails or not applicable
                             pass
                    elif hasattr(self.feature_extractor, 'feature_names'): # Custom attribute
                        feature_names = self.feature_extractor.feature_names

                    print(f"✅ Model's transformer feature_extractor produced: {extracted_features.shape[1]} features.")
                    return extracted_features, feature_names
                else:
                    print(f"⚠️ Model's feature_extractor does not have a recognized interface. Falling back to standard extraction.")
            
            # Path 2: Standard GUI feature extraction (54 features)
            print("🔧 Using standard GUI feature extractor (54 features).")
            features = []
            feature_names = []
            for col in ['S1', 'S2', 'S3', 'A1', 'A2', 'A3']:
                if col in data_df.columns:
                    values = data_df[col].dropna()
                    if len(values) > 0:
                        features.extend([values.mean(), values.std(), values.min(), values.max(), values.median()])
                        feature_names.extend([f'{col}_mean', f'{col}_std', f'{col}_min', f'{col}_max', f'{col}_median'])
            
            speed_cols = ['S1', 'S2', 'S3']
            speed_data = data_df[speed_cols].dropna()
            if not speed_data.empty:
                avg_speed = speed_data.mean(axis=1)
                features.extend([
                    avg_speed.mean(), avg_speed.std(),
                    (avg_speed < 70).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                    (avg_speed < 80).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                    (avg_speed < 90).sum() / len(avg_speed) if len(avg_speed) > 0 else 0
                ])
                feature_names.extend(['avg_speed_mean', 'avg_speed_std', 'severe_speed_ratio', 'obvious_speed_ratio', 'light_speed_ratio'])

            amp_cols = ['A1', 'A2', 'A3']
            amp_data = data_df[amp_cols].dropna()
            if not amp_data.empty:
                avg_amp = amp_data.mean(axis=1)
                features.extend([
                    avg_amp.mean(), avg_amp.std(),
                    (avg_amp > 12).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                    (avg_amp > 8).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                    (avg_amp > 6).sum() / len(avg_amp) if len(avg_amp) > 0 else 0
                ])
                feature_names.extend(['avg_amp_mean', 'avg_amp_std', 'severe_amp_ratio', 'obvious_amp_ratio', 'light_amp_ratio'])

            if 'Depth' in data_df.columns and not data_df['Depth'].empty:
                depth_range = data_df['Depth'].max() - data_df['Depth'].min()
                features.extend([data_df['Depth'].min(), data_df['Depth'].max(), depth_range, len(data_df)])
                feature_names.extend(['depth_min', 'depth_max', 'depth_range', 'num_points'])

            target_feature_count = 54
            if len(speed_cols) > 0 and len(amp_cols) > 0:
                for i, speed_col in enumerate(speed_cols):
                    for j, amp_col in enumerate(amp_cols):
                        if speed_col in data_df.columns and amp_col in data_df.columns:
                            corr = data_df[speed_col].corr(data_df[amp_col])
                            features.append(corr if not np.isnan(corr) else 0.0)
                            feature_names.append(f'{speed_col}_{amp_col}_corr')
            for col in ['S1', 'S2', 'S3', 'A1', 'A2', 'A3']:
                if col in data_df.columns and 'Depth' in data_df.columns and len(data_df['Depth']) > 1 and len(data_df[col]) > 1:
                    try:
                        # Ensure Depth and col have same length after potential dropna earlier
                        valid_indices = data_df['Depth'].notna() & data_df[col].notna()
                        if sum(valid_indices) > 1:
                             slope = np.polyfit(data_df.loc[valid_indices, 'Depth'], data_df.loc[valid_indices, col], 1)[0]
                             features.append(slope)
                        else:
                            features.append(0.0)
                    except Exception: # Catch LinAlgError if data is singular
                        features.append(0.0)
                    feature_names.append(f'{col}_trend')
                elif len(features) < target_feature_count : # ensure we add a feature if polyfit fails
                    features.append(0.0)
                    feature_names.append(f'{col}_trend_fallback')


            remaining_features = target_feature_count - len(features)
            if remaining_features > 0:
                features.extend([0.0] * remaining_features)
                feature_names.extend([f'padding_{i}' for i in range(remaining_features)])
            
            final_features = np.array(features[:target_feature_count]).reshape(1, -1)
            final_feature_names = feature_names[:target_feature_count]
            print(f"✅ Standard GUI feature_extractor produced: {final_features.shape[1]} features.")
            return final_features, final_feature_names

        except Exception as e:
            print(f"❌ Feature extraction error: {str(e)}")
            traceback.print_exc()
            return np.array([]).reshape(1, -1), []

    def generate_synthetic_training_data(self, num_samples=1000):
        try:
            training_features = []
            training_labels = []
            categories = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
            # Assuming 54 features for synthetic data generation based on standard extractor
            num_speed_features = 15 
            num_amp_features = 15
            num_other_features = 54 - num_speed_features - num_amp_features

            for category in categories:
                for _ in range(num_samples // len(categories)):
                    if category == 'I类桩':
                        speed_features = np.random.normal(95, 5, num_speed_features)
                        amp_features = np.random.normal(2, 1, num_amp_features)
                    elif category == 'II类桩':
                        speed_features = np.random.normal(85, 5, num_speed_features)
                        amp_features = np.random.normal(5, 2, num_amp_features)
                    elif category == 'III类桩':
                        speed_features = np.random.normal(75, 5, num_speed_features)
                        amp_features = np.random.normal(9, 3, num_amp_features)
                    else:  # IV类桩
                        speed_features = np.random.normal(60, 10, num_speed_features)
                        amp_features = np.random.normal(15, 5, num_amp_features)
                    
                    other_features_array = np.random.normal(0, 1, num_other_features)
                    features = np.concatenate([speed_features, amp_features, other_features_array])
                    training_features.append(features)
                    training_labels.append(category)
            
            self.training_data = list(zip(training_features, training_labels))
            print(f"✅ Generated {len(training_features)} synthetic training samples with {len(training_features[0])} features each.")
            return np.array(training_features), np.array(training_labels)
        except Exception as e:
            print(f"❌ Synthetic data generation error: {str(e)}")
            return np.array([]), np.array([])

    def train_models(self, features=None, labels=None):
        try:
            if features is None or labels is None or len(features) == 0:
                print("🔧 No explicit training data provided, generating synthetic data for training.")
                features, labels = self.generate_synthetic_training_data()

            if len(features) == 0:
                print("❌ No training data available, cannot train models.")
                return False

            print(f"🏋️ Training models with data of shape: {features.shape}")
            
            # Ensure scaler is fitted
            try:
                features_scaled = self.scaler.fit_transform(features)
            except Exception as scale_fit_error:
                print(f"❌ Error fitting scaler: {scale_fit_error}. Re-initializing scaler.")
                self.scaler = StandardScaler()
                features_scaled = self.scaler.fit_transform(features)

            self.classifier_model.fit(features_scaled, labels)
            self.anomaly_detector.fit(features_scaled)

            if hasattr(self.classifier_model, 'feature_importances_'):
                # Use actual feature names if available, otherwise generic names
                # This depends on whether 'labels' (and thus features) came from real data with names
                # or synthetic data. For synthetic, it's fine.
                # For now, assume number of features matches.
                num_feats = features.shape[1]
                feature_names_for_importance = [f'feature_{i}' for i in range(num_feats)]
                # If called from train_ai_model with real data, feature_names might be available.
                # However, this method's signature doesn't take feature_names.
                self.feature_importance = dict(zip(feature_names_for_importance, self.classifier_model.feature_importances_))
            
            print("✅ AI models trained successfully")
            return True
        except Exception as e:
            print(f"❌ Model training error: {str(e)}")
            traceback.print_exc()
            return False

    def predict(self, data_df): # Changed to accept data_df
        """Make predictions using trained models. Handles different model types."""
        try:
            print(f"🤖 AI Predict called with data_df of shape: {data_df.shape}")
            # Step 1: Extract features. This method will internally decide if to use
            # the model's specific feature_extractor or the standard one.
            features, feature_names = self.extract_features(data_df)

            if features.size == 0:
                print("❌ No features extracted from data_df in predict method.")
                return None
            
            print(f"⚙️ Features extracted for prediction, shape: {features.shape}")

            # Step 2: Route to the correct prediction logic based on model type
            if hasattr(self, 'is_optimized_model') and self.is_optimized_model:
                print("✨ Routing to optimized model prediction logic.")
                # This internal method will handle specific scaling/selection if needed
                return self._predict_with_optimized_model_internal(features, data_df) 
            else:
                # Standard prediction logic
                print("🔧 Routing to standard model prediction logic.")
                if self.classifier_model is None or not hasattr(self.classifier_model, 'classes_'):
                    print("⚠️ Standard classifier model not available or not trained. Attempting to train with synthetic data...")
                    if not self.train_models(): # This will use synthetic data
                        print("❌ Failed to train standard model, cannot predict.")
                        return None
                
                # Ensure scaler is fitted (might have been fitted with synthetic data if model was just trained)
                try:
                    if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None: # Check if scaler is fitted
                        print("⚠️ Standard scaler not fitted. Fitting with current features (or synthetic if model was just trained).")
                        # This is tricky. If we fit here with just one sample, it's bad.
                        # train_models() should have ensured scaler is fit.
                        # If we reach here and scaler is not fit, it implies an issue in initialization or training call.
                        # For safety, if classifier_model was just trained, scaler should be fine.
                        # If classifier_model existed but scaler is not fit, it's an inconsistent state.
                        # Let's assume train_models() handles scaler fitting.
                        pass # Scaler should be fitted by train_models or load_models
                    features_scaled = self.scaler.transform(features)
                except Exception as scale_error:
                    print(f"⚠️ Scaler error during standard prediction: {scale_error}. Retraining models as a fallback.")
                    if not self.train_models(): # Fallback to retrain with synthetic data
                         print("❌ Failed to retrain models after scaler error.")
                         return None
                    features_scaled = self.scaler.transform(features) # Try again

                prediction_numeric = self.classifier_model.predict(features_scaled)[0]
                probabilities = self.classifier_model.predict_proba(features_scaled)[0]
                class_names_raw = self.classifier_model.classes_

                # Map numeric prediction and class names to Chinese strings
                category_mapping = {0: 'I', 1: 'II', 2: 'III', 3: 'IV'}
                # Handle cases where classes_ might already be strings from synthetic training
                if all(isinstance(c, str) for c in class_names_raw): # If already strings
                    predicted_category_str = str(prediction_numeric) # prediction_numeric should be a string then
                    class_probabilities_mapped = dict(zip(class_names_raw, probabilities))
                else: # Assume numeric, needs mapping
                    predicted_category_str = category_mapping.get(int(prediction_numeric), 'IV')  # 确保默认返回最高风险等级
                    class_probabilities_mapped = {}
                    for i, class_label_raw in enumerate(class_names_raw):
                        class_name_str = category_mapping.get(int(class_label_raw), str(class_label_raw))
                        class_probabilities_mapped[class_name_str] = probabilities[i]
                
                anomaly_score = self.anomaly_detector.decision_function(features_scaled)[0]
                is_anomaly = self.anomaly_detector.predict(features_scaled)[0] == -1
                max_prob = np.max(probabilities)
                confidence = max_prob

                result = {
                    '完整性类别': predicted_category_str,
                    'ai_confidence': confidence,
                    'anomaly_score': anomaly_score,
                    'is_anomaly': is_anomaly,
                    'class_probabilities': class_probabilities_mapped,
                    'feature_importance': self.feature_importance, # From training
                    'overall_reasoning': self._generate_reasoning(predicted_category_str, confidence, anomaly_score, class_probabilities_mapped)
                }
                print(f"🎯 标准模型预测结果: {predicted_category_str} | 置信度: {confidence:.2%}")
                return result

        except Exception as e:
            print(f"❌ Prediction error in main predict method: {str(e)}")
            traceback.print_exc()
            return None

    def _predict_with_optimized_model_internal(self, features, data_df_for_preprocessor=None):
        """
        Internal prediction logic for optimized models.
        'features' are assumed to be correctly extracted by self.extract_features()
        which should have used model's self.feature_extractor if it was present.
        """
        try:
            print(f"🚀 Inside _predict_with_optimized_model_internal with features shape: {features.shape}")
            final_features_for_model = None

            # Path 1: Model with scaler and feature_selector (typical for enhanced training system)
            if hasattr(self, 'scaler') and self.scaler and \
               hasattr(self, 'feature_selector') and self.feature_selector:
                print("🚀 Applying model's scaler and feature_selector.")
                # Ensure scaler is fitted
                if not hasattr(self.scaler, 'mean_') or self.scaler.mean_ is None: # A check if scaler is fitted
                     print("CRITICAL ERROR: Optimized model's scaler is not fitted. Cannot proceed.")
                     return None
                features_scaled = self.scaler.transform(features)
                
                # Ensure feature_selector is fitted
                # Checking for a transform method is usually enough, fitting is implicit for selectors
                features_selected = self.feature_selector.transform(features_scaled)
                final_features_for_model = features_selected
                print(f"⚙️ After scaler & selector, features shape: {final_features_for_model.shape}")

            # Path 2: Model with a 'preprocessor' object that does all further steps
            elif hasattr(self, 'preprocessor') and self.preprocessor:
                print("🚀 Applying model's preprocessor.")
                # This preprocessor might work on 'features' (output of feature_extractor)
                # or it might expect the original data_df if it includes feature extraction itself.
                # Current structure implies it works on 'features' that came from self.extract_features
                final_features_for_model = self.preprocessor.transform(features)
                print(f"⚙️ After preprocessor, features shape: {final_features_for_model.shape}")
            
            # Path 3: Model might only have a scaler
            elif hasattr(self, 'scaler') and self.scaler:
                print("🚀 Applying model's scaler only.")
                if not hasattr(self.scaler, 'mean_') or self.scaler.mean_ is None:
                     print("CRITICAL ERROR: Optimized model's scaler is not fitted. Cannot proceed.")
                     return None
                final_features_for_model = self.scaler.transform(features)
                print(f"⚙️ After scaler, features shape: {final_features_for_model.shape}")
            else:
                print("🚀 Using features directly for optimized model (no further scaler/selector/preprocessor found).")
                final_features_for_model = features
                print(f"⚙️ Using direct features, shape: {final_features_for_model.shape}")

            if final_features_for_model is None:
                print("CRITICAL ERROR: final_features_for_model is None before prediction.")
                return None
            if self.classifier_model is None:
                print("CRITICAL ERROR: classifier_model is None for optimized model.")
                return None


            # Perform prediction
            prediction_numeric = self.classifier_model.predict(final_features_for_model)[0]
            probabilities = self.classifier_model.predict_proba(final_features_for_model)[0]
            
            class_names_raw = self.classifier_model.classes_
            category_mapping_internal = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩',
                                         'I类桩':'I类桩', 'II类桩':'II类桩', 'III类桩':'III类桩', 'IV类桩':'IV类桩'} # Handle if classes_ are already strings

            # Convert predicted label
            predicted_category_str = category_mapping_internal.get(prediction_numeric, str(prediction_numeric))
            if isinstance(prediction_numeric, (int, float)): # Ensure mapping if numeric
                 predicted_category_str = category_mapping_internal.get(int(prediction_numeric), str(prediction_numeric))


            class_probabilities_mapped = {}
            for i, class_label_raw in enumerate(class_names_raw):
                class_name_str = category_mapping_internal.get(class_label_raw, str(class_label_raw))
                if isinstance(class_label_raw, (int,float)): # Ensure mapping if numeric
                    class_name_str = category_mapping_internal.get(int(class_label_raw), str(class_label_raw))
                class_probabilities_mapped[class_name_str] = probabilities[i]
            
            anomaly_score = -0.05 
            is_anomaly = False    
            max_prob = np.max(probabilities)
            confidence = max_prob

            # Try to get feature importance if the model supports it and names are available
            current_feature_importance = {}
            if hasattr(self.classifier_model, 'feature_importances_'):
                num_model_features = len(self.classifier_model.feature_importances_)
                # Get feature names after selection/preprocessing if possible
                # This is complex as names can change. For now, use generic if specific names aren't tracked.
                # The 'feature_names' from extract_features might not match features after selection.
                # The feature_importance stored in self.feature_importance is from the *training* phase.
                # If the number of features matches, we can assume it's relevant.
                if len(self.feature_importance) == num_model_features:
                     current_feature_importance = self.feature_importance
                else: # Fallback to generic names for current model's importances
                    current_feature_importance = {f"model_feat_{i}": imp for i, imp in enumerate(self.classifier_model.feature_importances_)}
            
            result = {
                '完整性类别': predicted_category_str,
                'ai_confidence': confidence,
                'anomaly_score': anomaly_score,
                'is_anomaly': is_anomaly,
                'class_probabilities': class_probabilities_mapped,
                'feature_importance': current_feature_importance, 
                'overall_reasoning': self._generate_reasoning(predicted_category_str, confidence, anomaly_score, class_probabilities_mapped)
            }
            print(f"🎯 Optimized model prediction: {predicted_category_str} with {confidence:.2%} confidence")
            return result

        except Exception as e:
            print(f"❌ Optimized model internal prediction error: {str(e)}")
            traceback.print_exc()
            return None

    def _generate_reasoning(self, prediction, confidence, anomaly_score, class_probabilities):
        # prediction is already a string here (e.g., 'I类桩')
        # class_probabilities keys are also strings here
        reasoning = f"AI分析结果：{prediction}\n\n"
        reasoning += f"置信度分析：\n- 主要预测置信度：{confidence:.2%}\n"
        if confidence > 0.8: reasoning += "- 置信度较高，预测结果可信\n"
        elif confidence > 0.6: reasoning += "- 置信度中等，建议结合传统方法\n"
        else: reasoning += "- 置信度较低，建议以传统方法为准\n"
        reasoning += f"\n异常检测：\n- 异常分数：{anomaly_score:.3f}\n"
        if anomaly_score < -0.1: reasoning += "- 检测到异常模式，需要特别关注\n" # Example threshold
        else: reasoning += "- 数据模式正常\n"
        reasoning += f"\n各类别概率分布：\n"
        for class_name, prob in sorted(class_probabilities.items(), key=lambda x: x[1], reverse=True):
            reasoning += f"- {class_name}：{prob:.2%}\n"
        return reasoning

    def save_models(self, filepath):
        try:
            model_data = {
                'classifier_model': self.classifier_model,
                'anomaly_detector': self.anomaly_detector,
                'scaler': self.scaler,
                'feature_importance': self.feature_importance, # From training
                'config': self.config,
                # Save components if they exist (for optimized models)
                'feature_extractor': self.feature_extractor if hasattr(self, 'feature_extractor') else None,
                'feature_selector': self.feature_selector if hasattr(self, 'feature_selector') else None,
                'preprocessor': self.preprocessor if hasattr(self, 'preprocessor') else None,
                'is_optimized_model': self.is_optimized_model
            }
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            print(f"✅ Models saved to {filepath}")
            return True
        except Exception as e:
            print(f"❌ Failed to save models: {str(e)}")
            return False

    def load_models(self, filepath):
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            print(f"📦 Loaded model_data type: {type(model_data)}")
            if isinstance(model_data, dict):
                print(f"📦 model_data keys: {list(model_data.keys())}")

            # Reset flags before loading
            self.is_optimized_model = False
            self.feature_extractor = None
            self.feature_selector = None
            self.preprocessor = None
            self.scaler = StandardScaler() # Default scaler
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42) # Default
            self.feature_importance = {}


            if isinstance(model_data, dict):
                # Try to load main classifier model first
                self.classifier_model = model_data.get('classifier_model', model_data.get('model'))

                # Load other components if they exist in the dictionary
                loaded_scaler = model_data.get('scaler')
                if loaded_scaler: self.scaler = loaded_scaler

                loaded_anomaly_detector = model_data.get('anomaly_detector')
                if loaded_anomaly_detector: self.anomaly_detector = loaded_anomaly_detector
                
                self.feature_importance = model_data.get('feature_importance', {})
                self.config = model_data.get('config', self.config) # Keep GUI config or load from model

                # Check for optimized model components
                # These specific structures indicate an optimized/enhanced model
                if model_data.get('is_optimized_model'): # If flag is explicitly saved
                    self.is_optimized_model = True
                    print("🚩 is_optimized_model flag found in model file.")
                elif 'feature_extractor' in model_data or 'preprocessor' in model_data : # Infer if not flagged
                    self.is_optimized_model = True
                    print("🚩 Inferred is_optimized_model based on components.")


                if self.is_optimized_model:
                    self.feature_extractor = model_data.get('feature_extractor')
                    self.feature_selector = model_data.get('feature_selector')
                    self.preprocessor = model_data.get('preprocessor')
                    
                    if self.feature_extractor: print("Loaded model's feature_extractor.")
                    if self.feature_selector: print("Loaded model's feature_selector.")
                    if self.preprocessor: print("Loaded model's preprocessor.")
                    
                    # Ensure scaler is properly loaded for optimized models if not part of preprocessor
                    if self.preprocessor is None and loaded_scaler is None:
                        print("⚠️ Optimized model loaded but no specific scaler or preprocessor found. Ensure features are scaled if model expects it.")
                    elif loaded_scaler:
                        print("Loaded model's scaler.")


                if self.classifier_model is None:
                     print(f"❌ Critical: No classifier model ('classifier_model' or 'model') found in the .pkl dictionary.")
                     return False
                
                print(f"✅ Model package loaded from {filepath}. is_optimized_model: {self.is_optimized_model}")

            elif hasattr(model_data, 'predict'): # Single classifier model object
                self.classifier_model = model_data
                # This is a basic model, not "optimized" in the sense of having bundled components
                self.is_optimized_model = False 
                print(f"✅ Single classifier model loaded from {filepath}. Treating as standard model.")
                # For single classifiers, ensure other components are initialized (they are by default)
                # and potentially (re)train them with synthetic data if they are not fitted.
                self._initialize_missing_components_for_standard_model()
            else:
                print(f"❌ Unknown model format in {filepath}: {type(model_data)}")
                return False
            
            self.loaded_model_path = filepath # Track for V1 logic
            return True

        except Exception as e:
            print(f"❌ Failed to load models from {filepath}: {str(e)}")
            traceback.print_exc()
            return False

    def _initialize_missing_components_for_standard_model(self):
        """Initialize/fit scaler and anomaly detector if a standard model is loaded without them."""
        try:
            # Check if scaler and anomaly_detector are fitted. If not, fit with synthetic data.
            # This is typically for when a raw scikit-learn model is loaded.
            if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None or \
               not hasattr(self.anomaly_detector, 'estimators_features_'): # Check if anomaly detector is fitted
                
                print("🔧 Standard model loaded, components (scaler/anomaly detector) might need fitting. Using synthetic data.")
                features_syn, _ = self.generate_synthetic_training_data(100) # Small synthetic dataset
                if features_syn.size > 0:
                    if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None:
                        self.scaler.fit(features_syn)
                        print("✅ Scaler fitted with synthetic data for standard model.")
                    
                    # Anomaly detector fitting check is harder, but fit if it seems unfitted.
                    # For IsolationForest, 'estimators_features_' is an internal attribute set after fit.
                    if not hasattr(self.anomaly_detector, 'estimators_features_'):
                        scaled_features_syn = self.scaler.transform(features_syn)
                        self.anomaly_detector.fit(scaled_features_syn)
                        print("✅ Anomaly detector fitted with synthetic data for standard model.")
        except Exception as e:
            print(f"⚠️ Error initializing missing components for standard model: {e}")


    def _collect_real_training_data(self): # This method is not directly used by load_models but kept for potential future use
        try:
            # ... (implementation as before, but ensure it returns features compatible with models)
            pass # Placeholder
        except Exception as e:
            print(f"⚠️ Error collecting real training_data: {e}")
            return None, None


# --- GUI Class ---
class PileAnalyzerGZGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Pile Integrity Analyzer (GZ Method) - 桩基完整性分析系统 (GZ方法)")
        self.root.geometry("1600x1000")
        self.root.minsize(1200, 800)
        self.root.configure(bg='#f8f9fa')
        self.root.resizable(True, True)
        self.root.state('normal')
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        self.center_window()
        self.setup_window_controls()
        self.setup_styles()

        self.ai_models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ai_models')
        os.makedirs(self.ai_models_dir, exist_ok=True)
        
        self.ai_analyzer = BuiltInAIAnalyzer() # V1 compatible analyzer
        
        try:
            # Attempt to import and initialize ai_analyzer_v2
            # This part needs the actual ai_analyzer_v2.py file to exist and be importable
            from ai_analyzer_v2 import get_ai_analyzer_v2 
            self.ai_analyzer_v2 = get_ai_analyzer_v2()
            self.use_v2_analyzer = True
            print("✅ AI分析器 V2.0 已启用")
        except ImportError:
            print("⚠️ ai_analyzer_v2.py 未找到或导入失败。AI分析器 V2.0 将不可用。")
            self.ai_analyzer_v2 = None
            self.use_v2_analyzer = False
        except Exception as e:
            print(f"⚠️ AI分析器 V2.0 启用失败，使用V1版本: {e}")
            self.ai_analyzer_v2 = None
            self.use_v2_analyzer = False

        self.current_file = None
        self.analysis_results = {}
        self.progress_queue = queue.Queue()
        self.data_df = None
        self.profiles = []
        self.profile_name_map = {}

        self.config_vars = {
            'sp_ge_100': tk.DoubleVar(value=100.0), 'sp_85_lt_100_min': tk.DoubleVar(value=85.0),
            'sp_85_lt_100_max': tk.DoubleVar(value=100.0), 'sp_75_lt_85_min': tk.DoubleVar(value=75.0),
            'sp_75_lt_85_max': tk.DoubleVar(value=85.0), 'sp_65_lt_75_min': tk.DoubleVar(value=65.0),
            'sp_65_lt_75_max': tk.DoubleVar(value=75.0), 'ad_le_0': tk.DoubleVar(value=0.0),
            'ad_gt_0_le_4_min': tk.DoubleVar(value=0.0), 'ad_gt_0_le_4_max': tk.DoubleVar(value=4.0),
            'ad_gt_4_le_8_min': tk.DoubleVar(value=4.0), 'ad_gt_4_le_8_max': tk.DoubleVar(value=8.0),
            'ad_gt_8_le_12_min': tk.DoubleVar(value=8.0), 'ad_gt_8_le_12_max': tk.DoubleVar(value=12.0),
            'bi_ratio_default': tk.DoubleVar(value=1.0), 'auto_analysis': tk.BooleanVar(value=True),
            'show_details': tk.BooleanVar(value=True), 'ai_model_path': tk.StringVar(value="")
        }
        configure_chinese_fonts()
        self.setup_gui()

    def center_window(self):
        self.root.update_idletasks()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = 1600; window_height = 1000
        if window_width > screen_width: window_width = screen_width - 100
        if window_height > screen_height: window_height = screen_height - 100
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def bind_mousewheel(self, widget):
        def _on_mousewheel(event): widget.yview_scroll(int(-1*(event.delta/120)), "units")
        def _bind_to_mousewheel(event): widget.bind_all("<MouseWheel>", _on_mousewheel)
        def _unbind_from_mousewheel(event): widget.unbind_all("<MouseWheel>")
        widget.bind('<Enter>', _bind_to_mousewheel)
        widget.bind('<Leave>', _unbind_from_mousewheel)

    def setup_window_controls(self):
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.bind('<Escape>', self.exit_fullscreen)
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.is_fullscreen = False

    def toggle_fullscreen(self, event=None):
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)

    def exit_fullscreen(self, event=None):
        self.is_fullscreen = False
        self.root.attributes('-fullscreen', False)

    def on_closing(self):
        if messagebox.askokcancel("Quit", "Do you want to quit the application?"):
            self.root.quit()
            self.root.destroy()

    def setup_styles(self):
        style = ttk.Style(); style.theme_use('clam')
        self.colors = {'primary': '#2c3e50', 'secondary': '#3498db', 'success': '#27ae60', 
                       'warning': '#f39c12', 'danger': '#e74c3c', 'light': '#ecf0f1', 
                       'dark': '#34495e', 'white': '#ffffff', 'accent': '#9b59b6'}
        style.configure('Title.TLabel', font=('Segoe UI', 18, 'bold'), foreground=self.colors['primary'], background='#f8f9fa')
        style.configure('Heading.TLabel', font=('Segoe UI', 12, 'bold'), foreground=self.colors['dark'], background='#f8f9fa')
        style.configure('Modern.TButton', font=('Segoe UI', 10), padding=(12, 8))
        style.configure('Accent.TButton', font=('Segoe UI', 11, 'bold'), padding=(15, 10))
        style.configure('Success.TButton', font=('Segoe UI', 10, 'bold'), padding=(12, 8))
        style.configure('Modern.TFrame', background='#f8f9fa', relief='flat')
        style.configure('Modern.TNotebook', background='#f8f9fa', borderwidth=0)
        style.configure('Modern.TNotebook.Tab', padding=(20, 12), font=('Segoe UI', 10, 'bold'))

    def create_header(self):
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header_frame.pack(fill='x', padx=0, pady=0); header_frame.pack_propagate(False)
        tk.Label(header_frame, text="🔬 Pile Integrity Analyzer (GZ Method)", font=('Segoe UI', 16, 'bold'), fg='white', bg=self.colors['primary']).pack(pady=(15, 2))
        tk.Label(header_frame, text="桩基完整性分析系统 (GZ方法) | Professional Engineering Analysis Solution", font=('Segoe UI', 9), fg=self.colors['light'], bg=self.colors['primary']).pack()

    def setup_gui(self):
        self.create_header()
        self.notebook = ttk.Notebook(self.root, style='Modern.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=15, pady=(5, 10))
        self.setup_data_tab()
        self.setup_analysis_tab()
        self.setup_traditional_results_tab()
        self.setup_ai_results_tab()
        self.setup_comparison_tab()
        self.setup_visualization_tab()
        self.setup_config_tab()
        self.setup_status_bar()
        self.monitor_progress()

    def setup_status_bar(self):
        status_frame = tk.Frame(self.root, bg='#e9ecef', height=30)
        status_frame.pack(side='bottom', fill='x'); status_frame.pack_propagate(False)
        self.main_status_var = tk.StringVar(value="Ready")
        tk.Label(status_frame, textvariable=self.main_status_var, bg='#e9ecef', fg='#495057', font=('Segoe UI', 9), anchor='w').pack(side='left', padx=10, pady=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, mode='determinate', length=200)
        self.progress_bar.pack(side='right', padx=10, pady=5)

    def monitor_progress(self):
        try:
            while True:
                message = self.progress_queue.get_nowait()
                if isinstance(message, dict):
                    if 'status' in message: self.main_status_var.set(message['status'])
                    if 'progress' in message: self.progress_var.set(message['progress'])
                else: self.main_status_var.set(str(message))
        except queue.Empty: pass
        finally: self.root.after(100, self.monitor_progress)

    def setup_data_tab(self):
        data_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(data_frame, text="📁 Data Loading")
        main_container = ttk.Frame(data_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="📁 Data Loading & Preview", style='Title.TLabel').pack(pady=(0, 20))
        file_frame = ttk.LabelFrame(main_container, text="📂 File Selection")
        file_frame.pack(fill='x', pady=(0, 20), padx=10)
        path_frame = ttk.Frame(file_frame); path_frame.pack(fill='x', padx=15, pady=15)
        ttk.Label(path_frame, text="Selected File:", style='Heading.TLabel').pack(anchor='w')
        self.file_path_var = tk.StringVar(value="No file selected")
        file_display = tk.Frame(path_frame, bg='white', relief='solid', bd=1)
        file_display.pack(fill='x', pady=5)
        self.file_label = tk.Label(file_display, textvariable=self.file_path_var, bg='white', fg='#666666', font=('Segoe UI', 9), anchor='w')
        self.file_label.pack(fill='both', expand=True, padx=10, pady=8)
        button_frame = ttk.Frame(file_frame); button_frame.pack(fill='x', padx=15, pady=(0, 15))
        ttk.Button(button_frame, text="📂 Select Data File", style='Accent.TButton', command=self.select_file).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="🔄 Reload File", style='Modern.TButton', command=self.reload_file).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="📊 Quick Analysis", style='Success.TButton', command=self.quick_analysis).pack(side='right')
        preview_frame = ttk.LabelFrame(main_container, text="📊 Data Preview")
        preview_frame.pack(fill='both', expand=True, padx=10)
        self.create_data_preview(preview_frame)

    def create_data_preview(self, parent):
        columns = ('Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3')
        self.data_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=80, anchor='center')
        v_scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient='horizontal', command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        self.data_tree.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        v_scrollbar.pack(side='right', fill='y', pady=15)
        h_scrollbar.pack(side='bottom', fill='x', padx=15)
        self.bind_mousewheel(self.data_tree)
        info_frame = ttk.Frame(parent); info_frame.pack(side='bottom', fill='x', padx=15, pady=(0, 15))
        self.data_info_var = tk.StringVar(value="No data loaded")
        ttk.Label(info_frame, textvariable=self.data_info_var, font=('Segoe UI', 10)).pack(anchor='w')

    def setup_analysis_tab(self):
        analysis_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(analysis_frame, text="🔬 Analysis")
        main_container = ttk.Frame(analysis_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="🔬 Pile Integrity Analysis (GZ Method)", style='Title.TLabel').pack(pady=(0, 20))
        
        gz_params_frame = ttk.LabelFrame(main_container, text="⚙️ GZ Method Parameters")
        gz_params_frame.pack(fill='x', pady=(0, 15), padx=10)
        ratio_frame = ttk.Frame(gz_params_frame); ratio_frame.pack(fill='x', padx=15, pady=15)
        ttk.Label(ratio_frame, text="默认 Bi/BD 比值:", style='Heading.TLabel').pack(side='left')
        ttk.Entry(ratio_frame, textvariable=self.config_vars['bi_ratio_default'], width=10, font=('Segoe UI', 9)).pack(side='left', padx=(10, 0))

        system_selection_frame = ttk.LabelFrame(main_container, text="🚀 AI System Selection")
        system_selection_frame.pack(fill='x', pady=(0, 15), padx=10)
        system_frame = ttk.Frame(system_selection_frame); system_frame.pack(fill='x', padx=15, pady=15)
        self.ai_system_var = tk.StringVar(value="v2" if self.use_v2_analyzer else "v1")
        ttk.Radiobutton(system_frame, text="🚀 AI System V2.0 (推荐) - 支持多模型管理和高精度分析", variable=self.ai_system_var, value="v2", command=self.switch_ai_system).pack(anchor='w', pady=2)
        ttk.Radiobutton(system_frame, text="🔧 AI System V1.0 (兼容) - 传统单模型系统", variable=self.ai_system_var, value="v1", command=self.switch_ai_system).pack(anchor='w', pady=2)

        self.v2_model_frame = ttk.LabelFrame(main_container, text="🎯 AI Model Selection (V2.0)")
        # Packing of v2_model_frame is handled by switch_ai_system
        model_selection_frame = ttk.Frame(self.v2_model_frame); model_selection_frame.pack(fill='x', padx=15, pady=15)
        ttk.Label(model_selection_frame, text="选择AI模型:", style='Heading.TLabel').pack(side='left')
        self.selected_model_var = tk.StringVar()
        self.model_combobox = ttk.Combobox(model_selection_frame, textvariable=self.selected_model_var, state='readonly', width=40)
        self.model_combobox.pack(side='left', padx=(10, 5), fill='x', expand=True)
        self.model_combobox.bind('<<ComboboxSelected>>', self.on_model_selected)
        ttk.Button(model_selection_frame, text="📥 加载模型", style='Accent.TButton', command=self.load_external_model_v2).pack(side='right', padx=(5, 0))
        ttk.Button(model_selection_frame, text="🔄 刷新", style='Modern.TButton', command=self.refresh_model_list).pack(side='right', padx=(5, 0))
        self.model_info_frame = ttk.Frame(self.v2_model_frame); self.model_info_frame.pack(fill='x', padx=15, pady=(0, 15))
        extractor_frame = ttk.Frame(self.v2_model_frame); extractor_frame.pack(fill='x', padx=15, pady=(0, 15))
        ttk.Label(extractor_frame, text="特征提取器:", style='Heading.TLabel').pack(side='left')
        self.selected_extractor_var = tk.StringVar()
        self.extractor_combobox = ttk.Combobox(extractor_frame, textvariable=self.selected_extractor_var, state='readonly', width=30)
        self.extractor_combobox.pack(side='left', padx=(10, 5))
        self.extractor_combobox.bind('<<ComboboxSelected>>', self.on_extractor_selected)

        self.v1_model_frame = ttk.LabelFrame(main_container, text="🤖 AI Model Configuration (V1.0)")
        # Packing of v1_model_frame is handled by switch_ai_system
        model_path_container = ttk.Frame(self.v1_model_frame); model_path_container.pack(fill='x', padx=15, pady=15)
        ttk.Label(model_path_container, text="🤖 AI Model Path:", style='Heading.TLabel').pack(anchor='w', pady=(0, 5))
        model_path_frame = ttk.Frame(model_path_container); model_path_frame.pack(fill='x', pady=5)
        self.model_path_entry = ttk.Entry(model_path_frame, textvariable=self.config_vars['ai_model_path'], width=50, font=('Segoe UI', 9))
        self.model_path_entry.pack(side='left', fill='x', expand=True)
        ttk.Button(model_path_frame, text="📂 Browse", style='Modern.TButton', command=self.select_ai_model_for_analysis).pack(side='right', padx=(5, 0))
        ttk.Button(model_path_frame, text="📥 Load Model", style='Accent.TButton', command=self.load_ai_model_for_analysis).pack(side='right', padx=(5, 0))
        training_frame = ttk.Frame(model_path_container); training_frame.pack(fill='x', pady=(10, 0))
        ttk.Button(training_frame, text="🔧 Train AI Model", style='Modern.TButton', command=self.train_ai_model).pack(side='left', padx=(0, 5))
        ttk.Button(training_frame, text="💾 Save Model", style='Modern.TButton', command=self.save_ai_model).pack(side='left', padx=(0, 5))
        status_container = ttk.Frame(model_path_container); status_container.pack(fill='x', pady=(5, 0))
        ttk.Label(status_container, text="Model Status:", style='Heading.TLabel').pack(side='left')
        self.analysis_model_status_var = tk.StringVar(value="No model loaded")
        ttk.Label(status_container, textvariable=self.analysis_model_status_var, font=('Segoe UI', 9), foreground='gray').pack(side='left', padx=(10, 0))

        control_frame = ttk.LabelFrame(main_container, text="🎮 Analysis Control")
        control_frame.pack(fill='x', pady=(0, 20), padx=10)
        button_frame = ttk.Frame(control_frame); button_frame.pack(fill='x', padx=15, pady=15)
        ttk.Button(button_frame, text="📊 GZ Traditional Analysis", style='Modern.TButton', command=self.run_gz_traditional_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="🤖 AI Analysis", style='Accent.TButton', command=self.run_ai_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="🔄 Run Both", style='Success.TButton', command=self.run_both_analyses).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="💾 Save Results", style='Modern.TButton', command=self.save_results).pack(side='right')
        
        self.switch_ai_system() # Initialize visibility of V1/V2 frames
        if self.use_v2_analyzer:
            self.refresh_model_list()
            self.refresh_extractor_list()
            self.update_model_info_display()


    def setup_traditional_results_tab(self):
        traditional_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(traditional_frame, text="📊 GZ Traditional Analysis")
        main_container = ttk.Frame(traditional_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="📊 GZ Traditional Analysis Results", style='Title.TLabel').pack(pady=(0, 20))
        results_frame = ttk.LabelFrame(main_container, text="📋 Analysis Results")
        results_frame.pack(fill='both', expand=True, padx=10)
        self.traditional_text = tk.Text(results_frame, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        traditional_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.traditional_text.yview)
        self.traditional_text.configure(yscrollcommand=traditional_scroll.set)
        self.traditional_text.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        traditional_scroll.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.traditional_text)
        self.traditional_text.insert(tk.END, "📊 GZ Traditional Analysis Results\n" + "=" * 50 + "\n\nNo analysis results yet.\nPlease load data and run GZ traditional analysis to see results here.\n")

    def setup_ai_results_tab(self):
        ai_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(ai_frame, text="🤖 AI Analysis")
        main_container = ttk.Frame(ai_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="🤖 AI Enhanced Analysis Results", style='Title.TLabel').pack(pady=(0, 20))
        results_frame = ttk.LabelFrame(main_container, text="📋 AI Analysis Results")
        results_frame.pack(fill='both', expand=True, padx=10)
        self.ai_text = tk.Text(results_frame, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        ai_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.ai_text.yview)
        self.ai_text.configure(yscrollcommand=ai_scroll.set)
        self.ai_text.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        ai_scroll.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.ai_text)
        self.ai_text.insert(tk.END, "🤖 AI Enhanced Analysis Results\n" + "=" * 50 + "\n\nNo analysis results yet.\nPlease load data and run AI analysis to see results here.\n")

    def setup_comparison_tab(self):
        comparison_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(comparison_frame, text="⚖️ Comparison")
        main_container = ttk.Frame(comparison_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="⚖️ Comparative Analysis", style='Title.TLabel').pack(pady=(0, 20))
        results_frame = ttk.LabelFrame(main_container, text="📋 Comparison Results")
        results_frame.pack(fill='both', expand=True, padx=10)
        self.comparison_text = tk.Text(results_frame, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        comparison_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.comparison_text.yview)
        self.comparison_text.configure(yscrollcommand=comparison_scroll.set)
        self.comparison_text.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        comparison_scroll.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.comparison_text)
        self.comparison_text.insert(tk.END, "⚖️ Comparative Analysis Results\n" + "=" * 50 + "\n\nNo comparison results yet.\nPlease run both GZ traditional and AI analyses to see comparison here.\n")

    def setup_visualization_tab(self):
        viz_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(viz_frame, text="📈 Visualization")
        main_container = ttk.Frame(viz_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="📈 Data Visualization", style='Title.TLabel').pack(pady=(0, 20))
        control_frame = ttk.LabelFrame(main_container, text="🎮 Visualization Controls")
        control_frame.pack(fill='x', pady=(0, 15), padx=10)
        button_frame = ttk.Frame(control_frame); button_frame.pack(fill='x', padx=15, pady=15)
        ttk.Button(button_frame, text="📊 Plot Data", style='Modern.TButton', command=self.plot_data).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="📈 Plot Analysis Results", style='Accent.TButton', command=self.plot_analysis_results).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="💾 Save Plot", style='Modern.TButton', command=self.save_plot).pack(side='right')
        viz_display_frame = ttk.LabelFrame(main_container, text="📊 Plots")
        viz_display_frame.pack(fill='both', expand=True, padx=10)
        self.fig = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, viz_display_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=15, pady=15)
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        toolbar_frame = ttk.Frame(viz_display_frame); toolbar_frame.pack(fill='x', padx=15, pady=(0, 15))
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame); self.toolbar.update()

    def setup_config_tab(self):
        config_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(config_frame, text="⚙️ Configuration")
        main_container = ttk.Frame(config_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="⚙️ System Configuration", style='Title.TLabel').pack(pady=(0, 20))
        gz_config_frame = ttk.LabelFrame(main_container, text="🔧 GZ Method Thresholds")
        gz_config_frame.pack(fill='x', pady=(0, 15), padx=10)
        self.create_threshold_config(gz_config_frame)
        general_frame = ttk.LabelFrame(main_container, text="🎛️ General Settings")
        general_frame.pack(fill='x', pady=(0, 15), padx=10)
        ttk.Checkbutton(general_frame, text="Auto-run analysis when data is loaded", variable=self.config_vars['auto_analysis']).pack(anchor='w', padx=15, pady=10)
        ttk.Checkbutton(general_frame, text="Show detailed analysis information", variable=self.config_vars['show_details']).pack(anchor='w', padx=15, pady=(0, 10))
        config_button_frame = ttk.Frame(main_container); config_button_frame.pack(fill='x', pady=20, padx=10)
        ttk.Button(config_button_frame, text="💾 Save Configuration", style='Success.TButton', command=self.save_config).pack(side='left', padx=(0, 10))
        ttk.Button(config_button_frame, text="📥 Load Configuration", style='Modern.TButton', command=self.load_config).pack(side='left', padx=(0, 10))
        ttk.Button(config_button_frame, text="🔄 Reset to Defaults", style='Modern.TButton', command=self.reset_config).pack(side='right')

    def create_threshold_config(self, parent):
        canvas = tk.Canvas(parent); scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill="both", expand=True, padx=15, pady=15)
        scrollbar.pack(side="right", fill="y", pady=15)
        grid_frame = ttk.Frame(scrollable_frame); grid_frame.pack(fill='x', padx=10, pady=10)
        row = 0
        ttk.Label(grid_frame, text="Sp (%) 阈值配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(0, 10)); row += 1
        ttk.Label(grid_frame, text="sp ≥ 100%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_ge_100'], width=8).grid(row=row, column=1, padx=5); row += 1
        ttk.Label(grid_frame, text="85% ≤ sp < 100%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_85_lt_100_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_85_lt_100_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="75% ≤ sp < 85%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_75_lt_85_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_75_lt_85_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="65% ≤ sp < 75%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_65_lt_75_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_65_lt_75_max'], width=8).grid(row=row, column=4, padx=2); row += 2
        ttk.Label(grid_frame, text="Ad (dB) 阈值配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10)); row += 1
        ttk.Label(grid_frame, text="ad ≤ 0:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_le_0'], width=8).grid(row=row, column=1, padx=5); row += 1
        ttk.Label(grid_frame, text="0 < ad ≤ 4:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_0_le_4_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_0_le_4_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="4 < ad ≤ 8:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_4_le_8_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_4_le_8_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="8 < ad ≤ 12:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_8_le_12_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_8_le_12_max'], width=8).grid(row=row, column=4, padx=2); row += 2
        ttk.Label(grid_frame, text="Bi比值配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10)); row += 1
        ttk.Label(grid_frame, text="默认 Bi/BD 比值:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Entry(grid_frame, textvariable=self.config_vars['bi_ratio_default'], width=8).grid(row=row, column=1, padx=5)
        def _on_mousewheel(event): canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        def _bind_to_mousewheel(event): canvas.bind_all("<MouseWheel>", _on_mousewheel)
        def _unbind_from_mousewheel(event): canvas.unbind_all("<MouseWheel>")
        canvas.bind('<Enter>', _bind_to_mousewheel); canvas.bind('<Leave>', _unbind_from_mousewheel)

    def select_file(self):
        file_path = filedialog.askopenfilename(title="Select Pile Data File", filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")])
        if file_path:
            self.current_file = file_path
            self.file_path_var.set(file_path)
            self.load_data_file(file_path)

    def reload_file(self):
        if self.current_file: self.load_data_file(self.current_file)
        else: messagebox.showwarning("Warning", "No file selected to reload")

    def load_data_file(self, file_path):
        print(f"📁 Loading data file: {file_path}")
        try:
            self.data_df = self.parse_data_file(file_path)
            if self.data_df is None or self.data_df.empty:
                print("❌ Data loading failed or file is empty")
                messagebox.showerror("Error", "Failed to load data or file is empty"); return
            print(f"✅ Data loaded successfully: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            self.update_data_preview()
            if self.config_vars['auto_analysis'].get():
                print("🚀 Auto-running analysis..."); self.quick_analysis()
            print("🎉 Data loading completed successfully")
        except Exception as e:
            print(f"❌ Data loading error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"Failed to load file: {str(e)}")

    def parse_data_file(self, file_path):
        try:
            df = pd.read_csv(file_path, sep='\t', header=0)
            print(f"原始数据列名: {list(df.columns)}, 数据形状: {df.shape}")
            column_mapping = {}
            # More robust column name detection
            for col_name in df.columns:
                col_lower = col_name.lower().replace('_', '').replace(' ', '')
                if 'depth' in col_lower: column_mapping[col_name] = 'Depth'
                elif '1-2speed' in col_lower or '12speed' in col_lower : column_mapping[col_name] = 'S1'
                elif '1-2amp' in col_lower or '12amp' in col_lower: column_mapping[col_name] = 'A1'
                elif '1-3speed' in col_lower or '13speed' in col_lower: column_mapping[col_name] = 'S2'
                elif '1-3amp' in col_lower or '13amp' in col_lower: column_mapping[col_name] = 'A2'
                elif '2-3speed' in col_lower or '23speed' in col_lower: column_mapping[col_name] = 'S3'
                elif '2-3amp' in col_lower or '23amp' in col_lower: column_mapping[col_name] = 'A3'
            
            df.rename(columns=column_mapping, inplace=True)
            print(f"重命名后列名: {list(df.columns)}")
            required_columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告：缺少列 {missing_columns}")
                if len(missing_columns) > 3: return None # Too many missing, likely wrong format
            for col in required_columns:
                if col in df.columns: df[col] = pd.to_numeric(df[col], errors='coerce')
            original_len = len(df); df.dropna(inplace=True)
            print(f"删除缺失值后: {len(df)} 行 (原始: {original_len} 行)")
            return df
        except Exception as e:
            print(f"数据解析错误: {str(e)}"); traceback.print_exc(); return None

    def update_data_preview(self):
        if self.data_df is None or self.data_df.empty:
            self.data_info_var.set("No data loaded"); return
        for item in self.data_tree.get_children(): self.data_tree.delete(item)
        display_df = self.data_df.head(100)
        for index, row in display_df.iterrows():
            values = [f"{row[col]:.2f}" if pd.notnull(row[col]) else "N/A" for col in ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3'] if col in row.index]
            self.data_tree.insert('', 'end', values=values)
        info_text = f"Loaded {len(self.data_df)} rows, {len(self.data_df.columns)} columns"
        if len(self.data_df) > 100: info_text += " (showing first 100 rows)"
        self.data_info_var.set(info_text)

    def quick_analysis(self):
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return
        self.run_gz_traditional_analysis()
        # Check which AI system is active for quick analysis
        ai_system_version = self.ai_system_var.get()
        if ai_system_version == "v2" and self.use_v2_analyzer and self.ai_analyzer_v2 and self.ai_analyzer_v2.model_manager.current_model_key:
            self.run_ai_analysis()
        elif ai_system_version == "v1": # V1 logic
             model_path = self.config_vars['ai_model_path'].get()
             if model_path and os.path.exists(model_path) or hasattr(self.ai_analyzer.classifier_model, 'classes_'): # Model loaded or built-in trained
                self.run_ai_analysis()
        if 'gz_traditional' in self.analysis_results and 'ai' in self.analysis_results:
            self.generate_comparison()

    def run_gz_traditional_analysis(self):
        print("🔬 Starting GZ traditional analysis...")
        if self.data_df is None or self.data_df.empty:
            print("❌ No data loaded"); messagebox.showwarning("Warning", "Please load data first"); return
        try:
            print(f"📊 Data shape: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            self.main_status_var.set("Running GZ traditional analysis..."); self.root.update()
            result = self.perform_gz_analysis()
            print(f"✅ GZ traditional analysis result: {result.get('final_category', 'N/A')}")
            self.analysis_results['gz_traditional'] = result
            self.display_gz_traditional_result(result)
            self.main_status_var.set("GZ traditional analysis completed")
            print("🎉 GZ traditional analysis completed successfully")
        except Exception as e:
            print(f"❌ GZ traditional analysis error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"GZ traditional analysis failed: {str(e)}")
            self.main_status_var.set("GZ traditional analysis failed")

    def perform_gz_analysis(self):
        if self.data_df is None or self.data_df.empty: return None
        default_bi_ratio = self.config_vars['bi_ratio_default'].get()
        gz_config = self.create_dynamic_gz_config()
        results = {'I_ji_values': {}, 'K_values': {}, 'final_category': None, 'report_details': [], 'analysis_summary': "", 'detailed_analysis': {}, 'config_used': gz_config}
        for index, row in self.data_df.iterrows():
            depth = row['Depth']
            profiles_data = {'1-2': {'speed': row['S1'], 'amplitude': row['A1']}, '1-3': {'speed': row['S2'], 'amplitude': row['A2']}, '2-3': {'speed': row['S3'], 'amplitude': row['A3']}}
            I_ji_at_depth = {}
            for profile, data in profiles_data.items():
                if pd.notnull(data['speed']) and pd.notnull(data['amplitude']):
                    I_ji = calculate_I_ji(data['speed'], data['amplitude'], default_bi_ratio, gz_config)
                    I_ji_at_depth[profile] = I_ji
            if I_ji_at_depth:
                K_i = calculate_K_i(list(I_ji_at_depth.values()))
                results['K_values'][depth] = K_i
                results['I_ji_values'][depth] = I_ji_at_depth
        final_category, report_details = determine_final_category(results['K_values'])
        results['final_category'] = final_category; results['report_details'] = report_details
        results['analysis_summary'] = self.generate_gz_analysis_summary(results)
        return results

    def create_dynamic_gz_config(self):
        sp_ge_100 = self.config_vars['sp_ge_100'].get(); sp_85_min = self.config_vars['sp_85_lt_100_min'].get(); sp_85_max = self.config_vars['sp_85_lt_100_max'].get(); sp_75_min = self.config_vars['sp_75_lt_85_min'].get(); sp_75_max = self.config_vars['sp_75_lt_85_max'].get(); sp_65_min = self.config_vars['sp_65_lt_75_min'].get(); sp_65_max = self.config_vars['sp_65_lt_75_max'].get()
        ad_le_0 = self.config_vars['ad_le_0'].get(); ad_0_min = self.config_vars['ad_gt_0_le_4_min'].get(); ad_0_max = self.config_vars['ad_gt_0_le_4_max'].get(); ad_4_min = self.config_vars['ad_gt_4_le_8_min'].get(); ad_4_max = self.config_vars['ad_gt_4_le_8_max'].get(); ad_8_min = self.config_vars['ad_gt_8_le_12_min'].get(); ad_8_max = self.config_vars['ad_gt_8_le_12_max'].get()
        return {
            'Sp_conditions': {'ge_100': lambda sp: sp >= sp_ge_100, '85_lt_100': lambda sp: sp_85_min <= sp < sp_85_max, '75_lt_85': lambda sp: sp_75_min <= sp < sp_75_max, '65_lt_75': lambda sp: sp_65_min <= sp < sp_65_max, 'lt_65': lambda sp: sp < sp_65_min, 'ge_85': lambda sp: sp >= sp_85_min, 'ge_75': lambda sp: sp >= sp_75_min, 'ge_65': lambda sp: sp >= sp_65_min},
            'Ad_conditions': {'le_0': lambda ad: ad <= ad_le_0, 'gt_0_le_4': lambda ad: ad_0_min < ad <= ad_0_max, 'gt_4_le_8': lambda ad: ad_4_min < ad <= ad_4_max, 'gt_8_le_12': lambda ad: ad_8_min < ad <= ad_8_max, 'gt_12': lambda ad: ad > ad_8_max, 'le_4': lambda ad: ad <= ad_0_max, 'le_8': lambda ad: ad <= ad_4_max, 'le_12': lambda ad: ad <= ad_8_max},
            'Bi_ratio_conditions': {'gt_08': lambda br: br > 0.8, 'gt_05_le_08': lambda br: 0.5 < br <= 0.8, 'gt_05': lambda br: br > 0.5, 'gt_025_le_05': lambda br: 0.25 < br <= 0.5, 'gt_025': lambda br: br > 0.25, 'le_025': lambda br: br <= 0.25}
        }

    def generate_gz_analysis_summary(self, results):
        summary = f"GZ方法桩基完整性分析结果\n" + "=" * 40 + "\n\n" + f"最终判定: {results['final_category']}\n\n判定依据:\n"
        for detail in results['report_details']: summary += f"- {detail}\n"
        summary += f"\nK值分布统计:\n"
        if results['K_values']:
            k_counts = {}
            for k_val in results['K_values'].values(): k_counts[k_val] = k_counts.get(k_val, 0) + 1
            for k_val in sorted(k_counts.keys()):
                count = k_counts[k_val]; percentage = (count / len(results['K_values'])) * 100
                summary += f"K={k_val}: {count}个截面 ({percentage:.1f}%)\n"
        summary += f"\n总计分析截面: {len(results['K_values'])}个\n"
        return summary

    def display_gz_traditional_result(self, result):
        print(f"📝 Displaying GZ traditional analysis result. Result keys: {list(result.keys()) if result else 'None'}")
        if result is None: self.traditional_text.delete(1.0, tk.END); self.traditional_text.insert(tk.END, "没有GZ传统分析结果可显示。\n"); return
        self.traditional_text.delete(1.0, tk.END)
        self.traditional_text.insert(tk.END, f"桩基完整性类别: {result.get('final_category', 'N/A')}\n\n")
        summary = result.get('analysis_summary', ''); 
        if summary: self.traditional_text.insert(tk.END, summary + "\n")
        self.traditional_text.insert(tk.END, "详细分析结果:\n" + "-" * 50 + "\n")
        K_values = result.get('K_values', {}); I_ji_values = result.get('I_ji_values', {})
        for depth in sorted(K_values.keys()):
            self.traditional_text.insert(tk.END, f"深度 {depth:.2f}m: K(i) = {K_values[depth]}\n")
            if depth in I_ji_values:
                for profile, I_ji in I_ji_values[depth].items(): self.traditional_text.insert(tk.END, f"  剖面{profile}: I(j,i) = {I_ji}\n")
            self.traditional_text.insert(tk.END, "\n")
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i, "text") == "📊 GZ Traditional Analysis": self.notebook.select(i); print("📋 Auto-switched to GZ Traditional Analysis tab"); break
        self.traditional_text.see(1.0); print("✅ GZ traditional result displayed successfully")

    def select_ai_model_for_analysis(self): # For V1 system
        file_path = filedialog.askopenfilename(title="Select AI Model File", filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")], initialdir=self.ai_models_dir)
        if file_path:
            self.config_vars['ai_model_path'].set(file_path)
            self.analysis_model_status_var.set(f"Selected: {os.path.basename(file_path)}")

    def load_ai_model_for_analysis(self): # For V1 system
        model_path = self.config_vars['ai_model_path'].get()
        if not model_path: messagebox.showwarning("Warning", "Please select an AI model file first"); return
        if not os.path.exists(model_path): messagebox.showerror("Error", f"AI model file not found: {model_path}"); return
        try:
            if self.ai_analyzer.load_models(model_path): # BuiltInAIAnalyzer.load_models
                 self.analysis_model_status_var.set(f"✅ Loaded (V1): {os.path.basename(model_path)}")
                 messagebox.showinfo("Success", "AI model (V1) loaded successfully!")
            else:
                 self.analysis_model_status_var.set("❌ Failed to load model (V1)")
                 messagebox.showerror("Error", "Failed to load AI model (V1). Check console for details.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load AI model (V1): {str(e)}"); traceback.print_exc()
            self.analysis_model_status_var.set("❌ Failed to load model (V1)")

    def train_ai_model(self): # For V1 system
        try:
            print("🔧 Starting AI model training (V1)...")
            response = messagebox.askyesnocancel("AI Model Training (V1)", "Do you want to train with current data?\n\nYes: Use current loaded data\nNo: Use synthetic training data\nCancel: Cancel training")
            if response is None: return
            self.main_status_var.set("Training AI model (V1)..."); self.root.update()
            features_for_training, labels_for_training = None, None
            feature_names_for_training = None

            if response: # Yes - use current data
                if self.data_df is None or self.data_df.empty:
                    messagebox.showwarning("Warning", "No data loaded for V1 training. Using synthetic data instead.")
                else:
                    # For V1, extract_features is part of the analyzer, not loaded with model
                    features_for_training, feature_names_for_training = self.ai_analyzer.extract_features(self.data_df)
                    if features_for_training.size == 0:
                        messagebox.showwarning("Warning", "Failed to extract features for V1 training. Using synthetic data instead.")
                        features_for_training = None # Fallback to synthetic
                    elif 'gz_traditional' in self.analysis_results:
                        label = self.analysis_results['gz_traditional'].get('final_category', 'I类桩')
                        labels_for_training = np.array([label] * len(features_for_training)) # Label all data points if using whole df
                    else:
                        messagebox.showinfo("Info", "No GZ analysis for labels. V1 training will use synthetic data or unsupervised aspects if applicable.")
                        features_for_training = None # Fallback
            
            # train_models will use synthetic if features_for_training is None
            success = self.ai_analyzer.train_models(features_for_training, labels_for_training)
            
            # Update feature importance if names were available from real data extraction
            if success and feature_names_for_training and hasattr(self.ai_analyzer.classifier_model, 'feature_importances_'):
                 if len(feature_names_for_training) == len(self.ai_analyzer.classifier_model.feature_importances_):
                      self.ai_analyzer.feature_importance = dict(zip(feature_names_for_training, self.ai_analyzer.classifier_model.feature_importances_))

            if success:
                self.main_status_var.set("AI model training (V1) completed")
                messagebox.showinfo("Success", "AI model (V1) trained successfully!")
                print("✅ AI model training (V1) completed")
            else:
                self.main_status_var.set("AI model training (V1) failed")
                messagebox.showerror("Error", "AI model training (V1) failed")
                print("❌ AI model training (V1) failed")
        except Exception as e:
            print(f"❌ AI model training (V1) error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"AI model training (V1) failed: {str(e)}")
            self.main_status_var.set("AI model training (V1) failed")

    def save_ai_model(self): # For V1 system
        try:
            if self.ai_analyzer.classifier_model is None:
                messagebox.showwarning("Warning", "No trained V1 model to save. Please train a model first."); return
            file_path = filedialog.asksaveasfilename(title="Save AI Model (V1)", defaultextension=".pkl", filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")], initialdir=self.ai_models_dir)
            if file_path:
                if self.ai_analyzer.save_models(file_path):
                    messagebox.showinfo("Success", f"AI model (V1) saved to {file_path}")
                    print(f"✅ AI model (V1) saved to: {file_path}")
                else: messagebox.showerror("Error", "Failed to save AI model (V1)")
        except Exception as e:
            print(f"❌ Save AI model (V1) error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"Failed to save AI model (V1): {str(e)}")

    def run_ai_analysis(self):
        print("🤖 Starting AI analysis...")
        if self.data_df is None or self.data_df.empty:
            print("❌ No data loaded"); messagebox.showwarning("Warning", "Please load data first"); return
        print(f"📊 Data shape for AI: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
        try:
            self.main_status_var.set("Running AI analysis..."); self.root.update()
            ai_system_version = self.ai_system_var.get()
            result = None
            current_analyzer_name = "Unknown"

            if ai_system_version == "v2" and self.use_v2_analyzer and self.ai_analyzer_v2:
                print("🚀 Using AI System V2.0 for analysis.")
                current_analyzer_name = "AI System V2.0"
                if not self.ai_analyzer_v2.model_manager.current_model_key:
                     messagebox.showwarning("AI V2 Warning", "No AI V2 model selected/loaded. Please select or load a model.")
                     self.main_status_var.set("AI V2: No model selected"); return
                result = self.ai_analyzer_v2.predict(self.data_df.copy()) # Pass copy to avoid modification issues
            else: # Fallback or explicit V1 selection
                print("🔧 Using AI System V1.0 (BuiltInAIAnalyzer) for analysis.")
                current_analyzer_name = "AI System V1.0"
                current_analyzer = self.ai_analyzer
                model_path_v1 = self.config_vars['ai_model_path'].get()
                # Ensure V1 model is loaded or a default is ready
                if model_path_v1 and os.path.exists(model_path_v1):
                    if current_analyzer.loaded_model_path != model_path_v1 : # Load if different or not loaded
                        print(f"📥 V1: Loading external model: {model_path_v1}")
                        if not current_analyzer.load_models(model_path_v1):
                             messagebox.showerror("V1 Error", f"Failed to load V1 model from {model_path_v1}. Check console.")
                             self.main_status_var.set("AI V1: Model load failed"); return
                elif not hasattr(current_analyzer.classifier_model, 'classes_'): # No external and no built-in trained
                    print("⚠️ V1: No external model, built-in not trained. Training default.")
                    current_analyzer._initial_training() # Ensure a model is ready
                
                result = current_analyzer.predict(self.data_df.copy()) # Pass copy

            if result is None:
                print(f"❌ {current_analyzer_name} prediction failed or returned None.")
                messagebox.showerror("Error", f"{current_analyzer_name} prediction failed. Check console for details.")
                self.main_status_var.set(f"{current_analyzer_name} prediction failed"); return

            print(f"✅ {current_analyzer_name} analysis result: {result.get('完整性类别', 'N/A')}, Confidence: {result.get('ai_confidence', 0.0):.2%}")
            self.analysis_results['ai'] = result
            self.display_ai_result(result) # This will also switch tab
            self.main_status_var.set(f"{current_analyzer_name} analysis completed")
            print(f"🎉 {current_analyzer_name} analysis completed successfully")

        except Exception as e:
            print(f"❌ AI analysis error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"AI analysis failed: {str(e)}")
            self.main_status_var.set("AI analysis failed")


    def display_ai_result(self, result):
        print(f"📝 Displaying AI analysis result. Result keys: {list(result.keys()) if result else 'None'}")
        if result is None: self.ai_text.delete(1.0, tk.END); self.ai_text.insert(tk.END, "没有AI分析结果可显示。\n"); return
        
        self.ai_text.delete(1.0, tk.END)
        raw_category = result.get('完整性类别', 'N/A')
        category_display = str(raw_category) # Ensure it's a string
        
        # Mapping for numeric or simple Roman numeral strings to full text
        category_map = {
            "0": "I类桩", "1": "II类桩", "2": "III类桩", "3": "IV类桩", # Numeric input
            "I": "I类桩", "II": "II类桩", "III": "III类桩", "IV": "IV类桩"  # Simple Roman numeral input
        }
        
        if category_display in category_map:
            category_display = category_map[category_display]
        # If category_display is already "I类桩", "N/A", etc., it remains unchanged.
        
        self.ai_text.insert(tk.END, f"桩基完整性类别: {category_display}\n")
        self.ai_text.insert(tk.END, f"AI置信度: {result.get('ai_confidence', 0.0):.2%}\n")
        self.ai_text.insert(tk.END, f"异常分数: {result.get('anomaly_score', 0.0):.2f}\n\n")
        self.ai_text.insert(tk.END, f"AI分析结论: {result.get('overall_reasoning', '无分析结论')}\n\n")
        self.ai_text.insert(tk.END, "各类别概率:\n")
        class_probabilities = result.get('class_probabilities', {}) # Keys could be "0", "1", etc.
        # category_map is defined above in this method and handles "0"->"I类桩", etc.
        for class_key, prob in sorted(class_probabilities.items()): # Sort for consistent order
            display_class_name = category_map.get(str(class_key), str(class_key)) # Use map, fallback to original key
            self.ai_text.insert(tk.END, f"  {display_class_name}: {prob:.2%}\n")
        self.ai_text.insert(tk.END, "\n特征重要性排名 (部分):\n") # Clarify "部分"
        feature_importance = result.get('feature_importance', {})
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        for i, (feature, importance) in enumerate(sorted_features[:10]):
            self.ai_text.insert(tk.END, f"  {i+1}. {feature}: {importance:.4f}\n")
        
        for i_tab in range(self.notebook.index("end")): # Corrected variable name
            if self.notebook.tab(i_tab, "text") == "🤖 AI Analysis": self.notebook.select(i_tab); print("📋 Auto-switched to AI Analysis tab"); break
        self.ai_text.see(1.0); print("✅ AI result displayed successfully")

    def run_both_analyses(self):
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return
        self.run_gz_traditional_analysis()
        self.run_ai_analysis() # This will use the currently selected AI system
        if 'gz_traditional' in self.analysis_results and 'ai' in self.analysis_results:
            self.generate_comparison()

    def generate_comparison(self):
        print("⚖️ Generating comparison analysis...")
        if 'gz_traditional' not in self.analysis_results or 'ai' not in self.analysis_results:
            print("❌ Both analyses must be completed for comparison"); return
        try:
            gz_result = self.analysis_results['gz_traditional']
            ai_result = self.analysis_results['ai']
            comparison_text = self.create_comparison_text(gz_result, ai_result)
            self.display_comparison_result(comparison_text)
            print("✅ Comparison analysis completed successfully")
        except Exception as e:
            print(f"❌ Comparison analysis error: {str(e)}"); traceback.print_exc()

    def create_comparison_text(self, gz_result, ai_result):
        comparison = "⚖️ GZ传统方法 vs AI分析 对比结果\n" + "=" * 60 + "\n\n"
        gz_category = gz_result.get('final_category', 'N/A')
        ai_category = ai_result.get('完整性类别', 'N/A') # Already string
        comparison += f"GZ传统方法判定: {gz_category}\n"
        comparison += f"AI分析判定: {ai_category}\n"
        comparison += f"判定一致性: {'✅ 一致' if gz_category == ai_category else '❌ 不一致'}\n\n"
        ai_confidence = ai_result.get('ai_confidence', 0.0)
        comparison += f"AI置信度: {ai_confidence:.2%}\n\n详细对比分析:\n" + "-" * 40 + "\n"
        if gz_category == ai_category:
            comparison += f"✅ 两种方法均判定为 {gz_category}，结果一致。\n"
            if ai_confidence > 0.8: comparison += "✅ AI分析置信度较高，结果可信度强。\n"
            elif ai_confidence > 0.6: comparison += "⚠️ AI分析置信度中等，建议结合传统方法综合判断。\n"
            else: comparison += "⚠️ AI分析置信度较低，建议以传统方法为准。\n"
        else:
            comparison += f"❌ 判定结果不一致：GZ方法为{gz_category}，AI方法为{ai_category}。\n建议进一步分析原因：\n1. 检查数据质量和完整性\n2. 验证GZ方法参数设置\n3. 考虑AI模型的适用性\n4. 结合工程经验进行综合判断\n"
        comparison += f"\nGZ传统方法详情:\n"
        k_values = gz_result.get('K_values', {})
        if k_values:
            k_counts = {}; 
            for k_val in k_values.values(): k_counts[k_val] = k_counts.get(k_val, 0) + 1
            for k_val in sorted(k_counts.keys()):
                count = k_counts[k_val]; percentage = (count / len(k_values)) * 100
                comparison += f"  K={k_val}: {count}个截面 ({percentage:.1f}%)\n"
        comparison += f"\nAI分析详情:\n"
        category_map_comparison = {
            "0": "I类桩", "1": "II类桩", "2": "III类桩", "3": "IV类桩",
            "I": "I类桩", "II": "II类桩", "III": "III类桩", "IV": "IV类桩"
        }
        ai_category_display = str(ai_category) # ai_category is the raw value from ai_result
        if ai_category_display in category_map_comparison:
            ai_category_display = category_map_comparison[ai_category_display]
        elif not ai_category_display.endswith('类桩'): # If not in map and not already formatted
            ai_category_display = f"{ai_category_display} (原始值)"

        comparison += f"  AI判定类别: {ai_category_display}\n"
        comparison += f"  AI置信度: {ai_confidence:.2%}\n"

        class_probs = ai_result.get('class_probabilities', {}) 
        if class_probs:
            comparison += f"  各类别概率分布:\n"
            # Sort probabilities by probability value in descending order for display
            sorted_probs = sorted(class_probs.items(), key=lambda item: item[1], reverse=True)
            for class_key, prob in sorted_probs:
                display_class_name = category_map_comparison.get(str(class_key), str(class_key))
                comparison += f"    {display_class_name}: {prob:.2%}\n"
        else:
            comparison += "  各类别概率分布: 无数据\n"
        comparison += f"\n分析建议:\n"
        # Normalize GZ category for comparison (e.g., "I类桩" -> "I")
        gz_simple_category = gz_category.replace('类桩', '') if isinstance(gz_category, str) else 'N/A'
        
        # Normalize AI category for comparison (e.g., "I类桩" -> "I", "0" -> "I")
        ai_simple_category_for_comp = 'N/A'
        raw_ai_cat_str = str(ai_category) # Use the raw ai_category from ai_result for comparison logic
        if raw_ai_cat_str == "0" or raw_ai_cat_str == "I" or ai_category_display == "I类桩": ai_simple_category_for_comp = "I"
        elif raw_ai_cat_str == "1" or raw_ai_cat_str == "II" or ai_category_display == "II类桩": ai_simple_category_for_comp = "II"
        elif raw_ai_cat_str == "2" or raw_ai_cat_str == "III" or ai_category_display == "III类桩": ai_simple_category_for_comp = "III"
        elif raw_ai_cat_str == "3" or raw_ai_cat_str == "IV" or ai_category_display == "IV类桩": ai_simple_category_for_comp = "IV"

        if gz_simple_category != 'N/A' and ai_simple_category_for_comp != 'N/A':
            if gz_simple_category == ai_simple_category_for_comp:
                comparison += "\n判定一致性: ✅ 一致\n"
                if ai_confidence > 0.7:
                    comparison += "  建议: 建议采用一致的判定结果。\n"
                else:
                    comparison += "  建议: ⚠️ 虽然结果一致，但AI置信度不高，建议以GZ传统方法为主。\n"
            else:
                comparison += "\n判定一致性: ❌ 不一致\n"
                comparison += "  建议: \n   1. 优先考虑GZ传统方法结果\n   2. 分析数据质量和模型适用性\n   3. 结合现场实际情况综合判断\n"
        else:
            comparison += "\n判定一致性: ⚠️ 无法比较 (数据不足)\n"
            comparison += "  建议: 请确保GZ和AI分析均已执行并有有效结果。\n"
        return comparison

    def display_comparison_result(self, comparison_text):
        print("📝 Displaying comparison result...")
        self.comparison_text.delete(1.0, tk.END); self.comparison_text.insert(tk.END, comparison_text)
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i, "text") == "⚖️ Comparison": self.notebook.select(i); print("📋 Auto-switched to Comparison tab"); break
        self.comparison_text.see(1.0); print("✅ Comparison result displayed successfully")

    def plot_data(self):
        if self.data_df is None or self.data_df.empty: messagebox.showwarning("Warning", "Please load data first"); return
        try:
            self.fig.clear(); ax1 = self.fig.add_subplot(2, 1, 1); ax2 = self.fig.add_subplot(2, 1, 2)
            ax1.plot(self.data_df['Depth'], self.data_df['S1'], 'b-', label='Profile 1-2', linewidth=1); ax1.plot(self.data_df['Depth'], self.data_df['S2'], 'r-', label='Profile 1-3', linewidth=1); ax1.plot(self.data_df['Depth'], self.data_df['S3'], 'g-', label='Profile 2-3', linewidth=1)
            ax1.set_xlabel('深度 (m)'); ax1.set_ylabel('声速 (%)'); ax1.set_title('声速随深度变化'); ax1.legend(); ax1.grid(True, alpha=0.3); ax1.invert_yaxis()
            ax2.plot(self.data_df['Depth'], self.data_df['A1'], 'b-', label='Profile 1-2', linewidth=1); ax2.plot(self.data_df['Depth'], self.data_df['A2'], 'r-', label='Profile 1-3', linewidth=1); ax2.plot(self.data_df['Depth'], self.data_df['A3'], 'g-', label='Profile 2-3', linewidth=1)
            ax2.set_xlabel('深度 (m)'); ax2.set_ylabel('波幅 (dB)'); ax2.set_title('波幅随深度变化'); ax2.legend(); ax2.grid(True, alpha=0.3); ax2.invert_yaxis()
            self.fig.tight_layout(); self.canvas.draw(); print("✅ Data plot generated successfully")
        except Exception as e: print(f"❌ Plot data error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot data: {str(e)}")

    def plot_analysis_results(self):
        if 'gz_traditional' not in self.analysis_results: messagebox.showwarning("Warning", "Please run GZ traditional analysis first"); return
        try:
            self.fig.clear(); gz_result = self.analysis_results['gz_traditional']; K_values = gz_result.get('K_values', {})
            if not K_values: messagebox.showwarning("Warning", "No K values to plot"); return
            ax = self.fig.add_subplot(1, 1, 1); depths = sorted(K_values.keys()); k_vals = [K_values[d] for d in depths]
            colors = {1: 'green', 2: 'yellow', 3: 'orange', 4: 'red'}; point_colors = [colors.get(k, 'gray') for k in k_vals]
            ax.scatter(k_vals, depths, c=point_colors, s=50, alpha=0.7)
            for k_cat in [1, 2, 3, 4]: # Renamed k to k_cat
                k_depths = [d for d, k_val in K_values.items() if k_val == k_cat]
                if k_depths:
                    for depth_val in k_depths: # Renamed depth to depth_val
                        ax.axhline(y=depth_val, color=colors[k_cat], alpha=0.3, linewidth=1)
            ax.set_xlabel('K值'); ax.set_ylabel('深度 (m)'); ax.set_title(f'K值分布图 - 最终判定: {gz_result.get("final_category", "N/A")}')
            ax.set_xticks([1, 2, 3, 4]); ax.grid(True, alpha=0.3); ax.invert_yaxis()
            legend_elements = [plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=colors[k_cat_legend], markersize=8, label=f'K={k_cat_legend}') for k_cat_legend in [1,2,3,4]] # Renamed k to k_cat_legend
            ax.legend(handles=legend_elements, loc='upper right')
            self.fig.tight_layout(); self.canvas.draw(); print("✅ Analysis results plot generated successfully")
        except Exception as e: print(f"❌ Plot analysis results error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot analysis results: {str(e)}")

    def save_plot(self):
        try:
            file_path = filedialog.asksaveasfilename(title="Save Plot", defaultextension=".png", filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("SVG files", "*.svg"), ("All files", "*.*")])
            if file_path: self.fig.savefig(file_path, dpi=300, bbox_inches='tight'); messagebox.showinfo("Success", f"Plot saved to {file_path}"); print(f"✅ Plot saved to: {file_path}")
        except Exception as e: print(f"❌ Save plot error: {str(e)}"); messagebox.showerror("Error", f"Failed to save plot: {str(e)}")

    def save_config(self):
        try:
            config_data = {key: var.get() for key, var in self.config_vars.items()}
            file_path = filedialog.asksaveasfilename(title="Save Configuration", defaultextension=".json", filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f: json.dump(config_data, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Success", f"Configuration saved to {file_path}"); print(f"✅ Configuration saved to: {file_path}")
        except Exception as e: print(f"❌ Save config error: {str(e)}"); messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def load_config(self):
        try:
            file_path = filedialog.askopenfilename(title="Load Configuration", filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f: config_data = json.load(f)
                for key, value in config_data.items():
                    if key in self.config_vars: self.config_vars[key].set(value)
                messagebox.showinfo("Success", f"Configuration loaded from {file_path}"); print(f"✅ Configuration loaded from: {file_path}")
        except Exception as e: print(f"❌ Load config error: {str(e)}"); messagebox.showerror("Error", f"Failed to load configuration: {str(e)}")

    def reset_config(self):
        try:
            self.config_vars['sp_ge_100'].set(100.0); self.config_vars['sp_85_lt_100_min'].set(85.0); self.config_vars['sp_85_lt_100_max'].set(100.0); self.config_vars['sp_75_lt_85_min'].set(75.0); self.config_vars['sp_75_lt_85_max'].set(85.0); self.config_vars['sp_65_lt_75_min'].set(65.0); self.config_vars['sp_65_lt_75_max'].set(75.0)
            self.config_vars['ad_le_0'].set(0.0); self.config_vars['ad_gt_0_le_4_min'].set(0.0); self.config_vars['ad_gt_0_le_4_max'].set(4.0); self.config_vars['ad_gt_4_le_8_min'].set(4.0); self.config_vars['ad_gt_4_le_8_max'].set(8.0); self.config_vars['ad_gt_8_le_12_min'].set(8.0); self.config_vars['ad_gt_8_le_12_max'].set(12.0)
            self.config_vars['bi_ratio_default'].set(1.0); self.config_vars['auto_analysis'].set(True); self.config_vars['show_details'].set(True)
            messagebox.showinfo("Success", "Configuration reset to defaults"); print("✅ Configuration reset to defaults")
        except Exception as e: print(f"❌ Reset config error: {str(e)}"); messagebox.showerror("Error", f"Failed to reset configuration: {str(e)}")

    def save_results(self):
        if not self.analysis_results: messagebox.showwarning("Warning", "No analysis results to save"); return
        try:
            file_path = filedialog.asksaveasfilename(title="Save Analysis Results", defaultextension=".txt", filetypes=[("Text files", "*.txt"), ("JSON files", "*.json"), ("All files", "*.*")])
            if file_path:
                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f: json.dump(self.analysis_results, f, indent=2, ensure_ascii=False, default=str)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("桩基完整性分析结果报告\n" + "=" * 50 + "\n\n")
                        if 'gz_traditional' in self.analysis_results: f.write("GZ传统方法分析结果:\n" + "-" * 30 + "\n" + self.analysis_results['gz_traditional'].get('analysis_summary', '') + "\n\n")
                        if 'ai' in self.analysis_results:
                            ai_res = self.analysis_results['ai']
                            raw_category_save = ai_res.get('完整性类别', 'N/A')
                            category_display_save = str(raw_category_save)

                            category_map_save = {
                                "0": "I", "1": "II", "2": "III", "3": "IV",
                                "I": "I", "II": "II", "III": "III", "IV": "IV"
                            }
                            if category_display_save in category_map_save:
                                category_display_save = category_map_save[category_display_save]
                            
                            f.write("AI分析结果:\n" + "-" * 30 + "\n" + f"桩基完整性类别: {category_display_save}类桩\nAI置信度: {ai_res.get('ai_confidence', 0.0):.2%}\nAI分析结论: {ai_res.get('overall_reasoning', '无分析结论')}\n\n")
                messagebox.showinfo("Success", f"Results saved to {file_path}"); print(f"✅ Results saved to: {file_path}")
        except Exception as e: print(f"❌ Save results error: {str(e)}"); messagebox.showerror("Error", f"Failed to save results: {str(e)}")

    def run(self):
        print("🚀 Starting Pile Integrity Analyzer (GZ Method) GUI..."); self.root.mainloop()

    def switch_ai_system(self):
        try:
            system_version = self.ai_system_var.get()
            if system_version == "v2" and self.use_v2_analyzer:
                self.v2_model_frame.pack(fill='x', pady=(0, 15), padx=10)
                self.v1_model_frame.pack_forget(); print("🚀 切换到AI系统 V2.0")
            else:
                self.v1_model_frame.pack(fill='x', pady=(0, 20), padx=10)
                if hasattr(self, 'v2_model_frame'): self.v2_model_frame.pack_forget()
                print("🔧 切换到AI系统 V1.0 (或V2不可用)")
        except Exception as e: print(f"❌ 切换AI系统失败: {e}")

    def refresh_model_list(self): # For V2
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            models = self.ai_analyzer_v2.model_manager.get_available_models()
            model_names = []; self.model_key_mapping = {}
            for key, model_info in models.items():
                display_name = f"{model_info.name} ({model_info.accuracy:.1%})"
                model_names.append(display_name); self.model_key_mapping[display_name] = key
            self.model_combobox['values'] = model_names
            current_model_info = self.ai_analyzer_v2.model_manager.get_current_model_info()
            if current_model_info:
                current_display = f"{current_model_info.name} ({current_model_info.accuracy:.1%})"
                if current_display in model_names: self.selected_model_var.set(current_display)
            print(f"🔄 模型列表已刷新: {len(model_names)} 个模型")
        except Exception as e: print(f"❌ 刷新模型列表失败: {e}")

    def refresh_extractor_list(self): # For V2
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            extractors = self.ai_analyzer_v2.feature_manager.get_available_extractors()
            extractor_names = []; self.extractor_key_mapping = {}
            for key, extractor_info in extractors.items():
                display_name = f"{extractor_info['name']} ({extractor_info['feature_count']}特征)"
                extractor_names.append(display_name); self.extractor_key_mapping[display_name] = key
            self.extractor_combobox['values'] = extractor_names
            current_extractor = self.ai_analyzer_v2.feature_manager.get_current_extractor()
            if current_extractor:
                current_display = f"{current_extractor.name} ({current_extractor.feature_count}特征)"
                if current_display in extractor_names: self.selected_extractor_var.set(current_display)
            print(f"🔄 特征提取器列表已刷新: {len(extractor_names)} 个提取器")
        except Exception as e: print(f"❌ 刷新特征提取器列表失败: {e}")

    def on_model_selected(self, event=None): # For V2
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            selected_display = self.selected_model_var.get()
            if selected_display in self.model_key_mapping:
                model_key = self.model_key_mapping[selected_display]
                if self.ai_analyzer_v2.set_model(model_key):
                    print(f"✅ 模型已选择 (V2): {selected_display}"); self.update_model_info_display()
                else: print(f"❌ 模型选择失败 (V2): {selected_display}")
        except Exception as e: print(f"❌ 模型选择处理失败 (V2): {e}")

    def on_extractor_selected(self, event=None): # For V2
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            selected_display = self.selected_extractor_var.get()
            if selected_display in self.extractor_key_mapping:
                extractor_key = self.extractor_key_mapping[selected_display]
                if self.ai_analyzer_v2.set_feature_extractor(extractor_key):
                    print(f"✅ 特征提取器已选择 (V2): {selected_display}"); self.update_model_info_display()
                else: print(f"❌ 特征提取器选择失败 (V2): {selected_display}")
        except Exception as e: print(f"❌ 特征提取器选择处理失败 (V2): {e}")

    def update_model_info_display(self): # For V2
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            for widget in self.model_info_frame.winfo_children(): widget.destroy()
            current_model_info = self.ai_analyzer_v2.model_manager.get_current_model_info()
            current_extractor = self.ai_analyzer_v2.feature_manager.get_current_extractor()
            if current_model_info and current_extractor:
                info_text = f"📊 当前配置:\n  • 模型: {current_model_info.name}\n  • 准确率: {current_model_info.accuracy:.1%}\n  • 特征提取器: {current_extractor.name}\n  • 特征数量: {current_extractor.feature_count}\n  • 模型类型: {current_model_info.model_type}\n  • 文件大小: {current_model_info.file_size / 1024 / 1024:.1f} MB"
                ttk.Label(self.model_info_frame, text=info_text, font=('Consolas', 9), foreground='darkgreen').pack(anchor='w')
                if current_model_info.feature_count != current_extractor.feature_count:
                    warning_text = f"⚠️ 警告: 模型期望 {current_model_info.feature_count} 个特征，但提取器提供 {current_extractor.feature_count} 个特征"
                    ttk.Label(self.model_info_frame, text=warning_text, font=('Consolas', 9), foreground='red').pack(anchor='w', pady=(5, 0))
                else: ttk.Label(self.model_info_frame, text="✅ 模型与特征提取器兼容", font=('Consolas', 9), foreground='green').pack(anchor='w', pady=(5, 0))
        except Exception as e: print(f"❌ 更新模型信息显示失败 (V2): {e}")

    def load_external_model_v2(self): # For V2
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2):
                messagebox.showwarning("Warning", "请先切换到AI System V2.0 (如果可用)"); return
            default_model_dir = self.ai_models_dir
            file_path = filedialog.askopenfilename(title="选择AI模型文件 (V2)", filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")], initialdir=default_model_dir)
            if not file_path: return
            print(f"📥 用户选择模型文件 (V2): {file_path}")
            self._show_model_loading_dialog(file_path) # Common dialog for V2
        except Exception as e:
            print(f"❌ 加载外部模型失败 (V2): {e}"); traceback.print_exc()
            messagebox.showerror("Error", f"加载外部模型失败 (V2): {str(e)}")

    def _show_model_loading_dialog(self, file_path): # Common for V2 loading
        try:
            dialog = tk.Toplevel(self.root); dialog.title("加载外部模型"); dialog.geometry("650x550"); dialog.resizable(True, True); dialog.transient(self.root); dialog.grab_set()
            dialog.update_idletasks(); x = (dialog.winfo_screenwidth() // 2) - (650 // 2); y = (dialog.winfo_screenheight() // 2) - (550 // 2); dialog.geometry(f"650x550+{x}+{y}")
            main_frame = ttk.Frame(dialog, padding="15"); main_frame.pack(fill='both', expand=True)
            main_frame.grid_rowconfigure(3, weight=1); main_frame.grid_columnconfigure(0, weight=1)
            ttk.Label(main_frame, text="🤖 加载外部AI模型", font=('Segoe UI', 14, 'bold')).grid(row=0, column=0, pady=(0, 15), sticky='w')
            file_frame = ttk.LabelFrame(main_frame, text="📁 文件信息", padding="10"); file_frame.grid(row=1, column=0, sticky='ew', pady=(0, 10))
            ttk.Label(file_frame, text=f"文件路径: {file_path}", wraplength=500).pack(anchor='w')
            ttk.Label(file_frame, text=f"文件大小: {os.path.getsize(file_path) / 1024 / 1024:.1f} MB").pack(anchor='w')
            name_frame = ttk.Frame(main_frame); name_frame.grid(row=2, column=0, sticky='ew', pady=(0, 10))
            ttk.Label(name_frame, text="模型名称:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')
            model_name_var = tk.StringVar(value=f"外部模型 - {os.path.basename(file_path)}")
            ttk.Entry(name_frame, textvariable=model_name_var, width=60).pack(fill='x', pady=(5, 0))
            preview_frame = ttk.LabelFrame(main_frame, text="🔍 模型预览", padding="10"); preview_frame.grid(row=3, column=0, sticky='nsew', pady=(0, 10))
            preview_text = tk.Text(preview_frame, height=8, wrap='word', font=('Consolas', 9))
            preview_scrollbar = ttk.Scrollbar(preview_frame, orient='vertical', command=preview_text.yview); preview_text.configure(yscrollcommand=preview_scrollbar.set)
            preview_text.pack(side='left', fill='both', expand=True); preview_scrollbar.pack(side='right', fill='y')
            self._analyze_and_preview_model(file_path, preview_text) # Common analysis
            ttk.Separator(main_frame, orient='horizontal').grid(row=4, column=0, sticky='ew', pady=(10, 10))
            button_frame = ttk.Frame(main_frame); button_frame.grid(row=5, column=0, sticky='ew', pady=(0, 5))
            def load_model_action(): # Renamed from load_model to avoid conflict
                try:
                    model_name = model_name_var.get().strip()
                    if not model_name: messagebox.showwarning("Warning", "请输入模型名称"); return
                    print(f"🔄 开始加载模型 (V2): {model_name}")
                    if self.ai_analyzer_v2.model_manager.load_external_model(file_path, model_name):
                        print(f"✅ 模型加载成功 (V2)，正在更新界面...")
                        self.refresh_model_list() # V2 specific refresh
                        models = self.ai_analyzer_v2.model_manager.get_available_models()
                        for key, model_info in models.items():
                            if model_info.name == model_name:
                                display_name = f"{model_info.name} ({model_info.accuracy:.1%})"
                                self.selected_model_var.set(display_name); self.on_model_selected(); break
                        messagebox.showinfo("Success", f"模型 '{model_name}' (V2) 加载成功！\n\n已自动选择该模型。")
                        dialog.destroy()
                    else: messagebox.showerror("Error", "模型加载失败 (V2)，请检查文件格式或控制台日志。")
                except Exception as ex: print(f"❌ 模型加载失败 (V2): {ex}"); traceback.print_exc(); messagebox.showerror("Error", f"模型加载失败 (V2): {str(ex)}")
            load_btn = ttk.Button(button_frame, text="✅ 确定加载", style='Accent.TButton', command=load_model_action, width=15); load_btn.pack(side='right', padx=(10, 0))
            ttk.Button(button_frame, text="❌ 取消", style='Modern.TButton', command=dialog.destroy, width=15).pack(side='right')
            ttk.Label(button_frame, text="💡 提示: 点击'确定加载'按钮完成模型加载", font=('Segoe UI', 9), foreground='gray').pack(side='left')
            load_btn.focus_set(); dialog.bind('<Return>', lambda e: load_model_action())
            dialog.update_idletasks()
        except Exception as e: print(f"❌ 显示模型加载对话框失败: {e}"); messagebox.showerror("Error", f"显示模型加载对话框失败: {str(e)}")

    def _analyze_and_preview_model(self, file_path, preview_text): # Common for V2
        try:
            preview_text.delete(1.0, tk.END); preview_text.insert(tk.END, "🔍 正在分析模型文件...\n\n"); preview_text.update()
            with open(file_path, 'rb') as f: model_data = pickle.load(f)
            analysis_text = "📊 模型分析结果:\n" + "=" * 40 + "\n\n"
            if isinstance(model_data, dict):
                if 'model' in model_data and ('preprocessor' in model_data or 'feature_extractor' in model_data and 'scaler' in model_data): # More flexible check for optimized/enhanced
                    analysis_text += "🚀 模型类型: 高级/优化模型 (包含预处理组件)\n"
                    if 'accuracy' in model_data: analysis_text += f"📈 声明准确率: {model_data['accuracy']:.1%}\n" # If accuracy is stored
                    num_feat = model_data.get('feature_count', '未知') # If feature_count is stored
                    analysis_text += f"🔧 特征数量: {num_feat}\n"
                    if 'preprocessor' in model_data: analysis_text += "⚡ 预处理器: 已包含\n"
                    if 'feature_extractor' in model_data: analysis_text += "🔩 特征提取器: 已包含\n"
                    if 'scaler' in model_data: analysis_text += "⚖️ 缩放器: 已包含\n"
                    analysis_text += "🎯 推荐用途: 高精度分析\n\n"
                elif 'classifier_model' in model_data: # Standard V1 like model
                    analysis_text += "🔧 模型类型: 标准完整模型 (V1兼容)\n"
                    analysis_text += "📈 预期准确率: (取决于训练)\n"
                    analysis_text += "🔧 特征数量: (通常54个标准特征)\n"
                    analysis_text += "⚙️ 组件: 分类器, 可能有缩放器/异常检测器\n"
                    analysis_text += "🎯 推荐用途: 标准分析 (V1系统)\n\n"
                else:
                    analysis_text += "❓ 模型类型: 自定义字典格式\n"
                    analysis_text += f"📋 包含组件: {', '.join(model_data.keys())}\n\n"
            elif hasattr(model_data, 'predict'): # Raw sklearn model
                analysis_text += "🔧 模型类型: 单个分类器 (原始Scikit-learn模型)\n"
                analysis_text += "📈 预期准确率: (取决于训练)\n"
                if hasattr(model_data, 'n_features_in_'): analysis_text += f"🔧 输入特征数 (模型期望): {model_data.n_features_in_}\n"
                else: analysis_text += "🔧 输入特征数: 未知 (模型未提供)\n"
                analysis_text += "⚠️ 注意: 可能需要手动配置或加载额外的预处理步骤 (如缩放器)。\n"
                analysis_text += "🎯 推荐用途: 基础分析, 或作为V2系统中的一部分。\n\n"
                if hasattr(model_data, '__class__'): analysis_text += f"🏷️ 模型类: {model_data.__class__.__name__}\n"
            else:
                analysis_text += "❌ 模型类型: 未知格式\n⚠️ 警告: 可能不兼容当前系统\n\n"
            
            analysis_text += "🔍 兼容性提示 (V2系统):\n" + "-" * 20 + "\n"
            analysis_text += "  - V2系统会尝试根据模型结构自动匹配或配置特征提取器。\n"
            analysis_text += "  - 高级模型 (含'feature_extractor'或'preprocessor') 通常能更好地发挥V2系统性能。\n"
            analysis_text += "  - 标准或单个分类器模型也可以加载，但可能需要注意特征兼容性。\n"
            analysis_text += "\n💡 建议: 加载后，检查V2系统中的模型信息和特征提取器配置。"
            preview_text.delete(1.0, tk.END); preview_text.insert(tk.END, analysis_text)
        except Exception as e:
            error_text = f"❌ 模型分析失败: {str(e)}\n\n可能的原因:\n- 文件格式不正确或已损坏\n- 不是有效的pickle模型文件\n\n请选择正确的.pkl模型文件。"
            preview_text.delete(1.0, tk.END); preview_text.insert(tk.END, error_text); traceback.print_exc()

def main():
    try: app = PileAnalyzerGZGUI(); app.run()
    except Exception as e: print(f"❌ Application error: {str(e)}"); traceback.print_exc()

if __name__ == "__main__":
    main()
