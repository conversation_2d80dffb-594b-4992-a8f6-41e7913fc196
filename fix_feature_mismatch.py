#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复特征数量不匹配问题
Fix Feature Count Mismatch Issue
"""

import os
import pickle
import pandas as pd

def analyze_model_feature_requirements():
    """分析模型的特征需求"""
    print("🔍 分析模型的特征需求")
    print("=" * 80)
    
    model_files = [
        "advanced_models/model_r1.pkl",
        "advanced_models/model_q1.pkl", 
        "advanced_models/model_a1.pkl",
        "compatible_ai_model.pkl",
        "optimized_model_95.pkl"
    ]
    
    model_info = {}
    
    for model_file in model_files:
        if os.path.exists(model_file):
            try:
                with open(model_file, 'rb') as f:
                    model_data = pickle.load(f)
                
                print(f"\n📋 模型: {model_file}")
                
                # 检查模型结构
                if isinstance(model_data, dict):
                    classifier = model_data.get('classifier_model') or model_data.get('model')
                    
                    if classifier:
                        # 尝试获取特征数量
                        feature_count = None
                        
                        if hasattr(classifier, 'n_features_in_'):
                            feature_count = classifier.n_features_in_
                        elif hasattr(classifier, 'estimators_'):
                            # 集成模型
                            for estimator in classifier.estimators_:
                                if hasattr(estimator, 'n_features_in_'):
                                    feature_count = estimator.n_features_in_
                                    break
                        
                        print(f"  🎯 分类器类型: {type(classifier).__name__}")
                        print(f"  📊 期望特征数: {feature_count}")
                        
                        # 检查特征选择器
                        if 'feature_selector' in model_data:
                            selector = model_data['feature_selector']
                            if hasattr(selector, 'n_features_in_'):
                                print(f"  🔍 特征选择器输入: {selector.n_features_in_}")
                            if hasattr(selector, 'n_features_'):
                                print(f"  🔍 特征选择器输出: {selector.n_features_}")
                        
                        model_info[model_file] = {
                            'expected_features': feature_count,
                            'classifier_type': type(classifier).__name__,
                            'has_feature_selector': 'feature_selector' in model_data
                        }
                    else:
                        print(f"  ❌ 未找到分类器")
                else:
                    print(f"  ⚠️ 非字典格式模型")
                    
            except Exception as e:
                print(f"  ❌ 加载失败: {e}")
    
    return model_info

def test_feature_extractors():
    """测试特征提取器"""
    print(f"\n🧪 测试特征提取器")
    print("=" * 80)
    
    # 测试数据
    test_file = "training_data/III/1-2.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    df = pd.read_csv(test_file, sep='\t', encoding='utf-8')
    
    # 标准化列名
    column_mapping = {
        'Depth(m)': 'Depth',
        '1-2 Speed%': 'S1', '1-2 Amp%': 'A1',
        '1-3 Speed%': 'S2', '1-3 Amp%': 'A2', 
        '2-3 Speed%': 'S3', '2-3 Amp%': 'A3'
    }
    df = df.rename(columns=column_mapping)
    
    print(f"📊 测试数据形状: {df.shape}")
    print(f"📋 数据列: {list(df.columns)}")
    
    try:
        from feature_manager import get_feature_manager
        
        feature_manager = get_feature_manager()
        extractors = feature_manager.get_available_extractors()
        
        print(f"\n🔧 可用特征提取器: {len(extractors)} 个")
        
        for extractor_name in extractors:
            try:
                feature_manager.set_current_extractor(extractor_name)
                extractor = feature_manager.get_current_extractor()
                
                features, feature_names = extractor.extract_features(df)
                
                print(f"\n📋 {extractor_name}:")
                print(f"  📊 特征数量: {features.shape[1] if features.ndim > 1 else len(features)}")
                print(f"  📝 特征名称数: {len(feature_names)}")
                
            except Exception as e:
                print(f"\n❌ {extractor_name} 失败: {e}")
                
    except Exception as e:
        print(f"❌ 特征管理器测试失败: {e}")

def create_auto_feature_matching():
    """创建自动特征匹配功能"""
    print(f"\n🔧 创建自动特征匹配功能")
    print("=" * 80)
    
    fix_code = '''
def auto_match_features_for_model(model_data, df):
    """自动为模型匹配正确的特征"""
    try:
        # 获取模型期望的特征数量
        classifier = model_data.get('classifier_model') or model_data.get('model')
        
        if not classifier:
            raise ValueError("No classifier found in model")
        
        expected_features = None
        
        # 尝试获取期望特征数
        if hasattr(classifier, 'n_features_in_'):
            expected_features = classifier.n_features_in_
        elif hasattr(classifier, 'estimators_'):
            for estimator in classifier.estimators_:
                if hasattr(estimator, 'n_features_in_'):
                    expected_features = estimator.n_features_in_
                    break
        
        if expected_features is None:
            print("⚠️ 无法确定模型期望的特征数量")
            return None, None
        
        print(f"🎯 模型期望特征数: {expected_features}")
        
        # 根据期望特征数选择合适的特征提取器
        from feature_manager import get_feature_manager
        
        feature_manager = get_feature_manager()
        extractors = feature_manager.get_available_extractors()
        
        best_extractor = None
        best_features = None
        best_feature_names = None
        
        for extractor_name in extractors:
            try:
                feature_manager.set_current_extractor(extractor_name)
                extractor = feature_manager.get_current_extractor()
                
                features, feature_names = extractor.extract_features(df)
                feature_count = features.shape[1] if features.ndim > 1 else len(features)
                
                print(f"🔍 {extractor_name}: {feature_count} 特征")
                
                # 如果特征数量匹配
                if feature_count == expected_features:
                    print(f"✅ 找到匹配的特征提取器: {extractor_name}")
                    best_extractor = extractor_name
                    best_features = features
                    best_feature_names = feature_names
                    break
                    
            except Exception as e:
                print(f"⚠️ {extractor_name} 测试失败: {e}")
        
        # 如果没有完全匹配的，尝试特征选择
        if best_extractor is None and 'feature_selector' in model_data:
            print("🔧 尝试使用特征选择器...")
            
            # 使用高精度特征提取器
            feature_manager.set_current_extractor('advanced')
            extractor = feature_manager.get_current_extractor()
            
            features, feature_names = extractor.extract_features(df)
            
            # 应用特征选择器
            feature_selector = model_data['feature_selector']
            
            if hasattr(feature_selector, 'transform'):
                selected_features = feature_selector.transform(features.reshape(1, -1))
                
                if selected_features.shape[1] == expected_features:
                    print(f"✅ 特征选择后匹配: {selected_features.shape[1]} 特征")
                    best_features = selected_features
                    best_feature_names = [f"selected_feature_{i}" for i in range(selected_features.shape[1])]
                    best_extractor = 'advanced_with_selection'
        
        return best_features, best_feature_names, best_extractor
        
    except Exception as e:
        print(f"❌ 自动特征匹配失败: {e}")
        return None, None, None
'''
    
    with open('auto_feature_matching.py', 'w', encoding='utf-8') as f:
        f.write(fix_code)
    
    print("✅ 自动特征匹配功能已创建: auto_feature_matching.py")

def fix_ai_v2_feature_matching():
    """修复AI V2的特征匹配"""
    print(f"\n🔧 修复AI V2的特征匹配")
    print("=" * 80)
    
    # 读取AI V2文件
    ai_v2_file = "ai_analyzer_v2.py"
    
    if not os.path.exists(ai_v2_file):
        print(f"❌ AI V2文件不存在: {ai_v2_file}")
        return False
    
    # 创建改进的预测方法
    improved_predict = '''
    def predict_with_auto_features(self, df):
        """带自动特征匹配的预测"""
        try:
            print("🔍 开始自动特征匹配预测...")
            
            # 获取当前模型
            current_model = self.model_manager.get_current_model()
            current_model_info = self.model_manager.get_current_model_info()
            
            if current_model is None:
                raise ValueError("No model loaded")
            
            print(f"🎯 使用模型: {current_model_info.name}")
            
            # 自动匹配特征
            features, feature_names, extractor_used = self.auto_match_features_for_model(current_model, df)
            
            if features is None:
                raise ValueError("Failed to match features for model")
            
            print(f"✅ 特征匹配成功: {features.shape[1]} 特征, 使用 {extractor_used}")
            
            # 进行预测
            if current_model_info.model_type == 'optimized':
                result = self._predict_optimized(features, current_model, current_model_info)
            else:
                result = self._predict_standard(features, current_model, current_model_info)
            
            # 添加类别名称
            class_names = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
            result['类别名称'] = class_names[result['完整性类别']]
            
            return result
            
        except Exception as e:
            print(f"❌ 自动特征匹配预测失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def auto_match_features_for_model(self, model_data, df):
        """自动为模型匹配正确的特征"""
        try:
            # 获取模型期望的特征数量
            classifier = model_data.get('classifier_model') or model_data.get('model')
            
            if not classifier:
                raise ValueError("No classifier found in model")
            
            expected_features = None
            
            # 尝试获取期望特征数
            if hasattr(classifier, 'n_features_in_'):
                expected_features = classifier.n_features_in_
            elif hasattr(classifier, 'estimators_'):
                for estimator in classifier.estimators_:
                    if hasattr(estimator, 'n_features_in_'):
                        expected_features = estimator.n_features_in_
                        break
            
            if expected_features is None:
                print("⚠️ 无法确定模型期望的特征数量，尝试使用高精度特征")
                # 默认使用高精度特征提取器
                self.set_feature_extractor('advanced')
                features, feature_names = self.extract_features(df)
                return features, feature_names, 'advanced'
            
            print(f"🎯 模型期望特征数: {expected_features}")
            
            # 根据期望特征数选择合适的特征提取器
            extractors = self.feature_manager.get_available_extractors()
            
            for extractor_name in extractors:
                try:
                    self.set_feature_extractor(extractor_name)
                    features, feature_names = self.extract_features(df)
                    feature_count = features.shape[1] if features.ndim > 1 else len(features)
                    
                    print(f"🔍 {extractor_name}: {feature_count} 特征")
                    
                    # 如果特征数量匹配
                    if feature_count == expected_features:
                        print(f"✅ 找到匹配的特征提取器: {extractor_name}")
                        return features, feature_names, extractor_name
                        
                except Exception as e:
                    print(f"⚠️ {extractor_name} 测试失败: {e}")
            
            # 如果没有完全匹配的，尝试特征选择
            if 'feature_selector' in model_data:
                print("🔧 尝试使用特征选择器...")
                
                # 使用高精度特征提取器
                self.set_feature_extractor('advanced')
                features, feature_names = self.extract_features(df)
                
                # 应用特征选择器
                feature_selector = model_data['feature_selector']
                
                if hasattr(feature_selector, 'transform'):
                    selected_features = feature_selector.transform(features.reshape(1, -1))
                    
                    if selected_features.shape[1] == expected_features:
                        print(f"✅ 特征选择后匹配: {selected_features.shape[1]} 特征")
                        return selected_features, [f"selected_feature_{i}" for i in range(selected_features.shape[1])], 'advanced_with_selection'
            
            # 如果还是不匹配，抛出错误
            raise ValueError(f"无法为模型匹配合适的特征数量。期望: {expected_features}")
            
        except Exception as e:
            print(f"❌ 自动特征匹配失败: {e}")
            raise
'''
    
    with open('improved_ai_v2_predict.py', 'w', encoding='utf-8') as f:
        f.write(improved_predict)
    
    print("✅ 改进的AI V2预测方法已创建: improved_ai_v2_predict.py")
    
    return True

def test_fixed_prediction():
    """测试修复后的预测"""
    print(f"\n🧪 测试修复后的预测")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer = get_ai_analyzer_v2()
        
        # 测试文件
        test_file = "training_data/III/1-2.txt"
        
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        df = pd.read_csv(test_file, sep='\t', encoding='utf-8')
        
        # 标准化列名
        column_mapping = {
            'Depth(m)': 'Depth',
            '1-2 Speed%': 'S1', '1-2 Amp%': 'A1',
            '1-3 Speed%': 'S2', '1-3 Amp%': 'A2', 
            '2-3 Speed%': 'S3', '2-3 Amp%': 'A3'
        }
        df = df.rename(columns=column_mapping)
        
        print(f"📁 测试文件: {test_file}")
        print(f"📊 数据形状: {df.shape}")
        
        # 尝试使用兼容模型
        models = analyzer.model_manager.get_available_models()
        
        for key, model_info in models.items():
            if 'compatible' in key.lower() or model_info.model_type == 'enhanced':
                try:
                    print(f"\n🎯 测试模型: {model_info.name}")
                    analyzer.set_model(key)
                    
                    # 使用改进的预测方法（如果可用）
                    if hasattr(analyzer, 'predict_with_auto_features'):
                        result = analyzer.predict_with_auto_features(df)
                    else:
                        result = analyzer.predict(df)
                    
                    if result:
                        print(f"✅ 预测成功!")
                        print(f"🎯 预测类别: {result.get('完整性类别', 'N/A')}")
                        print(f"🎯 置信度: {result.get('ai_confidence', 0):.2%}")
                        return True
                    
                except Exception as e:
                    print(f"⚠️ 模型 {model_info.name} 测试失败: {e}")
                    continue
        
        print(f"❌ 所有模型测试失败")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复特征数量不匹配问题")
    print("=" * 100)
    
    # 1. 分析模型特征需求
    model_info = analyze_model_feature_requirements()
    
    # 2. 测试特征提取器
    test_feature_extractors()
    
    # 3. 创建自动特征匹配功能
    create_auto_feature_matching()
    
    # 4. 修复AI V2特征匹配
    fix_success = fix_ai_v2_feature_matching()
    
    # 5. 测试修复后的预测
    if fix_success:
        test_success = test_fixed_prediction()
    else:
        test_success = False
    
    # 总结
    print(f"\n📋 修复结果总结")
    print("=" * 100)
    
    print(f"模型分析: ✅ 完成")
    print(f"特征提取器测试: ✅ 完成")
    print(f"自动匹配功能: ✅ 创建")
    print(f"AI V2修复: {'✅ 成功' if fix_success else '❌ 失败'}")
    print(f"预测测试: {'✅ 成功' if test_success else '❌ 失败'}")
    
    print(f"\n💡 解决方案:")
    print("1. 使用 improved_ai_v2_predict.py 中的改进方法")
    print("2. 或者在GUI中选择 'Compatible Enhanced Model'")
    print("3. 确保特征提取器与模型匹配")
    
    if test_success:
        print(f"\n🎉 特征匹配问题已修复!")
    else:
        print(f"\n⚠️ 需要进一步调试特征匹配问题")

if __name__ == "__main__":
    main()
