#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型管理器 - 支持多个模型版本管理
Model Manager - Support Multiple Model Version Management
"""

import os
import pickle
import json
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

@dataclass
class ModelInfo:
    """模型信息数据类"""
    name: str
    description: str
    version: str
    accuracy: float
    feature_count: int
    model_type: str  # 'standard', 'optimized', 'custom'
    file_path: str
    created_date: str
    file_size: int
    is_loaded: bool = False

    def to_dict(self):
        """转换为字典"""
        return {
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'accuracy': self.accuracy,
            'feature_count': self.feature_count,
            'model_type': self.model_type,
            'file_path': self.file_path,
            'created_date': self.created_date,
            'file_size': self.file_size,
            'is_loaded': self.is_loaded
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建"""
        return cls(**data)

class ModelManager:
    """模型管理器"""

    def __init__(self, models_dir="ai_models"):
        self.models_dir = models_dir
        self.models_registry = {}  # 模型注册表
        self.loaded_models = {}    # 已加载的模型
        self.current_model_key = None
        self.registry_file = os.path.join(models_dir, "models_registry.json")

        # 确保模型目录存在
        os.makedirs(models_dir, exist_ok=True)

        # 加载模型注册表
        self.load_registry()

        # 自动发现和注册默认模型
        self.auto_discover_models()

    def auto_discover_models(self):
        """自动发现和注册模型"""
        print("🔍 自动发现模型...")

        # 预定义的模型路径和信息
        default_models = [
            {
                'key': 'standard_v1',
                'name': '标准AI模型 v1.0',
                'description': '基础AI模型，使用54个标准特征，快速分析',
                'version': '1.0',
                'accuracy': 0.47,
                'feature_count': 54,
                'model_type': 'standard',
                'file_path': 'advanced_models/model_a1.pkl'
            },
            {
                'key': 'optimized_v1',
                'name': '高精度AI模型 v1.0',
                'description': '优化AI模型，使用118个增强特征，95%以上准确率',
                'version': '1.0',
                'accuracy': 0.94,
                'feature_count': 118,
                'model_type': 'optimized',
                'file_path': 'optimized_model_95.pkl'
            }
        ]

        for model_config in default_models:
            file_path = model_config['file_path']
            full_path = os.path.join(os.path.dirname(self.models_dir), file_path)

            if os.path.exists(full_path):
                # 获取文件信息
                file_size = os.path.getsize(full_path)
                created_date = datetime.fromtimestamp(os.path.getctime(full_path)).strftime('%Y-%m-%d %H:%M:%S')

                # 创建模型信息
                model_info = ModelInfo(
                    name=model_config['name'],
                    description=model_config['description'],
                    version=model_config['version'],
                    accuracy=model_config['accuracy'],
                    feature_count=model_config['feature_count'],
                    model_type=model_config['model_type'],
                    file_path=full_path,
                    created_date=created_date,
                    file_size=file_size
                )

                # 注册模型
                self.register_model(model_config['key'], model_info)
                print(f"✅ 发现模型: {model_info.name}")
            else:
                print(f"⚠️ 模型文件不存在: {full_path}")

    def register_model(self, key: str, model_info: ModelInfo):
        """注册模型"""
        self.models_registry[key] = model_info
        print(f"📝 注册模型: {key} - {model_info.name}")

        # 保存注册表
        self.save_registry()

    def unregister_model(self, key: str):
        """注销模型"""
        if key in self.models_registry:
            model_info = self.models_registry[key]
            del self.models_registry[key]

            # 如果模型已加载，也要卸载
            if key in self.loaded_models:
                del self.loaded_models[key]

            # 如果是当前模型，清除当前模型
            if self.current_model_key == key:
                self.current_model_key = None

            print(f"🗑️ 注销模型: {key} - {model_info.name}")
            self.save_registry()
            return True
        return False

    def load_model(self, key: str):
        """加载模型"""
        if key not in self.models_registry:
            raise ValueError(f"Unknown model: {key}")

        # 如果已经加载，直接返回
        if key in self.loaded_models:
            print(f"✅ 模型已加载: {key}")
            return self.loaded_models[key]

        model_info = self.models_registry[key]

        try:
            print(f"📥 加载模型: {model_info.name}")

            with open(model_info.file_path, 'rb') as f:
                model_data = pickle.load(f)

            # 根据模型类型处理不同的数据格式
            if model_info.model_type == 'optimized':
                # 优化模型格式
                if isinstance(model_data, dict) and 'model' in model_data:
                    processed_model = {
                        'classifier_model': model_data.get('model'),
                        'preprocessor': model_data.get('preprocessor'),
                        'model_type': 'optimized',
                        'feature_count': model_info.feature_count
                    }
                else:
                    raise ValueError("Invalid optimized model format")
            else:
                # 标准模型格式
                if hasattr(model_data, 'predict'):
                    # 单个分类器
                    processed_model = {
                        'classifier_model': model_data,
                        'model_type': 'standard',
                        'feature_count': model_info.feature_count
                    }
                elif isinstance(model_data, dict):
                    # 完整模型包
                    processed_model = model_data
                    processed_model['model_type'] = 'standard'
                    processed_model['feature_count'] = model_info.feature_count
                else:
                    raise ValueError("Invalid standard model format")

            # 缓存加载的模型
            self.loaded_models[key] = processed_model

            # 更新模型信息
            self.models_registry[key].is_loaded = True

            print(f"✅ 模型加载成功: {model_info.name}")
            return processed_model

        except Exception as e:
            print(f"❌ 模型加载失败: {model_info.name} - {e}")
            raise

    def unload_model(self, key: str):
        """卸载模型"""
        if key in self.loaded_models:
            del self.loaded_models[key]
            self.models_registry[key].is_loaded = False
            print(f"🔄 模型已卸载: {key}")

    def set_current_model(self, key: str):
        """设置当前模型"""
        if key not in self.models_registry:
            raise ValueError(f"Unknown model: {key}")

        # 加载模型（如果未加载）
        self.load_model(key)

        self.current_model_key = key
        model_info = self.models_registry[key]
        print(f"🎯 切换到模型: {model_info.name}")

    def get_current_model(self):
        """获取当前模型"""
        if self.current_model_key is None:
            return None

        return self.loaded_models.get(self.current_model_key)

    def get_current_model_info(self):
        """获取当前模型信息"""
        if self.current_model_key is None:
            return None

        return self.models_registry.get(self.current_model_key)

    def get_available_models(self) -> Dict[str, ModelInfo]:
        """获取可用模型列表"""
        return self.models_registry.copy()

    def get_model_info(self, key: str) -> Optional[ModelInfo]:
        """获取指定模型信息"""
        return self.models_registry.get(key)

    def save_registry(self):
        """保存模型注册表"""
        try:
            registry_data = {
                key: model_info.to_dict()
                for key, model_info in self.models_registry.items()
            }

            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(registry_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"⚠️ 保存模型注册表失败: {e}")

    def load_registry(self):
        """加载模型注册表"""
        try:
            if os.path.exists(self.registry_file):
                with open(self.registry_file, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)

                self.models_registry = {
                    key: ModelInfo.from_dict(data)
                    for key, data in registry_data.items()
                }

                print(f"✅ 加载模型注册表: {len(self.models_registry)} 个模型")
            else:
                print("📝 创建新的模型注册表")

        except Exception as e:
            print(f"⚠️ 加载模型注册表失败: {e}")
            self.models_registry = {}

    def load_external_model(self, file_path: str, model_name: str = None) -> bool:
        """加载外部模型文件"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ 模型文件不存在: {file_path}")
                return False

            print(f"🔍 分析外部模型: {file_path}")

            # 分析模型文件
            model_info = self._analyze_model_file(file_path, model_name)

            if model_info is None:
                print(f"❌ 无法分析模型文件: {file_path}")
                return False

            # 生成唯一的模型键
            base_key = model_name.lower().replace(' ', '_') if model_name else 'external_model'
            model_key = base_key
            counter = 1
            while model_key in self.models_registry:
                model_key = f"{base_key}_{counter}"
                counter += 1

            # 注册模型
            self.register_model(model_key, model_info)

            print(f"✅ 外部模型加载成功: {model_info.name}")
            return True

        except Exception as e:
            print(f"❌ 加载外部模型失败: {e}")
            return False

    def _analyze_model_file(self, file_path: str, model_name: str = None) -> Optional[ModelInfo]:
        """分析模型文件并提取信息"""
        try:
            # 加载模型文件
            with open(file_path, 'rb') as f:
                model_data = pickle.load(f)

            # 获取文件信息
            file_size = os.path.getsize(file_path)
            created_date = datetime.fromtimestamp(os.path.getctime(file_path)).strftime('%Y-%m-%d %H:%M:%S')

            # 分析模型类型和特征数量
            model_type, feature_count, estimated_accuracy = self._detect_model_format(model_data)

            # 生成模型名称
            if model_name is None:
                filename = os.path.basename(file_path)
                model_name = f"外部模型 - {filename}"

            # 生成描述
            description = f"从 {os.path.basename(file_path)} 加载的外部模型"
            if model_type == 'optimized':
                description += "，高精度优化模型"
            elif model_type == 'standard':
                description += "，标准模型"

            # 创建模型信息
            model_info = ModelInfo(
                name=model_name,
                description=description,
                version="External",
                accuracy=estimated_accuracy,
                feature_count=feature_count,
                model_type=model_type,
                file_path=file_path,
                created_date=created_date,
                file_size=file_size
            )

            return model_info

        except Exception as e:
            print(f"❌ 分析模型文件失败: {e}")
            return None

    def _detect_model_format(self, model_data) -> Tuple[str, int, float]:
        """检测模型格式并估算参数"""
        try:
            model_type = 'unknown'
            feature_count = 54  # 默认特征数量
            estimated_accuracy = 0.5  # 默认准确率

            if isinstance(model_data, dict):
                if 'model' in model_data and 'preprocessor' in model_data:
                    # 优化模型格式
                    model_type = 'optimized'
                    feature_count = 118  # 优化模型通常使用118个特征
                    estimated_accuracy = 0.9  # 优化模型通常有更高准确率

                elif 'classifier_model' in model_data:
                    # 标准完整模型格式
                    model_type = 'standard'
                    feature_count = 54
                    estimated_accuracy = 0.7

                else:
                    # 其他字典格式
                    model_type = 'custom'

            elif hasattr(model_data, 'predict'):
                # 单个分类器
                model_type = 'standard'
                feature_count = 54
                estimated_accuracy = 0.6

                # 尝试从模型属性获取更多信息
                if hasattr(model_data, 'n_features_in_'):
                    feature_count = model_data.n_features_in_
                elif hasattr(model_data, 'feature_importances_'):
                    feature_count = len(model_data.feature_importances_)

            return model_type, feature_count, estimated_accuracy

        except Exception as e:
            print(f"⚠️ 模型格式检测失败: {e}")
            return 'unknown', 54, 0.5

    def get_model_statistics(self):
        """获取模型统计信息"""
        total_models = len(self.models_registry)
        loaded_models = sum(1 for info in self.models_registry.values() if info.is_loaded)

        model_types = {}
        for info in self.models_registry.values():
            model_types[info.model_type] = model_types.get(info.model_type, 0) + 1

        return {
            'total_models': total_models,
            'loaded_models': loaded_models,
            'model_types': model_types,
            'current_model': self.current_model_key
        }

# 全局模型管理器实例
model_manager = ModelManager()

def get_model_manager():
    """获取全局模型管理器"""
    return model_manager
