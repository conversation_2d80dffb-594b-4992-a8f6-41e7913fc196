#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化的基于GZ传统方法的合成训练数据生成器
直接基于GZ判定规则生成高质量的合成数据
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
from typing import List, Tuple


class OptimizedGZDataGenerator:
    """优化的GZ方法合成数据生成器"""

    def __init__(self):
        """初始化生成器"""
        # 直接基于GZ判定规则的参数组合
        self.pile_templates = {
            'I类桩': {
                'combinations': [
                    # (sp_range, ad_range, bi_ratio_range) -> 确保I(j,i)=1
                    ((100, 110), (-2, 0), (0.85, 1.0)),    # Sp≥100, Ad≤0, Bi>0.8
                    ((85, 100), (-2, 0), (0.85, 1.0)),     # 85≤Sp<100, Ad≤0, Bi>0.8
                    ((100, 110), (0, 4), (0.85, 1.0)),     # Sp≥100, 0<Ad≤4, Bi>0.8
                ],
                'weights': [0.5, 0.3, 0.2],  # 权重分布
                'description': '完整桩，无缺陷'
            },
            'II类桩': {
                'combinations': [
                    # 确保I(j,i)=2的组合
                    ((85, 100), (0, 4), (0.5, 0.8)),       # 85≤Sp<100, 0<Ad≤4, 0.5<Bi≤0.8
                    ((75, 85), (-2, 4), (0.5, 1.0)),       # 75≤Sp<85, Ad≤4, Bi>0.5
                    ((85, 110), (4, 8), (0.5, 1.0)),       # Sp≥85, 4<Ad≤8, Bi>0.5
                ],
                'weights': [0.4, 0.3, 0.3],
                'description': '轻微缺陷桩'
            },
            'III类桩': {
                'combinations': [
                    # 确保I(j,i)=3的组合
                    ((75, 85), (4, 8), (0.25, 0.5)),       # 75≤Sp<85, 4<Ad≤8, 0.25<Bi≤0.5
                    ((65, 75), (-2, 8), (0.25, 1.0)),      # 65≤Sp<75, Ad≤8, Bi>0.25
                    ((75, 110), (8, 12), (0.25, 1.0)),     # Sp≥75, 8<Ad≤12, Bi>0.25
                ],
                'weights': [0.4, 0.3, 0.3],
                'description': '明显缺陷桩'
            },
            'IV类桩': {
                'combinations': [
                    # 确保I(j,i)=4的组合
                    ((65, 75), (8, 12), (0.05, 0.25)),     # 65≤Sp<75, 8<Ad≤12, Bi≤0.25
                    ((30, 65), (-2, 12), (0.05, 0.25)),    # Sp<65, Ad≤12, Bi≤0.25
                    ((65, 110), (12, 20), (0.05, 0.25)),   # Sp≥65, Ad>12, Bi≤0.25
                ],
                'weights': [0.3, 0.4, 0.3],
                'description': '严重缺陷桩'
            }
        }

    def calculate_I_ji(self, Sp: float, Ad: float, Bi_ratio: float) -> int:
        """根据GZ方法计算I(j,i)值"""
        # I(j,i) = 1 (完整性最好)
        if Bi_ratio > 0.8:
            if (Sp >= 100 and Ad <= 0) or \
               (85 <= Sp < 100 and Ad <= 0) or \
               (Sp >= 100 and 0 < Ad <= 4):
                return 1

        # I(j,i) = 2 (轻微缺陷)
        if (0.5 < Bi_ratio <= 0.8 and 85 <= Sp < 100 and 0 < Ad <= 4) or \
           (Bi_ratio > 0.5 and 75 <= Sp < 85 and Ad <= 4) or \
           (Bi_ratio > 0.5 and Sp >= 85 and 4 < Ad <= 8):
            return 2

        # I(j,i) = 3 (明显缺陷)
        if (0.25 < Bi_ratio <= 0.5 and 75 <= Sp < 85 and 4 < Ad <= 8) or \
           (Bi_ratio > 0.25 and 65 <= Sp < 75 and Ad <= 8) or \
           (Bi_ratio > 0.25 and Sp >= 75 and 8 < Ad <= 12):
            return 3

        # I(j,i) = 4 (严重缺陷)
        return 4

    def calculate_K_i(self, I_ji_values: List[int]) -> int:
        """计算某深度的K(i)值"""
        if not I_ji_values:
            return 0

        valid_I_ji = [i for i in I_ji_values if i in [1, 2, 3, 4]]
        if not valid_I_ji:
            return 0

        sum_I_ji_sq = sum(i**2 for i in valid_I_ji)
        sum_I_ji = sum(valid_I_ji)

        if sum_I_ji == 0:
            return 0

        K_i_float = (sum_I_ji_sq / sum_I_ji) + 0.5
        return int(K_i_float)

    def determine_final_category(self, K_values: List[int]) -> str:
        """根据K值分布确定最终桩身完整性类别"""
        has_K4 = any(k == 4 for k in K_values)
        has_K3 = any(k == 3 for k in K_values)
        has_K2 = any(k == 2 for k in K_values)

        if has_K4:
            return "IV类桩"

        # 检查连续K=3
        consecutive_K3 = 0
        max_consecutive_K3 = 0
        for k in K_values:
            if k == 3:
                consecutive_K3 += 1
                max_consecutive_K3 = max(max_consecutive_K3, consecutive_K3)
            else:
                consecutive_K3 = 0

        if max_consecutive_K3 >= 6:
            return "IV类桩"

        if has_K3:
            return "III类桩"

        # 检查连续K=2
        consecutive_K2 = 0
        max_consecutive_K2 = 0
        for k in K_values:
            if k == 2:
                consecutive_K2 += 1
                max_consecutive_K2 = max(max_consecutive_K2, consecutive_K2)
            else:
                consecutive_K2 = 0

        if max_consecutive_K2 >= 6 and not has_K3 and not has_K4:
            return "III类桩"

        if has_K2 and not has_K3 and not has_K4:
            return "II类桩"

        if all(k == 1 for k in K_values):
            return "I类桩"

        return "未定类别"

    def generate_pile_data(self, pile_class: str, num_depths: int = 100) -> pd.DataFrame:
        """生成指定类别的桩身数据"""
        if pile_class not in self.pile_templates:
            raise ValueError(f"不支持的桩类: {pile_class}")

        template = self.pile_templates[pile_class]
        combinations = template['combinations']
        weights = template['weights']

        # 生成深度序列
        depths = np.linspace(10, 30, num_depths)

        data_rows = []

        for depth in depths:
            # 随机选择参数组合
            combo_idx = np.random.choice(len(combinations), p=weights)
            sp_range, ad_range, bi_range = combinations[combo_idx]

            # 为三个剖面生成参数
            row_data = {'Depth': depth}

            for profile_idx in range(3):
                # 生成参数
                sp = np.random.uniform(*sp_range)
                ad = np.random.uniform(*ad_range)
                bi = np.random.uniform(*bi_range)

                # 添加小量随机噪声
                sp += np.random.normal(0, 2)
                ad += np.random.normal(0, 0.5)
                bi += np.random.normal(0, 0.05)

                # 确保参数在合理范围内
                sp = np.clip(sp, 30, 150)
                ad = np.clip(ad, -5, 25)
                bi = np.clip(bi, 0.05, 1.0)

                row_data[f'S{profile_idx+1}'] = sp
                row_data[f'A{profile_idx+1}'] = ad
                row_data[f'BI{profile_idx+1}'] = bi  # 保存bi_ratio用于验证

            data_rows.append(row_data)

        return pd.DataFrame(data_rows)

    def verify_data(self, df: pd.DataFrame) -> str:
        """验证生成的数据符合哪个桩类"""
        K_values = []

        for _, row in df.iterrows():
            I_ji_values = []

            for profile_idx in range(3):
                sp = row[f'S{profile_idx+1}']
                ad = row[f'A{profile_idx+1}']

                # 使用实际的bi_ratio值（如果存在）
                if f'BI{profile_idx+1}' in row:
                    bi_ratio = row[f'BI{profile_idx+1}']
                else:
                    bi_ratio = 1.0  # 默认值

                I_ji = self.calculate_I_ji(sp, ad, bi_ratio)
                I_ji_values.append(I_ji)

            K_i = self.calculate_K_i(I_ji_values)
            K_values.append(K_i)

        return self.determine_final_category(K_values)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成优化的基于GZ方法的合成桩基数据')
    parser.add_argument('--samples', type=int, default=50, help='每个桩类生成的样本数量')
    parser.add_argument('--class_type', type=str, choices=['I', 'II', 'III', 'IV', 'all'], default='all')
    parser.add_argument('--output_dir', type=str, default='optimized_training_data')
    parser.add_argument('--verify', action='store_true', help='验证生成的数据')

    args = parser.parse_args()

    generator = OptimizedGZDataGenerator()

    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), args.output_dir)
    os.makedirs(output_dir, exist_ok=True)

    class_dirs = {
        'I类桩': os.path.join(output_dir, 'I'),
        'II类桩': os.path.join(output_dir, 'II'),
        'III类桩': os.path.join(output_dir, 'III'),
        'IV类桩': os.path.join(output_dir, 'IV')
    }

    for class_dir in class_dirs.values():
        os.makedirs(class_dir, exist_ok=True)

    # 确定要生成的桩类
    if args.class_type == 'all':
        pile_classes = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
    else:
        pile_classes = [f'{args.class_type}类桩']

    print(f"🚀 开始生成优化的GZ合成数据，每类 {args.samples} 个样本...")

    total_generated = 0
    total_verified = 0

    for pile_class in pile_classes:
        print(f"\n📊 正在为 {pile_class} 生成数据...")

        target_dir = class_dirs[pile_class]
        class_generated = 0
        class_verified = 0

        for i in range(args.samples):
            try:
                # 生成数据
                df = generator.generate_pile_data(pile_class)

                # 验证数据
                if args.verify:
                    predicted = generator.verify_data(df)
                    if predicted == pile_class:
                        class_verified += 1
                        total_verified += 1
                    else:
                        print(f"⚠️  样本 {i+1} 预测为 {predicted}，目标为 {pile_class}")

                # 保存文件
                file_name = f"optimized_{pile_class}_{i+1:03d}.txt"
                file_path = os.path.join(target_dir, file_name)

                # 标准格式输出（只保存前7列，排除BI列）
                df_output = df[['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']].copy()
                df_output.columns = ['Depth(m)', '1-2 Speed%', '1-2 Amp%', '1-3 Speed%', '1-3 Amp%', '2-3 Speed%', '2-3 Amp%']
                df_output.to_csv(file_path, sep='\t', index=False, float_format='%.2f')

                class_generated += 1
                total_generated += 1

                if (i + 1) % 10 == 0:
                    print(f"  ✅ 已生成 {i + 1}/{args.samples} 个样本")

            except Exception as e:
                print(f"❌ 生成样本 {i+1} 时出错: {str(e)}")

        print(f"✅ {pile_class} 完成: 生成 {class_generated} 个样本")
        if args.verify:
            accuracy = (class_verified / class_generated * 100) if class_generated > 0 else 0
            print(f"📈 验证准确率: {accuracy:.1f}% ({class_verified}/{class_generated})")

    print(f"\n🎉 优化合成数据生成完成!")
    print(f"📊 总计生成: {total_generated} 个样本")
    if args.verify:
        overall_accuracy = (total_verified / total_generated * 100) if total_generated > 0 else 0
        print(f"📈 总体验证准确率: {overall_accuracy:.1f}% ({total_verified}/{total_generated})")


if __name__ == "__main__":
    main()
