{"standard_v1": {"name": "标准AI模型 v1.0", "description": "基础AI模型，使用54个标准特征，快速分析", "version": "1.0", "accuracy": 0.47, "feature_count": 54, "model_type": "standard", "file_path": "advanced_models/model_a1.pkl", "created_date": "2025-05-26 10:07:10", "file_size": 1307874, "is_loaded": false}, "optimized_v1": {"name": "高精度AI模型 v1.0", "description": "优化AI模型，使用118个增强特征，95%以上准确率", "version": "1.0", "accuracy": 0.94, "feature_count": 118, "model_type": "optimized", "file_path": "optimized_model_95.pkl", "created_date": "2025-05-26 12:31:13", "file_size": 2396383, "is_loaded": false}, "演示高性能模型_v1.0": {"name": "演示高性能模型 v1.0", "description": "从 demo_high_performance_model.pkl 加载的外部模型，标准模型", "version": "External", "accuracy": 0.7, "feature_count": 54, "model_type": "standard", "file_path": "demo_models\\demo_high_performance_model.pkl", "created_date": "2025-05-26 13:12:56", "file_size": 1011099, "is_loaded": false}, "测试标准模型": {"name": "测试标准模型", "description": "从 test_external_model.pkl 加载的外部模型，标准模型", "version": "External", "accuracy": 0.7, "feature_count": 54, "model_type": "standard", "file_path": "ai_models\\test_external_model.pkl", "created_date": "2025-05-26 13:28:36", "file_size": 348495, "is_loaded": false}, "测试优化模型": {"name": "测试优化模型", "description": "从 test_optimized_model.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "ai_models\\test_optimized_model.pkl", "created_date": "2025-05-26 13:28:36", "file_size": 440066, "is_loaded": false}, "对话框测试模型": {"name": "对话框测试模型", "description": "从 dialog_test_model.pkl 加载的外部模型，标准模型", "version": "External", "accuracy": 0.7, "feature_count": 54, "model_type": "standard", "file_path": "ai_models\\dialog_test_model.pkl", "created_date": "2025-05-26 13:37:26", "file_size": 121994, "is_loaded": false}, "最终测试模型": {"name": "最终测试模型", "description": "从 final_test_model.pkl 加载的外部模型，标准模型", "version": "External", "accuracy": 0.7, "feature_count": 54, "model_type": "standard", "file_path": "ai_models\\final_test_model.pkl", "created_date": "2025-05-26 14:29:18", "file_size": 17010, "is_loaded": false}, "外部模型_-_model_a1.pkl": {"name": "外部模型 - model_a1.pkl", "description": "从 model_a1.pkl 加载的外部模型，标准模型", "version": "External", "accuracy": 0.6, "feature_count": 54, "model_type": "standard", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl", "created_date": "2025-05-26 10:07:10", "file_size": 195589, "is_loaded": false}, "外部模型_-_model_a1.pkl_1": {"name": "外部模型 - model_a1.pkl", "description": "从 model_a1.pkl 加载的外部模型，标准模型", "version": "External", "accuracy": 0.6, "feature_count": 54, "model_type": "standard", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl", "created_date": "2025-05-26 10:07:10", "file_size": 195589, "is_loaded": false}, "外部模型_-_model_a1.pkl_2": {"name": "外部模型 - model_a1.pkl", "description": "从 model_a1.pkl 加载的外部模型，标准模型", "version": "External", "accuracy": 0.6, "feature_count": 54, "model_type": "standard", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl", "created_date": "2025-05-26 10:07:10", "file_size": 195589, "is_loaded": false}, "外部模型_-_model_r1.pkl": {"name": "外部模型 - model_r1.pkl", "description": "从 model_r1.pkl 加载的外部模型", "version": "External", "accuracy": 0.5, "feature_count": 54, "model_type": "custom", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_r1.pkl", "created_date": "2025-05-26 10:08:08", "file_size": 1307874, "is_loaded": true}, "外部模型_-_model_q1.pkl": {"name": "外部模型 - model_q1.pkl", "description": "从 model_q1.pkl 加载的外部模型", "version": "External", "accuracy": 0.5, "feature_count": 54, "model_type": "custom", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_q1.pkl", "created_date": "2025-05-26 09:47:08", "file_size": 1307874, "is_loaded": false}, "compatible_enhanced": {"name": "Compatible Enhanced Model (85.78%)", "description": "兼容的增强AI模型，使用118个特征，85.78%准确率", "version": "1.0", "accuracy": 0.8578, "feature_count": 118, "model_type": "enhanced", "file_path": "compatible_ai_model.pkl", "created_date": "2025-05-26 16:33:08", "file_size": 1310741, "is_loaded": false}, "外部模型_-_model_r1.pkl_1": {"name": "外部模型 - model_r1.pkl", "description": "从 model_r1.pkl 加载的外部模型", "version": "External", "accuracy": 0.5, "feature_count": 54, "model_type": "custom", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_r1.pkl", "created_date": "2025-05-26 10:08:08", "file_size": 1307874, "is_loaded": false}, "外部模型_-_model_a1.pkl_3": {"name": "外部模型 - model_a1.pkl", "description": "从 model_a1.pkl 加载的外部模型", "version": "External", "accuracy": 0.5, "feature_count": 54, "model_type": "custom", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl", "created_date": "2025-05-26 10:07:10", "file_size": 1307874, "is_loaded": false}, "外部模型_-_model_r1.pkl_2": {"name": "外部模型 - model_r1.pkl", "description": "从 model_r1.pkl 加载的外部模型", "version": "External", "accuracy": 0.5, "feature_count": 54, "model_type": "custom", "file_path": "F:/2025/AIpile/AIpiles_final/advanced_models/model_r1.pkl", "created_date": "2025-05-26 10:08:08", "file_size": 1307874, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_1": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_2": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_3": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_4": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_5": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_6": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_7": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_8": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_9": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_10": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_11": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_12": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_13": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": true}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_14": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_15": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_16": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_17": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_18": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_19": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_20": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_21": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部_-_ai_pile_model_enhanced_20250527_044217": {"name": "外部 - ai_pile_model_enhanced_20250527_044217", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": true}, "外部_-_ai_pile_model_enhanced_20250527_044217_1": {"name": "外部 - ai_pile_model_enhanced_20250527_044217", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}, "外部模型_-_ai_pile_model_enhanced_20250527_044217.pkl_22": {"name": "外部模型 - ai_pile_model_enhanced_20250527_044217.pkl", "description": "从 ai_pile_model_enhanced_20250527_044217.pkl 加载的外部模型，高精度优化模型", "version": "External", "accuracy": 0.9, "feature_count": 118, "model_type": "optimized", "file_path": "F:/2025/AIpile/AIpiles_final/models/ai_pile_model_enhanced_20250527_044217.pkl", "created_date": "2025-05-27 07:30:22", "file_size": 519234, "is_loaded": false}}