#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的外部模型加载功能
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def create_test_model_in_ai_models():
    """在ai_models目录中创建测试模型"""
    print("🔧 在ai_models目录中创建测试模型")
    print("=" * 60)
    
    # 确保ai_models目录存在
    ai_models_dir = "ai_models"
    os.makedirs(ai_models_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    X = np.random.randn(150, 54)  # 54个特征
    y = np.random.randint(0, 4, 150)  # 4个类别
    
    print("📊 训练测试模型...")
    
    # 数据预处理
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 训练分类器
    classifier = RandomForestClassifier(
        n_estimators=50,
        max_depth=8,
        random_state=42,
        class_weight='balanced'
    )
    classifier.fit(X_scaled, y)
    
    # 计算训练准确率
    train_accuracy = classifier.score(X_scaled, y)
    print(f"✅ 训练准确率: {train_accuracy:.2%}")
    
    # 创建完整模型包
    test_model = {
        'classifier_model': classifier,
        'scaler': scaler,
        'feature_importance': dict(zip([f'特征_{i+1}' for i in range(54)], 
                                     classifier.feature_importances_)),
        'metadata': {
            'name': '测试外部模型',
            'version': '1.0',
            'accuracy': train_accuracy,
            'features': 54,
            'classes': ['I类桩', 'II类桩', 'III类桩', 'IV类桩'],
            'description': '用于测试外部模型加载功能的示例模型'
        }
    }
    
    # 保存到ai_models目录
    model_path = os.path.join(ai_models_dir, "test_external_model.pkl")
    with open(model_path, 'wb') as f:
        pickle.dump(test_model, f)
    
    print(f"✅ 测试模型已保存到: {model_path}")
    print(f"📁 文件大小: {os.path.getsize(model_path) / 1024:.1f} KB")
    
    return model_path

def create_optimized_model_in_ai_models():
    """在ai_models目录中创建优化模型"""
    print("\n🚀 在ai_models目录中创建优化模型")
    print("=" * 60)
    
    # 确保ai_models目录存在
    ai_models_dir = "ai_models"
    os.makedirs(ai_models_dir, exist_ok=True)
    
    # 生成118个特征的数据
    np.random.seed(123)
    X = np.random.randn(200, 118)  # 118个特征
    y = np.random.randint(0, 4, 200)  # 4个类别
    
    print("📊 训练优化模型...")
    
    from sklearn.pipeline import Pipeline
    from sklearn.preprocessing import PowerTransformer
    from sklearn.feature_selection import SelectKBest, f_classif
    from sklearn.ensemble import VotingClassifier, GradientBoostingClassifier
    
    # 创建预处理器
    preprocessor = Pipeline([
        ('transformer', PowerTransformer()),
        ('selector', SelectKBest(f_classif, k=100))
    ])
    
    X_processed = preprocessor.fit_transform(X, y)
    
    # 创建集成模型
    rf = RandomForestClassifier(n_estimators=30, random_state=42)
    gb = GradientBoostingClassifier(n_estimators=30, random_state=42)
    
    ensemble = VotingClassifier([
        ('rf', rf),
        ('gb', gb)
    ], voting='soft')
    
    ensemble.fit(X_processed, y)
    
    # 计算训练准确率
    train_accuracy = ensemble.score(X_processed, y)
    print(f"✅ 训练准确率: {train_accuracy:.2%}")
    
    # 创建优化模型格式
    optimized_model = {
        'model': ensemble,
        'preprocessor': preprocessor,
        'metadata': {
            'name': '测试优化模型',
            'version': '2.0',
            'accuracy': train_accuracy,
            'features': 118,
            'classes': ['I类桩', 'II类桩', 'III类桩', 'IV类桩'],
            'description': '用于测试的高精度优化模型'
        }
    }
    
    # 保存到ai_models目录
    model_path = os.path.join(ai_models_dir, "test_optimized_model.pkl")
    with open(model_path, 'wb') as f:
        pickle.dump(optimized_model, f)
    
    print(f"✅ 优化模型已保存到: {model_path}")
    print(f"📁 文件大小: {os.path.getsize(model_path) / 1024:.1f} KB")
    
    return model_path

def test_model_loading_programmatically():
    """程序化测试模型加载功能"""
    print("\n🧪 程序化测试模型加载功能")
    print("=" * 60)
    
    try:
        from model_manager import get_model_manager
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        # 创建测试模型
        standard_model_path = create_test_model_in_ai_models()
        optimized_model_path = create_optimized_model_in_ai_models()
        
        model_manager = get_model_manager()
        
        print(f"\n📥 测试标准模型加载:")
        success1 = model_manager.load_external_model(standard_model_path, "测试标准模型")
        print(f"结果: {'✅ 成功' if success1 else '❌ 失败'}")
        
        print(f"\n📥 测试优化模型加载:")
        success2 = model_manager.load_external_model(optimized_model_path, "测试优化模型")
        print(f"结果: {'✅ 成功' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            print(f"\n📋 当前所有模型:")
            models = model_manager.get_available_models()
            for key, model_info in models.items():
                print(f"  - {model_info.name} ({model_info.model_type}, {model_info.accuracy:.1%})")
            
            # 测试预测功能
            print(f"\n🎯 测试预测功能:")
            analyzer = get_ai_analyzer_v2()
            
            # 创建测试数据
            test_data = {
                'Depth(m)': [0.5, 1.0, 1.5, 2.0],
                '1-2 Speed%': [95, 90, 85, 80],
                '1-2 Amp%': [2, 5, 8, 12],
                '1-3 Speed%': [94, 89, 84, 79],
                '1-3 Amp%': [3, 6, 9, 13],
                '2-3 Speed%': [96, 91, 86, 81],
                '2-3 Amp%': [1, 4, 7, 11]
            }
            
            df = pd.DataFrame(test_data)
            
            # 测试新加载的模型
            for key, model_info in models.items():
                if "测试" in model_info.name:
                    print(f"\n测试模型: {model_info.name}")
                    
                    try:
                        analyzer.set_model(key)
                        result = analyzer.predict(df)
                        
                        if result:
                            category = result.get('完整性类别', 'N/A')
                            confidence = result.get('ai_confidence', 0.0)
                            
                            # 转换数字类别为中文名称
                            category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                            if isinstance(category, (int, float, np.integer, np.floating)):
                                category_name = category_mapping.get(int(category), f'未知类别({category})')
                            else:
                                category_name = category
                            
                            print(f"  ✅ 预测成功: {category_name} (置信度: {confidence:.2%})")
                        else:
                            print(f"  ❌ 预测失败")
                            
                    except Exception as e:
                        print(f"  ❌ 测试失败: {e}")
        
        return success1 and success2
        
    except Exception as e:
        print(f"❌ 程序化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_gui_instructions():
    """显示GUI使用说明"""
    print(f"\n🖥️ GUI测试说明")
    print("=" * 60)
    
    print("📋 现在您可以测试修复后的GUI功能:")
    print("1. 启动GUI: python Pile_analyze_GZ_gui.py")
    print("2. 选择 '🚀 AI System V2.0'")
    print("3. 点击 '📥 加载模型' 按钮")
    print("4. 文件选择对话框会自动打开到 'ai_models' 目录")
    print("5. 选择以下任一测试模型:")
    print("   - test_external_model.pkl (标准模型)")
    print("   - test_optimized_model.pkl (优化模型)")
    print("6. 在加载对话框中:")
    print("   - 查看模型预览信息")
    print("   - 修改模型名称(可选)")
    print("   - 点击 '✅ 确定加载' 按钮")
    print("7. 模型加载成功后会自动选中")
    print("8. 现在可以使用新模型进行分析")
    
    print(f"\n🔧 修复的问题:")
    print("✅ 添加了明确的 '✅ 确定加载' 按钮")
    print("✅ 文件选择对话框默认打开到 'ai_models' 目录")
    print("✅ 改善了按钮布局和可见性")
    print("✅ 增加了详细的加载状态反馈")
    print("✅ 支持回车键快速确认")
    
    print(f"\n💡 测试建议:")
    print("- 尝试加载不同类型的模型")
    print("- 观察模型预览信息的准确性")
    print("- 验证加载后的自动选择功能")
    print("- 测试加载的模型是否能正常预测")

def cleanup_test_files():
    """清理测试文件"""
    print(f"\n🧹 清理测试文件")
    print("=" * 60)
    
    try:
        test_files = [
            "ai_models/test_external_model.pkl",
            "ai_models/test_optimized_model.pkl"
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ 已删除: {file_path}")
        
        # 可选：清理模型注册表
        # registry_file = "ai_models/models_registry.json"
        # if os.path.exists(registry_file):
        #     os.remove(registry_file)
        #     print(f"✅ 已清理模型注册表: {registry_file}")
        
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

def main():
    """主函数"""
    print("🔬 测试修复后的外部模型加载功能")
    print("=" * 80)
    
    try:
        # 程序化测试
        success = test_model_loading_programmatically()
        
        if success:
            print(f"\n🎉 程序化测试成功!")
            
            # 显示GUI测试说明
            show_gui_instructions()
            
            # 询问是否清理
            print(f"\n❓ 是否保留测试文件供GUI测试使用？")
            print("测试模型文件将保留在ai_models目录中")
            print("输入 'n' 清理，其他键保留文件")
            
            try:
                choice = input("请选择: ").strip().lower()
                if choice == 'n':
                    cleanup_test_files()
                else:
                    print("📁 测试文件已保留，您可以在GUI中使用")
                    print("文件位置:")
                    print("  - ai_models/test_external_model.pkl")
                    print("  - ai_models/test_optimized_model.pkl")
            except:
                print("📁 测试文件已保留")
            
        else:
            print(f"\n❌ 程序化测试失败")
            print("请检查依赖环境和代码实现")
        
        print(f"\n🎯 修复总结:")
        print("✅ 确定按钮现在清晰可见")
        print("✅ 默认目录设置为ai_models")
        print("✅ 改善了用户体验和反馈")
        print("✅ 支持键盘快捷操作")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
