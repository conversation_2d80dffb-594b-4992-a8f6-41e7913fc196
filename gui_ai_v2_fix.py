
# GUI修复脚本 - 确保AI V2.0正确工作

def fix_ai_v2_in_gui():
    """在GUI中修复AI V2.0配置"""
    try:
        # 1. 确保AI V2.0系统已选择
        if hasattr(self, 'ai_mode_var'):
            self.ai_mode_var.set("🚀 AI System V2.0")
            self.on_ai_mode_changed()
        
        # 2. 强制设置高精度模型
        if hasattr(self, 'ai_analyzer_v2'):
            models = self.ai_analyzer_v2.model_manager.get_available_models()
            for key, model_info in models.items():
                if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                    # 加载模型
                    self.ai_analyzer_v2.model_manager.load_model(key)
                    self.ai_analyzer_v2.set_model(key)
                    self.ai_analyzer_v2.set_feature_extractor('advanced')
                    
                    # 更新GUI显示
                    if hasattr(self, 'selected_model_var'):
                        display_name = f"{model_info.name} ({model_info.accuracy:.1%})"
                        self.selected_model_var.set(display_name)
                    
                    print(f"✅ GUI中已设置高精度模型: {model_info.name}")
                    break
        
        # 3. 确保使用正确的分析方法
        return True
        
    except Exception as e:
        print(f"❌ GUI修复失败: {e}")
        return False

# 在GUI的run_ai_v2_analysis方法中添加此修复
