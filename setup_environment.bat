@echo off
echo 正在设置AI桩基完整性分析系统环境...
echo.

echo 1. 激活虚拟环境...
call F:\2025\AIpile\AIpiles_final\ai_pile_env\Scripts\activate.bat

echo.
echo 2. 升级pip...
python -m pip install --upgrade pip

echo.
echo 3. 安装依赖包...
pip install -r F:\2025\AIpile\AIpiles_final\requirements.txt

echo.
echo 4. 验证安装...
python -c "import torch; print('PyTorch版本:', torch.__version__)"
python -c "import pandas; print('Pandas版本:', pandas.__version__)"
python -c "import sklearn; print('Scikit-learn版本:', sklearn.__version__)"
python -c "import matplotlib; print('Matplotlib版本:', matplotlib.__version__)"
python -c "import plotly; print('Plotly版本:', plotly.__version__)"

echo.
echo 环境设置完成！
echo.
echo 使用方法：
echo 1. 运行 activate_env.bat 激活虚拟环境
echo 2. 然后运行 python ai_pile_integrity_analyzer.py
echo.
pause
