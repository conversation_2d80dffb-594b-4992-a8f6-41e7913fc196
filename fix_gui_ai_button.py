#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复GUI中AI分析按钮的问题
使"🤖 AI Analysis"按钮能够根据选择的AI系统版本调用正确的分析器
"""

import os
import re

def fix_gui_ai_button():
    """修复GUI中的AI分析按钮"""
    print("🔧 修复GUI中的AI分析按钮...")
    
    gui_file = "Pile_analyze_GZ_gui.py"
    
    if not os.path.exists(gui_file):
        print(f"❌ 文件不存在: {gui_file}")
        return False
    
    try:
        # 读取文件内容
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ 文件读取成功")
        
        # 查找run_ai_analysis方法的位置
        method_pattern = r'def run_ai_analysis\(self\):(.*?)(?=\n    def |\n\n    def |\nclass |\Z)'
        match = re.search(method_pattern, content, re.DOTALL)
        
        if not match:
            print("❌ 未找到run_ai_analysis方法")
            return False
        
        print("✅ 找到run_ai_analysis方法")
        
        # 新的方法实现
        new_method = '''def run_ai_analysis(self):
        """Run AI analysis using selected AI system (V1.0 or V2.0)"""
        print("🤖 Starting AI analysis...")

        if self.data_df is None or self.data_df.empty:
            print("❌ No data loaded")
            messagebox.showwarning("Warning", "Please load data first")
            return

        print(f"📊 Data shape: {self.data_df.shape}")
        print(f"📋 Data columns: {list(self.data_df.columns)}")

        try:
            self.main_status_var.set("Running AI analysis...")
            self.root.update()

            # Check which AI system is selected
            ai_system_version = self.ai_system_var.get()
            print(f"🔍 Selected AI system: {ai_system_version}")

            if ai_system_version == "v2" and self.use_v2_analyzer and self.ai_analyzer_v2:
                # Use AI System V2.0
                print("🚀 Using AI System V2.0")
                result = self.run_ai_v2_analysis_internal()
            else:
                # Use AI System V1.0 (legacy)
                print("🔧 Using AI System V1.0")
                result = self.run_ai_v1_analysis_internal()

            if result is None:
                print("❌ AI prediction failed")
                messagebox.showerror("Error", "AI prediction failed")
                return

            print(f"✅ AI analysis result: {result.get('完整性类别', 'N/A')}")
            print(f"🎯 AI confidence: {result.get('ai_confidence', 0.0):.2%}")

            self.analysis_results['ai'] = result

            # Display results
            print("📝 Displaying AI results...")
            self.display_ai_result(result)

            self.main_status_var.set("AI analysis completed")
            print("🎉 AI analysis completed successfully")

        except Exception as e:
            print(f"❌ AI analysis error: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("Error", f"AI analysis failed: {str(e)}")
            self.main_status_var.set("AI analysis failed")

    def run_ai_v1_analysis_internal(self):
        """Internal method for AI System V1.0 analysis"""
        print("🔧 Running AI System V1.0 analysis...")
        
        # Check if external model is selected and load it
        model_path = self.config_vars['ai_model_path'].get()
        if model_path and os.path.exists(model_path):
            print(f"📥 Loading external AI model: {model_path}")
            try:
                self.ai_analyzer.load_models(model_path)
                self.analysis_model_status_var.set(f"✅ External model loaded: {os.path.basename(model_path)}")
                print("✅ External AI model loaded successfully")
            except Exception as e:
                print(f"⚠️ Failed to load external model, using built-in model: {str(e)}")
                self.analysis_model_status_var.set("⚠️ Using built-in AI model")
        else:
            print("🔧 Using built-in AI model")
            self.analysis_model_status_var.set("🔧 Using built-in AI model")

        # Extract features from data
        print("🔍 Extracting features...")
        features, feature_names = self.ai_analyzer.extract_features(self.data_df)

        if features.size == 0:
            print("❌ No features extracted")
            return None

        print(f"✅ Extracted {features.shape[1]} features")

        # Run AI prediction
        print("🔍 Running AI prediction...")
        result = self.ai_analyzer.predict(features)
        
        return result

    def run_ai_v2_analysis_internal(self):
        """Internal method for AI System V2.0 analysis"""
        print("🚀 Running AI System V2.0 analysis...")
        
        # Ensure high-precision model is selected if available
        try:
            models = self.ai_analyzer_v2.model_manager.get_available_models()
            current_model_key = self.ai_analyzer_v2.model_manager.current_model_key
            
            # If no model is selected, try to select the best available model
            if not current_model_key:
                print("🔍 No model selected, selecting best available model...")
                best_model_key = None
                best_accuracy = 0.0
                
                for key, model_info in models.items():
                    if model_info.accuracy > best_accuracy:
                        best_accuracy = model_info.accuracy
                        best_model_key = key
                
                if best_model_key:
                    print(f"🎯 Auto-selecting best model: {models[best_model_key].name} ({best_accuracy:.1%})")
                    self.ai_analyzer_v2.model_manager.load_model(best_model_key)
                    self.ai_analyzer_v2.set_model(best_model_key)
                    
                    # Also set advanced feature extractor if available
                    try:
                        self.ai_analyzer_v2.set_feature_extractor('advanced')
                        print("✅ Set to advanced feature extractor")
                    except:
                        print("⚠️ Advanced feature extractor not available, using current")
            
            print(f"🤖 Using model: {self.ai_analyzer_v2.model_manager.current_model_key}")
            print(f"🔍 Using feature extractor: {type(self.ai_analyzer_v2.feature_manager.current_extractor).__name__}")
            
        except Exception as e:
            print(f"⚠️ Model selection warning: {e}")
        
        # Run AI V2.0 prediction
        print("🔍 Running AI V2.0 prediction...")
        result = self.ai_analyzer_v2.predict(self.data_df)
        
        return result'''
        
        # 替换方法
        new_content = re.sub(method_pattern, new_method, content, flags=re.DOTALL)
        
        # 备份原文件
        backup_file = gui_file + ".backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 原文件已备份到: {backup_file}")
        
        # 写入修改后的内容
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ GUI文件修改成功!")
        print("\n🎯 修改内容:")
        print("1. run_ai_analysis方法现在会检查选择的AI系统版本")
        print("2. 如果选择V2.0，会调用AI System V2.0")
        print("3. 如果选择V1.0，会调用传统AI系统")
        print("4. 添加了自动选择最佳模型的功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 修复GUI中AI分析按钮问题")
    print("=" * 60)
    
    success = fix_gui_ai_button()
    
    if success:
        print("\n🎉 修复完成!")
        print("\n📋 使用说明:")
        print("1. 重新启动GUI程序")
        print("2. 在'AI System Selection'中选择'🚀 AI System V2.0'")
        print("3. 在模型下拉菜单中选择'高精度AI模型 v1.0 (94.0%)'")
        print("4. 点击'🤖 AI Analysis'按钮")
        print("5. 现在应该会使用AI System V2.0进行分析")
        
        print("\n⚠️ 注意:")
        print("- 如果仍有问题，请检查控制台输出")
        print("- 原文件已备份为 Pile_analyze_GZ_gui.py.backup")
    else:
        print("\n❌ 修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
