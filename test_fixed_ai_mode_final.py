#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的AI模式 - 最终版本
"""

import os
import sys
import pandas as pd
import numpy as np

# 强制清除模块缓存
modules_to_clear = [k for k in sys.modules.keys() if 'Pile_analyze_GZ_gui' in k]
for module in modules_to_clear:
    del sys.modules[module]

def test_ai_mode_with_real_data():
    """测试使用真实数据的AI模式"""
    print("🧪 测试修复后的AI模式 - 使用真实训练数据")
    print("=" * 80)
    
    try:
        from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
        
        # 创建AI分析器
        analyzer = BuiltInAIAnalyzer()
        print("✅ AI分析器创建成功")
        
        # 加载外部模型
        model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
        
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return
        
        print(f"📥 加载外部模型: {model_path}")
        success = analyzer.load_models(model_path)
        
        if not success:
            print("❌ 外部模型加载失败")
            return
        
        print("✅ 外部模型加载成功")
        
        # 测试样本
        test_cases = [
            ("F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt", "I类桩", 0),
            ("F:/2025/AIpile/AIpiles_final/training_data/II/KBZ1-67.txt", "II类桩", 1),
            ("F:/2025/AIpile/AIpiles_final/training_data/III/1-2.txt", "III类桩", 2),
            ("F:/2025/AIpile/AIpiles_final/training_data/IV/KBZ1-40.txt", "IV类桩", 3),
        ]
        
        print("\n🎯 测试AI预测功能:")
        print("-" * 60)
        
        correct_predictions = 0
        total_predictions = 0
        
        for file_path, expected_class, expected_num in test_cases:
            if not os.path.exists(file_path):
                print(f"⚠️ 文件不存在: {file_path}")
                continue
            
            filename = os.path.basename(file_path)
            
            try:
                # 读取数据
                df = pd.read_csv(file_path, sep='\t')
                
                # 提取特征
                features, feature_names = analyzer.extract_features(df)
                
                if features.size == 0:
                    print(f"❌ {filename}: 特征提取失败")
                    continue
                
                # 进行预测
                result = analyzer.predict(features)
                
                if result is None:
                    print(f"❌ {filename}: 预测失败")
                    continue
                
                # 获取预测结果
                predicted_category_num = result['完整性类别']
                ai_confidence = result['ai_confidence']
                anomaly_score = result['anomaly_score']
                class_probabilities = result['class_probabilities']
                overall_reasoning = result['overall_reasoning']
                
                # 转换数字类别为中文名称
                category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                predicted_category = category_mapping.get(int(predicted_category_num), f'未知类别({predicted_category_num})')
                
                # 检查预测是否正确
                is_correct = predicted_category_num == expected_num
                if is_correct:
                    correct_predictions += 1
                total_predictions += 1
                
                status = "✅" if is_correct else "❌"
                
                print(f"\n{status} 文件: {filename}")
                print(f"   期望类别: {expected_class} ({expected_num})")
                print(f"   预测类别: {predicted_category} ({predicted_category_num})")
                print(f"   AI置信度: {ai_confidence:.2%}")
                print(f"   异常分数: {anomaly_score:.3f}")
                
                # 显示类别概率
                print(f"   类别概率:")
                for class_key, prob in class_probabilities.items():
                    class_name = category_mapping.get(class_key, f'类别{class_key}')
                    print(f"     {class_name}: {prob:.2%}")
                
                # 检查推理结果
                if predicted_category in overall_reasoning:
                    print(f"   ✅ 推理包含中文类别名称")
                else:
                    print(f"   ❌ 推理不包含中文类别名称")
                
            except Exception as e:
                print(f"❌ {filename}: 处理失败 - {e}")
        
        # 计算准确率
        if total_predictions > 0:
            accuracy = correct_predictions / total_predictions
            print(f"\n📊 总体预测准确率: {correct_predictions}/{total_predictions} = {accuracy:.2%}")
            
            if accuracy >= 0.75:
                print("🎉 AI模式性能良好！")
            elif accuracy >= 0.5:
                print("⚠️ AI模式性能一般，但可用")
            else:
                print("❌ AI模式性能较差，需要改进")
        
        return accuracy if total_predictions > 0 else 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 0

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成")
    print("=" * 80)
    
    try:
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        # 检查GUI类是否可以正常导入
        print("✅ GUI类导入成功")
        
        # 检查AI相关方法
        ai_methods = [
            'run_ai_analysis',
            'display_ai_result',
            'create_comparison_text'
        ]
        
        for method in ai_methods:
            if hasattr(PileAnalyzerGZGUI, method):
                print(f"✅ GUI方法存在: {method}")
            else:
                print(f"❌ GUI方法缺失: {method}")
        
        print("✅ GUI集成检查完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成检查失败: {e}")
        return False

def test_script_compatibility():
    """测试脚本兼容性"""
    print("\n🔧 测试脚本兼容性")
    print("=" * 80)
    
    try:
        from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
        
        # 创建分析器
        analyzer = BuiltInAIAnalyzer()
        
        # 检查必要的方法
        required_methods = [
            'extract_features',
            'predict',
            'load_models',
            'save_models',
            '_generate_reasoning'
        ]
        
        for method in required_methods:
            if hasattr(analyzer, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
        
        # 检查属性
        required_attributes = [
            'classifier_model',
            'anomaly_detector',
            'scaler',
            'feature_importance'
        ]
        
        for attr in required_attributes:
            if hasattr(analyzer, attr):
                print(f"✅ 属性存在: {attr}")
            else:
                print(f"❌ 属性缺失: {attr}")
        
        print("✅ 脚本兼容性检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 脚本兼容性检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔬 AI模式修复后的最终测试")
    print("=" * 100)
    
    # 1. 测试AI模式功能
    accuracy = test_ai_mode_with_real_data()
    
    # 2. 测试GUI集成
    gui_ok = test_gui_integration()
    
    # 3. 测试脚本兼容性
    script_ok = test_script_compatibility()
    
    # 总结
    print("\n📋 最终测试总结")
    print("=" * 80)
    print(f"✅ AI模式预测准确率: {accuracy:.2%}")
    print(f"✅ GUI集成: {'正常' if gui_ok else '异常'}")
    print(f"✅ 脚本兼容性: {'正常' if script_ok else '异常'}")
    
    if accuracy >= 0.5 and gui_ok and script_ok:
        print("\n🎉 AI模式修复成功！")
        print("\n📋 修复成果:")
        print("✅ 使用真实训练数据初始化缺失组件")
        print("✅ 数字类别正确转换为中文名称")
        print("✅ AI分析结论包含中文类别名称")
        print("✅ 预测准确率达到可用水平")
        print("✅ GUI集成正常")
        print("✅ 脚本兼容性良好")
        
        print("\n🚀 现在可以正常使用AI模式进行桩身完整性分析！")
    else:
        print("\n⚠️ AI模式仍存在一些问题，但基本功能可用")
        
        if accuracy < 0.5:
            print("  - 预测准确率较低，建议收集更多训练数据")
        if not gui_ok:
            print("  - GUI集成存在问题")
        if not script_ok:
            print("  - 脚本兼容性存在问题")

if __name__ == "__main__":
    main()
