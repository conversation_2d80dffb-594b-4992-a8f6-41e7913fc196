#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于GZ传统方法的优化合成训练数据生成器
根据传统桩身完整性判定方法生成符合物理规律的合成数据
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional


class GZBasedSyntheticDataGenerator:
    """基于GZ方法的合成数据生成器"""

    def __init__(self):
        """初始化生成器"""
        # GZ方法的默认配置参数
        self.gz_config = {
            'Sp_conditions': {  # 相对波速百分比条件
                'ge_100': lambda sp: sp >= 100,        # ≥100%
                '85_lt_100': lambda sp: 85 <= sp < 100, # 85%≤sp<100%
                '75_lt_85': lambda sp: 75 <= sp < 85,   # 75%≤sp<85%
                '65_lt_75': lambda sp: 65 <= sp < 75,   # 65%≤sp<75%
                'lt_65': lambda sp: sp < 65,            # <65%
            },
            'Ad_conditions': {  # 波幅差值条件 (dB)
                'le_0': lambda ad: ad <= 0,             # ≤0dB
                'gt_0_le_4': lambda ad: 0 < ad <= 4,    # 0<ad≤4dB
                'gt_4_le_8': lambda ad: 4 < ad <= 8,    # 4<ad≤8dB
                'gt_8_le_12': lambda ad: 8 < ad <= 12,  # 8<ad≤12dB
                'gt_12': lambda ad: ad > 12,            # >12dB
            },
            'Bi_ratio_conditions': {  # 波形质量比值条件
                'gt_08': lambda br: br > 0.8,           # >0.8
                'gt_05_le_08': lambda br: 0.5 < br <= 0.8, # 0.5<br≤0.8
                'gt_025_le_05': lambda br: 0.25 < br <= 0.5, # 0.25<br≤0.5
                'le_025': lambda br: br <= 0.25,        # ≤0.25
            }
        }

        # 各类桩的特征参数范围（基于GZ方法的判定标准优化）
        self.pile_class_params = {
            'I类桩': {
                'sp_range': (100, 110),     # 相对波速范围：≥100%为最佳
                'ad_range': (-2, 0),        # 波幅差值范围：≤0dB为最佳
                'bi_ratio_range': (0.85, 1.0),  # 波形质量比值范围：>0.8为最佳
                'defect_probability': 0.02,  # 缺陷出现概率：极低
                'depth_range': (10, 30),    # 深度范围
                'description': '完整桩，无缺陷'
            },
            'II类桩': {
                'sp_range': (85, 100),      # 85%≤sp<100%
                'ad_range': (0, 4),         # 0<ad≤4dB
                'bi_ratio_range': (0.5, 0.8),  # 0.5<br≤0.8
                'defect_probability': 0.1,
                'depth_range': (10, 30),
                'description': '轻微缺陷桩'
            },
            'III类桩': {
                'sp_range': (75, 85),       # 75%≤sp<85%
                'ad_range': (4, 8),         # 4<ad≤8dB
                'bi_ratio_range': (0.25, 0.5),  # 0.25<br≤0.5
                'defect_probability': 0.25,
                'depth_range': (10, 30),
                'description': '明显缺陷桩'
            },
            'IV类桩': {
                'sp_range': (50, 75),       # sp<75%（包含<65%的严重情况）
                'ad_range': (8, 15),        # ad>8dB（包含>12dB的严重情况）
                'bi_ratio_range': (0.05, 0.25),  # br≤0.25
                'defect_probability': 0.6,
                'depth_range': (10, 30),
                'description': '严重缺陷桩'
            }
        }

    def calculate_I_ji(self, Sp: float, Ad: float, Bi_ratio: float) -> int:
        """根据GZ方法计算I(j,i)值"""
        # I(j,i) = 1 (完整性最好)
        if Bi_ratio > 0.8:
            if (Sp >= 100 and Ad <= 0) or \
               (85 <= Sp < 100 and Ad <= 0) or \
               (Sp >= 100 and 0 < Ad <= 4):
                return 1

        # I(j,i) = 2 (轻微缺陷)
        if (0.5 < Bi_ratio <= 0.8 and 85 <= Sp < 100 and 0 < Ad <= 4) or \
           (Bi_ratio > 0.5 and 75 <= Sp < 85 and Ad <= 4) or \
           (Bi_ratio > 0.5 and Sp >= 85 and 4 < Ad <= 8):
            return 2

        # I(j,i) = 3 (明显缺陷)
        if (0.25 < Bi_ratio <= 0.5 and 75 <= Sp < 85 and 4 < Ad <= 8) or \
           (Bi_ratio > 0.25 and 65 <= Sp < 75 and Ad <= 8) or \
           (Bi_ratio > 0.25 and Sp >= 75 and 8 < Ad <= 12):
            return 3

        # I(j,i) = 4 (严重缺陷)
        if Bi_ratio <= 0.25:
            if (65 <= Sp < 75 and 8 < Ad <= 12) or \
               (Sp < 65 and Ad <= 12) or \
               (Sp >= 65 and Ad > 12):
                return 4

        # 其他情况默认返回4（严重缺陷）
        return 4

    def calculate_K_i(self, I_ji_values_at_depth: List[int]) -> int:
        """计算某深度的K(i)值"""
        if not I_ji_values_at_depth:
            return 0

        valid_I_ji = [i_val for i_val in I_ji_values_at_depth if i_val in [1, 2, 3, 4]]
        if not valid_I_ji:
            return 0

        sum_I_ji_sq = sum(i_val**2 for i_val in valid_I_ji)  # ∑I(j,i)²
        sum_I_ji = sum(valid_I_ji)                           # ∑I(j,i)

        if sum_I_ji == 0:
            return 0

        # K(i) = [∑I(j,i)² / ∑I(j,i)] + 0.5，然后取整
        K_i_float = (sum_I_ji_sq / sum_I_ji) + 0.5
        return int(K_i_float)

    def determine_final_category(self, K_values_list: List[int]) -> str:
        """根据K值分布确定最终桩身完整性类别"""
        has_K4 = any(k == 4 for k in K_values_list)
        has_K3 = any(k == 3 for k in K_values_list)
        has_K2 = any(k == 2 for k in K_values_list)

        # IV类桩判定
        if has_K4:
            return "IV类桩"

        # 检查连续K=3的情况（简化版本）
        consecutive_K3_count = 0
        max_consecutive_K3 = 0
        for k in K_values_list:
            if k == 3:
                consecutive_K3_count += 1
                max_consecutive_K3 = max(max_consecutive_K3, consecutive_K3_count)
            else:
                consecutive_K3_count = 0

        if max_consecutive_K3 >= 6:  # 连续6个K=3
            return "IV类桩"

        # III类桩判定
        if has_K3:
            return "III类桩"

        # 检查连续K=2的情况
        consecutive_K2_count = 0
        max_consecutive_K2 = 0
        for k in K_values_list:
            if k == 2:
                consecutive_K2_count += 1
                max_consecutive_K2 = max(max_consecutive_K2, consecutive_K2_count)
            else:
                consecutive_K2_count = 0

        if max_consecutive_K2 >= 6 and not has_K3 and not has_K4:
            return "III类桩"

        # II类桩判定
        if has_K2 and not has_K3 and not has_K4:
            return "II类桩"

        # I类桩判定
        if all(k == 1 for k in K_values_list):
            return "I类桩"

        return "未定类别"

    def generate_defect_pattern(self, pile_class: str, depth: float, num_profiles: int = 3) -> Tuple[List[float], List[float], List[float]]:
        """为指定桩类和深度生成缺陷模式"""
        params = self.pile_class_params[pile_class]

        # 基础参数范围
        sp_min, sp_max = params['sp_range']
        ad_min, ad_max = params['ad_range']
        bi_min, bi_max = params['bi_ratio_range']
        defect_prob = params['defect_probability']

        sp_values = []
        ad_values = []
        bi_ratios = []

        for profile_idx in range(num_profiles):
            # 判断是否在此剖面引入缺陷
            has_defect = np.random.random() < defect_prob

            if has_defect:
                # 引入缺陷：参数向更严重的类别偏移
                if pile_class == 'I类桩':
                    # I类桩偶尔出现轻微异常，向II类桩参数偏移
                    sp = np.random.uniform(85, 95)  # 降到II类桩范围
                    ad = np.random.uniform(1, 3)    # 增到II类桩范围
                    bi = np.random.uniform(0.6, 0.8)  # 降到II类桩范围
                elif pile_class == 'II类桩':
                    # II类桩出现中等缺陷，向III类桩参数偏移
                    sp = np.random.uniform(75, 85)
                    ad = np.random.uniform(5, 7)
                    bi = np.random.uniform(0.3, 0.5)
                elif pile_class == 'III类桩':
                    # III类桩出现明显缺陷，向IV类桩参数偏移
                    sp = np.random.uniform(60, 75)
                    ad = np.random.uniform(9, 12)
                    bi = np.random.uniform(0.1, 0.25)
                else:  # IV类桩
                    # IV类桩出现严重缺陷，参数进一步恶化
                    sp = np.random.uniform(40, 65)  # 更低的波速
                    ad = np.random.uniform(12, 20)  # 更高的波幅差
                    bi = np.random.uniform(0.05, 0.2)  # 更差的波形质量
            else:
                # 正常区域：参数在该类别的正常范围内
                sp = np.random.uniform(sp_min, sp_max)
                ad = np.random.uniform(ad_min, ad_max)
                bi = np.random.uniform(bi_min, bi_max)

            # 确保参数在合理范围内
            sp = np.clip(sp, 30, 150)  # 波速百分比限制
            ad = np.clip(ad, -5, 25)   # 波幅差限制
            bi = np.clip(bi, 0.05, 1.0)  # 波形质量比值限制

            sp_values.append(sp)
            ad_values.append(ad)
            bi_ratios.append(bi)

        return sp_values, ad_values, bi_ratios

    def add_depth_correlation(self, data: List[Dict], pile_class: str) -> List[Dict]:
        """添加深度相关性，模拟真实桩身缺陷的空间分布特征"""
        if not data:
            return data

        # 根据桩类确定缺陷分布模式
        if pile_class == 'I类桩':
            # I类桩：基本无缺陷，偶有随机小异常
            return data

        elif pile_class == 'II类桩':
            # II类桩：可能有1-2个局部缺陷区域
            defect_zones = np.random.choice([1, 2], p=[0.7, 0.3])

        elif pile_class == 'III类桩':
            # III类桩：可能有2-3个缺陷区域
            defect_zones = np.random.choice([2, 3], p=[0.6, 0.4])

        else:  # IV类桩
            # IV类桩：可能有多个缺陷区域或连续缺陷
            defect_zones = np.random.choice([3, 4, 5], p=[0.4, 0.4, 0.2])

        # 为每个缺陷区域分配深度范围
        total_depth = len(data)
        for zone in range(defect_zones):
            # 随机选择缺陷区域的起始位置和长度
            start_idx = np.random.randint(0, max(1, total_depth - 5))
            length = np.random.randint(3, min(8, total_depth - start_idx))

            # 在该区域内加强缺陷特征
            for i in range(start_idx, min(start_idx + length, total_depth)):
                # 对该深度的所有剖面数据进行调整
                for profile_idx in range(3):
                    # 降低波速
                    data[i][f'S{profile_idx+1}'] *= np.random.uniform(0.85, 0.95)
                    # 增加波幅差
                    data[i][f'A{profile_idx+1}'] += np.random.uniform(1, 3)

        return data

    def generate_synthetic_pile_data(self, pile_class: str, num_depths: Optional[int] = None) -> pd.DataFrame:
        """生成指定类别的合成桩身数据"""
        if pile_class not in self.pile_class_params:
            raise ValueError(f"不支持的桩类: {pile_class}")

        params = self.pile_class_params[pile_class]

        # 确定深度点数量
        if num_depths is None:
            num_depths = np.random.randint(80, 200)  # 随机生成80-200个深度点

        # 生成深度序列
        depth_min, depth_max = params['depth_range']
        depths = np.linspace(depth_min, depth_max, num_depths)

        # 初始化数据列表
        data_rows = []

        # 为每个深度生成数据
        for depth in depths:
            # 生成三个剖面的数据
            sp_values, ad_values, bi_ratios = self.generate_defect_pattern(pile_class, depth)

            # 创建数据行
            row = {
                'Depth': depth,
                'S1': sp_values[0],  # 1-2剖面相对波速
                'A1': ad_values[0],  # 1-2剖面波幅差
                'S2': sp_values[1],  # 1-3剖面相对波速
                'A2': ad_values[1],  # 1-3剖面波幅差
                'S3': sp_values[2],  # 2-3剖面相对波速
                'A3': ad_values[2],  # 2-3剖面波幅差
                'BI1': bi_ratios[0], # 1-2剖面波形质量比值（用于验证）
                'BI2': bi_ratios[1], # 1-3剖面波形质量比值（用于验证）
                'BI3': bi_ratios[2], # 2-3剖面波形质量比值（用于验证）
            }

            data_rows.append(row)

        # 添加深度相关性
        data_rows = self.add_depth_correlation(data_rows, pile_class)

        # 创建DataFrame
        df = pd.DataFrame(data_rows)

        # 验证生成的数据是否符合预期的桩类
        predicted_class = self.verify_generated_data(df)

        # 如果生成的数据不符合预期，进行微调
        if predicted_class != pile_class:
            df = self.adjust_data_to_target_class(df, pile_class, predicted_class)

        return df

    def verify_generated_data(self, df: pd.DataFrame) -> str:
        """验证生成的数据符合哪个桩类"""
        K_values = []

        for _, row in df.iterrows():
            I_ji_values = []

            # 计算每个剖面的I(j,i)值
            for profile_idx in range(3):
                sp = row[f'S{profile_idx+1}']
                ad = row[f'A{profile_idx+1}']

                # 使用实际的bi_ratio值（如果存在）
                if f'BI{profile_idx+1}' in row:
                    bi_ratio = row[f'BI{profile_idx+1}']
                else:
                    bi_ratio = 1.0  # 默认值

                I_ji = self.calculate_I_ji(sp, ad, bi_ratio)
                I_ji_values.append(I_ji)

            # 计算该深度的K(i)值
            K_i = self.calculate_K_i(I_ji_values)
            K_values.append(K_i)

        # 根据K值分布确定桩类
        return self.determine_final_category(K_values)

    def adjust_data_to_target_class(self, df: pd.DataFrame, target_class: str, current_class: str) -> pd.DataFrame:
        """调整数据以符合目标桩类"""
        print(f"调整数据：从 {current_class} 到 {target_class}")

        target_params = self.pile_class_params[target_class]
        sp_min, sp_max = target_params['sp_range']
        ad_min, ad_max = target_params['ad_range']

        # 调整策略
        if target_class == 'I类桩' and current_class != 'I类桩':
            # 需要改善数据质量
            df.loc[:, ['S1', 'S2', 'S3']] = df[['S1', 'S2', 'S3']].apply(
                lambda x: np.clip(x * np.random.uniform(1.05, 1.15), sp_min, sp_max)
            )
            df.loc[:, ['A1', 'A2', 'A3']] = df[['A1', 'A2', 'A3']].apply(
                lambda x: np.clip(x * np.random.uniform(0.5, 0.8), ad_min, ad_max)
            )

        elif target_class == 'IV类桩' and current_class != 'IV类桩':
            # 需要恶化数据质量
            df.loc[:, ['S1', 'S2', 'S3']] = df[['S1', 'S2', 'S3']].apply(
                lambda x: np.clip(x * np.random.uniform(0.7, 0.9), sp_min, sp_max)
            )
            df.loc[:, ['A1', 'A2', 'A3']] = df[['A1', 'A2', 'A3']].apply(
                lambda x: np.clip(x * np.random.uniform(1.5, 2.0), ad_min, ad_max)
            )

        return df


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='基于GZ方法生成优化的合成桩基数据文件')
    parser.add_argument('--samples', type=int, default=50, help='每个桩类生成的样本数量 (默认: 50)')
    parser.add_argument('--class_type', type=str, choices=['I', 'II', 'III', 'IV', 'all'], default='all',
                        help='要生成的桩类 (I, II, III, IV 或 all 表示全部)')
    parser.add_argument('--output_dir', type=str, default='training_data',
                        help='输出目录 (默认: training_data)')
    parser.add_argument('--depths', type=int, default=None,
                        help='每个样本的深度点数量 (默认: 随机80-200)')
    parser.add_argument('--verify', action='store_true',
                        help='验证生成的数据是否符合预期桩类')

    # 解析命令行参数
    args = parser.parse_args()

    # 初始化生成器
    generator = GZBasedSyntheticDataGenerator()

    # 获取训练数据目录
    training_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), args.output_dir)

    # 确保目录存在
    os.makedirs(training_dir, exist_ok=True)

    # 桩类目录映射
    class_dirs = {
        'I类桩': os.path.join(training_dir, 'I'),
        'II类桩': os.path.join(training_dir, 'II'),
        'III类桩': os.path.join(training_dir, 'III'),
        'IV类桩': os.path.join(training_dir, 'IV')
    }

    # 确保各类别目录存在
    for class_dir in class_dirs.values():
        os.makedirs(class_dir, exist_ok=True)

    # 确定要生成的桩类
    if args.class_type == 'all':
        pile_classes = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
    else:
        pile_classes = [f'{args.class_type}类桩']

    # 生成合成数据
    print(f"🚀 开始生成基于GZ方法的优化合成数据，每类 {args.samples} 个样本...")
    print(f"📁 输出目录: {training_dir}")

    total_generated = 0
    total_verified = 0

    for pile_class in pile_classes:
        print(f"\n📊 正在为 {pile_class} 生成数据...")

        # 获取目标目录
        target_dir = class_dirs[pile_class]

        class_generated = 0
        class_verified = 0

        for i in range(args.samples):
            try:
                # 生成文件名
                file_name = f"gz_synthetic_{pile_class}_{i+1:03d}.txt"
                file_path = os.path.join(target_dir, file_name)

                # 生成合成数据
                df = generator.generate_synthetic_pile_data(pile_class, args.depths)

                # 验证数据质量
                if args.verify:
                    predicted_class = generator.verify_generated_data(df)
                    if predicted_class == pile_class:
                        class_verified += 1
                        total_verified += 1
                    else:
                        print(f"⚠️  样本 {i+1} 预测为 {predicted_class}，目标为 {pile_class}")

                # 保存到文件
                # 使用标准格式：Depth(m), 1-2 Speed%, 1-2 Amp%, 1-3 Speed%, 1-3 Amp%, 2-3 Speed%, 2-3 Amp%
                # 只保存前7列（排除BI列）
                df_output = df[['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']].copy()
                df_output.columns = ['Depth(m)', '1-2 Speed%', '1-2 Amp%', '1-3 Speed%', '1-3 Amp%', '2-3 Speed%', '2-3 Amp%']

                # 保存为制表符分隔的文本文件
                df_output.to_csv(file_path, sep='\t', index=False, float_format='%.2f')

                class_generated += 1
                total_generated += 1

                if (i + 1) % 10 == 0:
                    print(f"  ✅ 已生成 {i + 1}/{args.samples} 个样本")

            except Exception as e:
                print(f"❌ 生成文件 {file_name} 时出错: {str(e)}")
                import traceback
                traceback.print_exc()

        print(f"✅ {pile_class} 完成: 生成 {class_generated} 个样本")
        if args.verify:
            accuracy = (class_verified / class_generated * 100) if class_generated > 0 else 0
            print(f"📈 验证准确率: {accuracy:.1f}% ({class_verified}/{class_generated})")

    print(f"\n🎉 合成数据生成完成!")
    print(f"📊 总计生成: {total_generated} 个样本")
    if args.verify:
        overall_accuracy = (total_verified / total_generated * 100) if total_generated > 0 else 0
        print(f"📈 总体验证准确率: {overall_accuracy:.1f}% ({total_verified}/{total_generated})")

    print(f"\n💡 生成的数据特点:")
    print(f"   • 基于GZ传统方法的物理原理")
    print(f"   • 符合I(j,i)和K(i)值计算规则")
    print(f"   • 包含真实的缺陷空间分布模式")
    print(f"   • 自动验证和调整以确保数据质量")


if __name__ == "__main__":
    main()