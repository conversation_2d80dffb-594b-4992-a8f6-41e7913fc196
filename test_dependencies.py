#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试依赖安装是否成功
"""

def test_dependencies():
    """测试所有依赖是否正确安装"""
    try:
        print("正在测试依赖安装...")
        
        # 测试基础库
        import pandas as pd
        print(f"✓ Pandas版本: {pd.__version__}")
        
        import numpy as np
        print(f"✓ NumPy版本: {np.__version__}")
        
        import sklearn
        print(f"✓ Scikit-learn版本: {sklearn.__version__}")
        
        # 测试可视化库
        import matplotlib
        print(f"✓ Matplotlib版本: {matplotlib.__version__}")
        
        import plotly
        print(f"✓ Plotly版本: {plotly.__version__}")
        
        import seaborn
        print(f"✓ Seaborn版本: {seaborn.__version__}")
        
        # 测试深度学习库
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        
        import torchvision
        print(f"✓ TorchVision版本: {torchvision.__version__}")
        
        # 测试可解释性AI库
        import shap
        print(f"✓ SHAP版本: {shap.__version__}")
        
        # 测试统计库
        import scipy
        print(f"✓ SciPy版本: {scipy.__version__}")
        
        print("\n所有依赖安装成功！✓")
        return True
        
    except ImportError as e:
        print(f"✗ 依赖安装失败: {e}")
        return False

if __name__ == "__main__":
    test_dependencies()
