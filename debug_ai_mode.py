#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试AI模式的加载和预测问题
"""

import os
import pandas as pd
import numpy as np
from Pile_analyze_GZ_gui import BuiltInAIAnalyzer, PileAnalyzerGZGUI

def test_ai_analyzer_directly():
    """直接测试AI分析器"""
    print("🔍 直接测试AI分析器")
    print("=" * 60)
    
    # 创建AI分析器
    analyzer = BuiltInAIAnalyzer()
    
    # 测试文件
    test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    try:
        # 读取数据
        df = pd.read_csv(test_file, sep='\t')
        print(f"✅ 成功读取数据: {df.shape}")
        print(f"📊 列名: {list(df.columns)}")
        
        # 提取特征
        features, feature_names = analyzer.extract_features(df)
        print(f"✅ 特征提取成功: {features.shape}")
        print(f"📈 特征数量: {len(feature_names)}")
        
        # 进行预测
        result = analyzer.predict(features)
        
        if result:
            print(f"✅ 预测成功:")
            print(f"  完整性类别: {result['完整性类别']}")
            print(f"  置信度: {result['ai_confidence']:.3f}")
            print(f"  异常分数: {result['anomaly_score']:.3f}")
            print(f"  是否异常: {result['is_anomaly']}")
            print(f"  类别概率:")
            for cls, prob in result['class_probabilities'].items():
                print(f"    {cls}: {prob:.3f}")
        else:
            print("❌ 预测失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_external_model_loading():
    """测试外部模型加载"""
    print("\n🔍 测试外部模型加载")
    print("=" * 60)
    
    analyzer = BuiltInAIAnalyzer()
    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"📥 尝试加载模型: {model_path}")
    
    try:
        success = analyzer.load_models(model_path)
        
        if success:
            print("✅ 外部模型加载成功")
            
            # 测试预测
            test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"
            if os.path.exists(test_file):
                df = pd.read_csv(test_file, sep='\t')
                features, _ = analyzer.extract_features(df)
                result = analyzer.predict(features)
                
                if result:
                    print(f"✅ 使用外部模型预测成功:")
                    print(f"  完整性类别: {result['完整性类别']}")
                    print(f"  置信度: {result['ai_confidence']:.3f}")
                else:
                    print("❌ 使用外部模型预测失败")
        else:
            print("❌ 外部模型加载失败")
            
    except Exception as e:
        print(f"❌ 外部模型加载测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_gui_ai_integration():
    """测试GUI中的AI集成"""
    print("\n🔍 测试GUI中的AI集成")
    print("=" * 60)
    
    try:
        # 模拟GUI的AI分析流程
        from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
        
        # 创建分析器（模拟GUI中的初始化）
        ai_analyzer = BuiltInAIAnalyzer()
        
        # 模拟加载外部模型
        model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
        if os.path.exists(model_path):
            print(f"📥 在GUI环境中加载模型: {model_path}")
            success = ai_analyzer.load_models(model_path)
            
            if success:
                print("✅ GUI环境中模型加载成功")
                
                # 模拟数据分析流程
                test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"
                if os.path.exists(test_file):
                    df = pd.read_csv(test_file, sep='\t')
                    print(f"✅ 数据加载成功: {df.shape}")
                    
                    # 提取特征（模拟GUI中的流程）
                    features, feature_names = ai_analyzer.extract_features(df)
                    print(f"✅ 特征提取成功: {features.shape}")
                    
                    # 进行AI预测（模拟GUI中的AI分析）
                    result = ai_analyzer.predict(features)
                    
                    if result:
                        print(f"✅ GUI环境中AI预测成功:")
                        print(f"  完整性类别: {result['完整性类别']}")
                        print(f"  置信度: {result['ai_confidence']:.3f}")
                        print(f"  推理过程:")
                        print(f"    {result['overall_reasoning']}")
                    else:
                        print("❌ GUI环境中AI预测失败")
                else:
                    print(f"❌ 测试文件不存在: {test_file}")
            else:
                print("❌ GUI环境中模型加载失败")
        else:
            print(f"❌ 模型文件不存在: {model_path}")
            
    except Exception as e:
        print(f"❌ GUI AI集成测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_model_compatibility():
    """测试模型兼容性"""
    print("\n🔍 测试模型兼容性")
    print("=" * 60)
    
    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    try:
        import pickle
        
        # 检查模型文件内容
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        print(f"✅ 模型文件读取成功")
        print(f"📊 模型类型: {type(model_data)}")
        
        if hasattr(model_data, 'predict'):
            print(f"✅ 模型具有predict方法")
            if hasattr(model_data, 'classes_'):
                print(f"📋 模型类别: {model_data.classes_}")
            if hasattr(model_data, 'n_features_in_'):
                print(f"📈 期望特征数: {model_data.n_features_in_}")
        
        # 测试特征生成
        analyzer = BuiltInAIAnalyzer()
        test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"
        
        if os.path.exists(test_file):
            df = pd.read_csv(test_file, sep='\t')
            features, _ = analyzer.extract_features(df)
            
            print(f"📊 生成的特征数: {features.shape[1]}")
            
            if hasattr(model_data, 'n_features_in_'):
                expected_features = model_data.n_features_in_
                if features.shape[1] == expected_features:
                    print(f"✅ 特征数量匹配: {features.shape[1]} == {expected_features}")
                else:
                    print(f"❌ 特征数量不匹配: {features.shape[1]} != {expected_features}")
            
            # 尝试直接预测
            try:
                prediction = model_data.predict(features)
                probabilities = model_data.predict_proba(features)
                
                print(f"✅ 直接模型预测成功:")
                print(f"  预测类别: {prediction[0]}")
                print(f"  类别概率: {probabilities[0]}")
                
            except Exception as pred_error:
                print(f"❌ 直接模型预测失败: {pred_error}")
        
    except Exception as e:
        print(f"❌ 模型兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🧪 AI模式调试测试")
    print("=" * 80)
    
    test_ai_analyzer_directly()
    test_external_model_loading()
    test_gui_ai_integration()
    test_model_compatibility()
    
    print("\n📋 调试总结:")
    print("1. 检查AI分析器的基本功能")
    print("2. 检查外部模型加载")
    print("3. 检查GUI环境中的AI集成")
    print("4. 检查模型兼容性")

if __name__ == "__main__":
    main()
