#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终测试AI修复
"""

import sys
import importlib

# 强制重新加载模块
if 'Pile_analyze_GZ_gui' in sys.modules:
    del sys.modules['Pile_analyze_GZ_gui']

from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
import pandas as pd
import os

def test_final_ai_fix():
    """最终测试AI修复"""
    print("🧪 最终测试AI修复")
    print("=" * 80)
    
    # 创建AI分析器
    analyzer = BuiltInAIAnalyzer()
    
    # 加载外部模型
    model_path = "F:/2025/AIpile/AIpiles_final/advanced_models/model_a1.pkl"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"📥 加载外部模型: {model_path}")
    success = analyzer.load_models(model_path)
    
    if not success:
        print("❌ 外部模型加载失败")
        return
    
    print("✅ 外部模型加载成功")
    
    # 测试文件
    test_file = "F:/2025/AIpile/AIpiles_final/training_data/I/3-0.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    try:
        # 读取数据
        df = pd.read_csv(test_file, sep='\t')
        print(f"✅ 数据读取成功: {df.shape}")
        
        # 提取特征
        features, feature_names = analyzer.extract_features(df)
        print(f"✅ 特征提取成功: {features.shape}")
        
        # 进行预测
        result = analyzer.predict(features)
        
        if result is None:
            print("❌ 预测失败")
            return
        
        print("✅ 预测成功")
        
        # 检查原始结果
        print(f"\n📋 原始预测结果:")
        print(f"  完整性类别 (原始): {result['完整性类别']}")
        print(f"  类型: {type(result['完整性类别'])}")
        
        # 测试类别映射
        category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
        category = result.get('完整性类别', 'N/A')
        if isinstance(category, (int, float)):
            mapped_category = category_mapping.get(int(category), f'未知类别({category})')
            print(f"  映射后类别: {mapped_category}")
        
        # 显示AI分析结论
        print(f"\n📋 AI分析结论:")
        reasoning = result.get('overall_reasoning', '无分析结论')
        print(reasoning)
        
        # 检查reasoning中是否包含中文类别名称
        if '类桩' in reasoning:
            print("✅ AI分析结论中包含中文类别名称")
        else:
            print("❌ AI分析结论中仍然显示数字类别")
        
        # 测试GUI显示逻辑
        print(f"\n🖥️ 模拟GUI显示逻辑:")
        
        # 模拟display_ai_result方法的逻辑
        res = result
        
        # Convert numeric category to Chinese name if needed
        category = res.get('完整性类别', 'N/A')
        if isinstance(category, (int, float)):
            category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
            category = category_mapping.get(int(category), f'未知类别({category})')

        print(f"桩基完整性类别: {category}")
        print(f"AI置信度: {res.get('ai_confidence', 0.0):.2%}")
        print(f"异常分数: {res.get('anomaly_score', 0.0):.2f}")
        print()
        print(f"AI分析结论: {res.get('overall_reasoning', '无分析结论')}")
        print()

        # 显示类别概率
        print("各类别概率:")
        class_probabilities = res.get('class_probabilities', {})
        category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
        
        for class_key, prob in class_probabilities.items():
            # Convert numeric keys to Chinese names
            if isinstance(class_key, (int, float)):
                class_name = category_mapping.get(int(class_key), f'类别{class_key}')
            else:
                class_name = class_key
            print(f"  {class_name}: {prob:.2%}")
        
        print("\n✅ 最终AI修复测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_ai_fix()
