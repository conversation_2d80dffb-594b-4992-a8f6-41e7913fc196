#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试AI System V2.0的高精度模型分析结果
"""

import pandas as pd
import os

def test_ai_v2_high_precision():
    """测试AI System V2.0高精度模型"""
    print("🔬 测试AI System V2.0高精度模型分析")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        # 获取AI分析器V2
        analyzer_v2 = get_ai_analyzer_v2()
        
        # 设置高精度模型
        models = analyzer_v2.model_manager.get_available_models()
        optimized_model_key = None
        
        for key, model_info in models.items():
            if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                optimized_model_key = key
                print(f"✅ 找到高精度模型: {model_info.name} (准确率: {model_info.accuracy:.1%})")
                break
        
        if optimized_model_key:
            # 设置模型和特征提取器
            analyzer_v2.set_model(optimized_model_key)
            analyzer_v2.set_feature_extractor('advanced')
            
            print(f"🎯 已设置高精度模型: {models[optimized_model_key].name}")
            print(f"🔧 已设置高精度特征提取器: 118特征")
        else:
            print("❌ 未找到94%准确率的高精度模型")
            return False
        
        # 测试III类桩文件
        test_files = [
            ("training_data/III/1-2.txt", "III类桩", 2),
            ("training_data/III/KBZ1-51.txt", "III类桩", 2)
        ]
        
        print(f"\n🧪 开始测试III类桩文件:")
        print("-" * 60)
        
        all_correct = True
        
        for file_path, expected_label, expected_class in test_files:
            print(f"\n📁 测试文件: {file_path}")
            
            if not os.path.exists(file_path):
                print(f"   ❌ 文件不存在")
                all_correct = False
                continue
            
            try:
                # 读取数据
                df = pd.read_csv(file_path, sep='\t', encoding='utf-8')
                print(f"   📊 数据形状: {df.shape}")
                
                # 进行AI分析
                result = analyzer_v2.predict(df)
                
                if result:
                    predicted_class = result.get('完整性类别', -1)
                    confidence = result.get('ai_confidence', 0.0)
                    
                    class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                    pred_name = class_names.get(predicted_class, f'未知({predicted_class})')
                    
                    print(f"   🤖 AI V2.0预测: {pred_name}")
                    print(f"   🎯 置信度: {confidence:.2%}")
                    print(f"   📋 预期结果: {expected_label}")
                    
                    if predicted_class == expected_class:
                        print(f"   ✅ 预测正确!")
                    else:
                        print(f"   ❌ 预测错误! 预期{expected_label}，实际{pred_name}")
                        all_correct = False
                    
                    # 显示详细分析
                    if 'detailed_analysis' in result:
                        print(f"   📝 详细分析: {result['detailed_analysis']}")
                        
                else:
                    print(f"   ❌ AI预测失败")
                    all_correct = False
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                all_correct = False
        
        print(f"\n📋 测试总结:")
        print("=" * 60)
        if all_correct:
            print("🎉 所有测试通过! AI System V2.0正确识别了III类桩")
            print("✅ 高精度模型工作正常")
        else:
            print("⚠️ 部分测试失败，需要检查模型配置")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ AI V2.0测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_all_methods():
    """对比所有分析方法"""
    print(f"\n🔍 对比所有分析方法")
    print("=" * 80)
    
    test_file = "training_data/III/1-2.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    df = pd.read_csv(test_file, sep='\t', encoding='utf-8')
    print(f"📁 测试文件: {test_file}")
    print(f"📊 数据形状: {df.shape}")
    print(f"🎯 预期结果: III类桩")
    
    results = {}
    
    # 1. 传统GZ方法
    try:
        print(f"\n🔧 传统GZ方法:")
        # 这里可以调用传统分析方法
        print("   结果: III类桩 (从GUI日志确认)")
        results['传统GZ'] = 'III类桩'
    except Exception as e:
        print(f"   ❌ 传统方法失败: {e}")
        results['传统GZ'] = '失败'
    
    # 2. AI System V1.0 (内置)
    try:
        print(f"\n🤖 AI System V1.0 (内置):")
        print("   结果: II类桩 (从GUI日志确认)")
        print("   置信度: 52.00%")
        results['AI V1.0'] = 'II类桩'
    except Exception as e:
        print(f"   ❌ AI V1.0失败: {e}")
        results['AI V1.0'] = '失败'
    
    # 3. AI System V2.0 (高精度)
    try:
        print(f"\n🚀 AI System V2.0 (高精度):")
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer_v2 = get_ai_analyzer_v2()
        
        # 设置高精度模型
        models = analyzer_v2.model_manager.get_available_models()
        for key, model_info in models.items():
            if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                analyzer_v2.set_model(key)
                analyzer_v2.set_feature_extractor('advanced')
                break
        
        result = analyzer_v2.predict(df)
        if result:
            predicted_class = result.get('完整性类别', -1)
            confidence = result.get('ai_confidence', 0.0)
            
            class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
            pred_name = class_names.get(predicted_class, f'未知({predicted_class})')
            
            print(f"   结果: {pred_name}")
            print(f"   置信度: {confidence:.2%}")
            results['AI V2.0'] = pred_name
        else:
            print(f"   ❌ 预测失败")
            results['AI V2.0'] = '失败'
            
    except Exception as e:
        print(f"   ❌ AI V2.0失败: {e}")
        results['AI V2.0'] = '失败'
    
    # 总结对比
    print(f"\n📊 方法对比总结:")
    print("=" * 60)
    print(f"{'方法':<15} {'预测结果':<10} {'准确性'}")
    print("-" * 40)
    
    for method, result in results.items():
        accuracy = "✅ 正确" if result == 'III类桩' else "❌ 错误" if result != '失败' else "❌ 失败"
        print(f"{method:<15} {result:<10} {accuracy}")
    
    print(f"\n💡 结论:")
    if results.get('AI V2.0') == 'III类桩':
        print("✅ AI System V2.0 (94%高精度模型) 正确识别了III类桩")
        print("✅ 问题已解决，请在GUI中使用AI System V2.0")
    else:
        print("⚠️ AI System V2.0 仍有问题，需要进一步检查")

def main():
    """主函数"""
    print("🔬 AI System V2.0 高精度模型测试")
    print("=" * 100)
    
    # 1. 测试AI V2.0高精度模型
    success = test_ai_v2_high_precision()
    
    # 2. 对比所有方法
    compare_all_methods()
    
    print(f"\n🎯 最终结论:")
    print("=" * 80)
    
    if success:
        print("🎉 AI System V2.0 高精度模型工作正常!")
        print("✅ 能够正确识别III类桩为III类桩")
        print("📋 在GUI中请确保:")
        print("   1. 选择 '🚀 AI System V2.0'")
        print("   2. 选择 '高精度AI模型 v1.0 (94.0%)'")
        print("   3. 点击 '🚀 开始AI分析' 按钮")
        print("   4. 不要使用 '🤖 AI Analysis' 按钮")
    else:
        print("⚠️ AI System V2.0 测试未完全通过")
        print("🔧 建议检查模型文件和配置")

if __name__ == "__main__":
    main()
