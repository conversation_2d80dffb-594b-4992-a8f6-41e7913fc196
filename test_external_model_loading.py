#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试外部模型加载功能
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def create_test_models():
    """创建测试模型文件"""
    print("🔧 创建测试模型文件")
    print("=" * 80)
    
    # 创建测试目录
    test_models_dir = "test_models"
    os.makedirs(test_models_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    X = np.random.randn(100, 54)  # 54个特征
    y = np.random.randint(0, 4, 100)  # 4个类别
    
    # 1. 创建单个分类器模型
    print("1. 创建单个分类器模型...")
    single_classifier = RandomForestClassifier(n_estimators=10, random_state=42)
    single_classifier.fit(X, y)
    
    single_model_path = os.path.join(test_models_dir, "single_classifier.pkl")
    with open(single_model_path, 'wb') as f:
        pickle.dump(single_classifier, f)
    print(f"✅ 单个分类器模型已保存: {single_model_path}")
    
    # 2. 创建完整模型包
    print("2. 创建完整模型包...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    complete_classifier = RandomForestClassifier(n_estimators=20, random_state=42)
    complete_classifier.fit(X_scaled, y)
    
    complete_model = {
        'classifier_model': complete_classifier,
        'scaler': scaler,
        'feature_importance': dict(zip([f'feature_{i}' for i in range(54)], 
                                     complete_classifier.feature_importances_))
    }
    
    complete_model_path = os.path.join(test_models_dir, "complete_model.pkl")
    with open(complete_model_path, 'wb') as f:
        pickle.dump(complete_model, f)
    print(f"✅ 完整模型包已保存: {complete_model_path}")
    
    # 3. 创建优化模型格式 (模拟高精度模型)
    print("3. 创建优化模型格式...")
    from sklearn.pipeline import Pipeline
    from sklearn.preprocessing import PowerTransformer
    from sklearn.feature_selection import SelectKBest, f_classif
    from sklearn.ensemble import VotingClassifier, GradientBoostingClassifier
    
    # 生成118个特征的数据
    X_enhanced = np.random.randn(100, 118)
    
    # 创建预处理器
    preprocessor = Pipeline([
        ('transformer', PowerTransformer()),
        ('selector', SelectKBest(f_classif, k=100))
    ])
    
    X_processed = preprocessor.fit_transform(X_enhanced, y)
    
    # 创建集成模型
    rf = RandomForestClassifier(n_estimators=50, random_state=42)
    gb = GradientBoostingClassifier(n_estimators=50, random_state=42)
    
    ensemble = VotingClassifier([
        ('rf', rf),
        ('gb', gb)
    ], voting='soft')
    
    ensemble.fit(X_processed, y)
    
    optimized_model = {
        'model': ensemble,
        'preprocessor': preprocessor
    }
    
    optimized_model_path = os.path.join(test_models_dir, "optimized_model.pkl")
    with open(optimized_model_path, 'wb') as f:
        pickle.dump(optimized_model, f)
    print(f"✅ 优化模型已保存: {optimized_model_path}")
    
    # 4. 创建自定义格式模型
    print("4. 创建自定义格式模型...")
    custom_model = {
        'my_classifier': single_classifier,
        'my_scaler': scaler,
        'metadata': {
            'version': '1.0',
            'created_by': 'test_script',
            'features': 54
        }
    }
    
    custom_model_path = os.path.join(test_models_dir, "custom_model.pkl")
    with open(custom_model_path, 'wb') as f:
        pickle.dump(custom_model, f)
    print(f"✅ 自定义格式模型已保存: {custom_model_path}")
    
    return [single_model_path, complete_model_path, optimized_model_path, custom_model_path]

def test_model_manager_loading():
    """测试模型管理器的加载功能"""
    print("\n🧪 测试模型管理器加载功能")
    print("=" * 80)
    
    try:
        from model_manager import get_model_manager
        
        model_manager = get_model_manager()
        
        # 创建测试模型
        test_model_paths = create_test_models()
        
        print(f"\n📥 测试外部模型加载:")
        
        for i, model_path in enumerate(test_model_paths, 1):
            model_name = f"测试模型 {i}"
            filename = os.path.basename(model_path)
            
            print(f"\n{i}. 加载 {filename}:")
            
            success = model_manager.load_external_model(model_path, model_name)
            
            if success:
                print(f"  ✅ 加载成功")
                
                # 获取模型信息
                models = model_manager.get_available_models()
                for key, model_info in models.items():
                    if model_info.name == model_name:
                        print(f"  📊 模型信息:")
                        print(f"    - 名称: {model_info.name}")
                        print(f"    - 类型: {model_info.model_type}")
                        print(f"    - 特征数: {model_info.feature_count}")
                        print(f"    - 预期准确率: {model_info.accuracy:.1%}")
                        print(f"    - 文件大小: {model_info.file_size / 1024:.1f} KB")
                        break
            else:
                print(f"  ❌ 加载失败")
        
        # 显示所有模型
        print(f"\n📋 当前所有模型:")
        all_models = model_manager.get_available_models()
        for key, model_info in all_models.items():
            print(f"  - {model_info.name} ({model_info.model_type}, {model_info.accuracy:.1%})")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_analyzer_v2_with_external_models():
    """测试AI分析器V2与外部模型的集成"""
    print("\n🤖 测试AI分析器V2与外部模型集成")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer = get_ai_analyzer_v2()
        
        # 创建测试数据
        test_data = {
            'Depth(m)': [0.5, 1.0, 1.5, 2.0, 2.5],
            '1-2 Speed%': [95, 98, 92, 88, 85],
            '1-2 Amp%': [2, 3, 5, 8, 12],
            '1-3 Speed%': [96, 97, 90, 86, 83],
            '1-3 Amp%': [1, 2, 4, 7, 10],
            '2-3 Speed%': [94, 99, 91, 87, 84],
            '2-3 Amp%': [3, 4, 6, 9, 11]
        }
        
        df = pd.DataFrame(test_data)
        print(f"📊 测试数据: {df.shape}")
        
        # 获取所有可用模型
        models = analyzer.model_manager.get_available_models()
        
        print(f"\n🎯 测试所有模型的预测:")
        
        for key, model_info in models.items():
            print(f"\n测试模型: {model_info.name}")
            
            try:
                # 设置模型
                analyzer.set_model(key)
                
                # 进行预测
                result = analyzer.predict(df)
                
                if result:
                    category = result.get('完整性类别', 'N/A')
                    confidence = result.get('ai_confidence', 0.0)
                    
                    # 转换数字类别为中文名称
                    category_mapping = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
                    if isinstance(category, (int, float, np.integer, np.floating)):
                        category_name = category_mapping.get(int(category), f'未知类别({category})')
                    else:
                        category_name = category
                    
                    print(f"  ✅ 预测成功:")
                    print(f"    - 类别: {category_name}")
                    print(f"    - 置信度: {confidence:.2%}")
                    
                    # 显示模型信息
                    model_info_result = result.get('model_info', {})
                    if model_info_result:
                        print(f"    - 模型类型: {model_info_result.get('model_type', 'N/A')}")
                        print(f"    - 特征数量: {model_info_result.get('feature_count', 'N/A')}")
                else:
                    print(f"  ❌ 预测失败")
                    
            except Exception as e:
                print(f"  ❌ 模型测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI分析器V2测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成")
    print("=" * 80)
    
    try:
        # 检查GUI是否有外部模型加载方法
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        gui_class = PileAnalyzerGZGUI
        
        required_methods = [
            'load_external_model_v2',
            '_show_model_loading_dialog',
            '_analyze_and_preview_model'
        ]
        
        print("检查GUI方法:")
        for method in required_methods:
            if hasattr(gui_class, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        print("✅ GUI集成检查完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件")
    print("=" * 80)
    
    try:
        import shutil
        
        test_models_dir = "test_models"
        if os.path.exists(test_models_dir):
            shutil.rmtree(test_models_dir)
            print(f"✅ 已删除测试目录: {test_models_dir}")
        
        # 清理模型注册表
        registry_file = "ai_models/models_registry.json"
        if os.path.exists(registry_file):
            os.remove(registry_file)
            print(f"✅ 已清理模型注册表: {registry_file}")
        
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

def main():
    """主函数"""
    print("🔬 外部模型加载功能测试")
    print("=" * 100)
    
    # 测试结果
    results = {}
    
    try:
        # 1. 测试模型管理器加载功能
        results['model_manager'] = test_model_manager_loading()
        
        # 2. 测试AI分析器V2集成
        results['ai_analyzer'] = test_ai_analyzer_v2_with_external_models()
        
        # 3. 测试GUI集成
        results['gui'] = test_gui_integration()
        
    finally:
        # 清理测试文件
        cleanup_test_files()
    
    # 总结
    print("\n📋 测试总结")
    print("=" * 80)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过 ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 外部模型加载功能测试成功！")
        print("\n🚀 新功能特性:")
        print("✅ 支持多种模型格式 (单分类器、完整包、优化模型)")
        print("✅ 智能模型分析和预览")
        print("✅ 自动兼容性检查")
        print("✅ 用户友好的加载界面")
        print("✅ 动态模型注册和管理")
        print("✅ 自动特征提取器匹配")
        
        print(f"\n📖 使用指南:")
        print("1. 启动GUI: python Pile_analyze_GZ_gui.py")
        print("2. 选择AI System V2.0")
        print("3. 点击'📥 加载模型'按钮")
        print("4. 选择模型文件并预览")
        print("5. 输入模型名称并加载")
        print("6. 享受自定义AI模型分析！")
        
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ 外部模型加载功能基本正常")
        print("⚠️ 部分功能需要进一步完善")
    else:
        print("\n❌ 外部模型加载功能存在问题")
        print("💡 建议检查依赖环境和实现代码")

if __name__ == "__main__":
    main()
