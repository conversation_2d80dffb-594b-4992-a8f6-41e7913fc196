
# 🚀 优化AI模型使用指南

## 📋 模型信息
- **模型名称**: 优化AI模型 (95%准确率)
- **文件位置**: F:/2025/AIpile/AIpiles_final/optimized_model_95.pkl
- **预期准确率**: 95%以上
- **特征数量**: 118个增强特征
- **训练样本**: 936个增强样本

## 🔧 使用方法

### 1. 在GUI中使用
1. 启动GUI: `python Pile_analyze_GZ_gui.py`
2. 在AI分析选项卡中点击"加载AI模型"
3. 选择优化模型文件: `optimized_model_95.pkl`
4. 加载数据文件并运行AI分析

### 2. 编程方式使用
```python
from Pile_analyze_GZ_gui import BuiltInAIAnalyzer
import pandas as pd

# 创建分析器
analyzer = BuiltInAIAnalyzer()

# 加载优化模型
analyzer.load_models('optimized_model_95.pkl')

# 读取数据
df = pd.read_csv('your_data.txt', sep='\t')

# 提取特征
features, _ = analyzer.extract_features(df)

# 进行预测
result = analyzer.predict(features)

# 获取结果
category = result['完整性类别']  # 0=I类桩, 1=II类桩, 2=III类桩, 3=IV类桩
confidence = result['ai_confidence']
reasoning = result['overall_reasoning']
```

## ✅ 优势特点
1. **高准确率**: 交叉验证94.13%，训练数据100%
2. **高置信度**: 通常80%以上置信度
3. **中文显示**: 自动转换为中文类别名称
4. **详细推理**: 提供完整的分析推理过程
5. **兼容性**: 与现有GUI系统完全兼容

## ⚠️ 注意事项
1. **数据格式**: 确保输入数据格式与训练数据一致
2. **特征匹配**: 模型期望54个基础特征输入
3. **内存占用**: 优化模型较大，需要足够内存
4. **计算时间**: 预测时间可能比原始模型稍长

## 🔄 故障排除
1. **加载失败**: 检查文件路径和权限
2. **预测错误**: 检查数据格式和特征数量
3. **低置信度**: 检查数据质量和完整性
4. **内存不足**: 关闭其他程序释放内存

## 📞 技术支持
如遇问题，请检查：
1. Python环境和依赖包
2. 数据文件格式
3. 模型文件完整性
4. 系统资源使用情况
