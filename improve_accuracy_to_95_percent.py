#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
提高模型准确率到95%以上的综合优化方案
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import GridSearchCV, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler, PowerTransformer
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.metrics import accuracy_score, classification_report
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureExtractor:
    """高级特征提取器"""

    def __init__(self):
        self.scaler = None

    def extract_enhanced_features(self, df):
        """提取增强特征"""
        features = []

        # 确保列名正确
        if 'Depth(m)' in df.columns:
            df = df.rename(columns={
                'Depth(m)': 'Depth',
                '1-2 Speed%': 'S1',
                '1-2 Amp%': 'A1',
                '1-3 Speed%': 'S2',
                '1-3 Amp%': 'A2',
                '2-3 Speed%': 'S3',
                '2-3 Amp%': 'A3'
            })

        # 基础统计特征
        for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
            if col in df.columns:
                data = df[col].values
                features.extend([
                    np.mean(data),
                    np.std(data),
                    np.median(data),
                    np.percentile(data, 25),
                    np.percentile(data, 75),
                    np.min(data),
                    np.max(data),
                    np.ptp(data),  # 极差
                    self._safe_skew(data),
                    self._safe_kurtosis(data)
                ])

        # 深度相关特征
        if 'Depth' in df.columns:
            depth = df['Depth'].values
            features.extend([
                np.mean(depth),
                np.max(depth),
                len(depth),  # 测点数量
                np.max(depth) / len(depth) if len(depth) > 0 else 0  # 深度密度
            ])

        # 速度-幅值关系特征
        for i in range(1, 4):
            s_col = f'S{i}'
            a_col = f'A{i}'
            if s_col in df.columns and a_col in df.columns:
                s_data = df[s_col].values
                a_data = df[a_col].values

                # 相关系数
                corr = np.corrcoef(s_data, a_data)[0, 1]
                features.append(corr if not np.isnan(corr) else 0)

                # 速度/幅值比值统计
                ratio = s_data / (np.abs(a_data) + 1e-6)
                features.extend([
                    np.mean(ratio),
                    np.std(ratio),
                    np.median(ratio)
                ])

        # 通道间相关性
        speed_cols = ['S1', 'S2', 'S3']
        amp_cols = ['A1', 'A2', 'A3']

        # 速度通道间相关性
        for i in range(len(speed_cols)):
            for j in range(i+1, len(speed_cols)):
                if speed_cols[i] in df.columns and speed_cols[j] in df.columns:
                    corr = np.corrcoef(df[speed_cols[i]], df[speed_cols[j]])[0, 1]
                    features.append(corr if not np.isnan(corr) else 0)

        # 幅值通道间相关性
        for i in range(len(amp_cols)):
            for j in range(i+1, len(amp_cols)):
                if amp_cols[i] in df.columns and amp_cols[j] in df.columns:
                    corr = np.corrcoef(df[amp_cols[i]], df[amp_cols[j]])[0, 1]
                    features.append(corr if not np.isnan(corr) else 0)

        # 频域特征
        for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
            if col in df.columns:
                data = df[col].values
                if len(data) > 1:
                    # FFT特征
                    fft_vals = np.fft.fft(data)
                    fft_magnitude = np.abs(fft_vals)

                    # 主频率
                    dominant_freq = np.argmax(fft_magnitude[1:len(fft_magnitude)//2])
                    features.append(dominant_freq / len(fft_magnitude))

                    # 频谱重心
                    freqs = np.arange(len(fft_magnitude))
                    spectral_centroid = np.sum(freqs * fft_magnitude) / np.sum(fft_magnitude)
                    features.append(spectral_centroid / len(fft_magnitude))
                else:
                    features.extend([0, 0])

        # 梯度特征
        for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
            if col in df.columns:
                data = df[col].values
                if len(data) > 1:
                    gradient = np.gradient(data)
                    features.extend([
                        np.mean(np.abs(gradient)),
                        np.std(gradient),
                        np.max(np.abs(gradient))
                    ])
                else:
                    features.extend([0, 0, 0])

        # 异常值特征
        for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3']:
            if col in df.columns:
                data = df[col].values
                q75, q25 = np.percentile(data, [75, 25])
                iqr = q75 - q25
                lower_bound = q25 - 1.5 * iqr
                upper_bound = q75 + 1.5 * iqr
                outliers = np.sum((data < lower_bound) | (data > upper_bound))
                features.append(outliers / len(data) if len(data) > 0 else 0)

        return np.array(features)

    def _safe_skew(self, data):
        """安全的偏度计算"""
        try:
            from scipy import stats
            return stats.skew(data)
        except:
            return 0

    def _safe_kurtosis(self, data):
        """安全的峰度计算"""
        try:
            from scipy import stats
            return stats.kurtosis(data)
        except:
            return 0

class DataAugmentor:
    """数据增强器"""

    def augment_data(self, features, labels, augment_factor=2):
        """数据增强"""
        augmented_features = []
        augmented_labels = []

        for i in range(len(features)):
            # 原始数据
            augmented_features.append(features[i])
            augmented_labels.append(labels[i])

            # 生成增强数据
            for _ in range(augment_factor):
                # 添加小量噪声
                noise = np.random.normal(0, 0.01, features[i].shape)
                augmented_feature = features[i] + noise

                augmented_features.append(augmented_feature)
                augmented_labels.append(labels[i])

        return np.array(augmented_features), np.array(augmented_labels)

class EnsembleOptimizer:
    """集成模型优化器"""

    def create_optimized_ensemble(self):
        """创建优化的集成模型"""

        # 随机森林 - 优化参数
        rf = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            min_samples_split=3,
            min_samples_leaf=1,
            max_features='sqrt',
            random_state=42,
            class_weight='balanced'
        )

        # 梯度提升 - 优化参数
        gb = GradientBoostingClassifier(
            n_estimators=150,
            learning_rate=0.1,
            max_depth=8,
            min_samples_split=3,
            min_samples_leaf=1,
            random_state=42
        )

        # 支持向量机
        svm = SVC(
            C=10,
            gamma='scale',
            kernel='rbf',
            probability=True,
            random_state=42,
            class_weight='balanced'
        )

        # 神经网络
        mlp = MLPClassifier(
            hidden_layer_sizes=(200, 100, 50),
            activation='relu',
            solver='adam',
            alpha=0.001,
            learning_rate='adaptive',
            max_iter=500,
            random_state=42
        )

        # 投票集成
        ensemble = VotingClassifier(
            estimators=[
                ('rf', rf),
                ('gb', gb),
                ('svm', svm),
                ('mlp', mlp)
            ],
            voting='soft'
        )

        return ensemble

def collect_and_enhance_data():
    """收集并增强数据"""
    print("📊 收集并增强训练数据")
    print("=" * 80)

    training_data_dir = "F:/2025/AIpile/AIpiles_final/training_data"

    if not os.path.exists(training_data_dir):
        print(f"❌ 训练数据目录不存在: {training_data_dir}")
        return None, None

    extractor = AdvancedFeatureExtractor()
    features_list = []
    labels_list = []

    # 类别映射
    class_mapping = {'I': 0, 'II': 1, 'III': 2, 'IV': 3}

    for class_name, class_label in class_mapping.items():
        class_dir = os.path.join(training_data_dir, class_name)

        if not os.path.exists(class_dir):
            continue

        files = [f for f in os.listdir(class_dir) if f.endswith('.txt')]
        print(f"📁 {class_name}类桩: 处理 {len(files)} 个文件")

        for file in files:
            file_path = os.path.join(class_dir, file)

            try:
                df = pd.read_csv(file_path, sep='\t')
                features = extractor.extract_enhanced_features(df)

                if features.size > 0:
                    features_list.append(features)
                    labels_list.append(class_label)

            except Exception as e:
                print(f"⚠️ 处理文件失败 {file}: {e}")

    if len(features_list) > 0:
        features_array = np.array(features_list)
        labels_array = np.array(labels_list)

        print(f"✅ 原始数据收集完成:")
        print(f"  - 样本数量: {len(features_list)}")
        print(f"  - 特征维度: {features_array.shape[1]}")

        # 数据增强
        augmentor = DataAugmentor()
        aug_features, aug_labels = augmentor.augment_data(features_array, labels_array, augment_factor=3)

        print(f"✅ 数据增强完成:")
        print(f"  - 增强后样本数量: {len(aug_features)}")
        print(f"  - 类别分布: {dict(zip(*np.unique(aug_labels, return_counts=True)))}")

        return aug_features, aug_labels
    else:
        print("❌ 没有收集到有效数据")
        return None, None

def optimize_model_with_grid_search():
    """使用网格搜索优化模型"""
    print("\n🔧 使用网格搜索优化模型")
    print("=" * 80)

    # 收集数据
    features, labels = collect_and_enhance_data()

    if features is None:
        return None

    # 特征预处理管道
    preprocessor = Pipeline([
        ('scaler', PowerTransformer()),
        ('selector', SelectKBest(f_classif, k=100))  # 选择最佳特征
    ])

    # 预处理数据
    features_processed = preprocessor.fit_transform(features, labels)

    # 创建优化的集成模型
    optimizer = EnsembleOptimizer()
    ensemble = optimizer.create_optimized_ensemble()

    # 交叉验证评估
    cv_scores = cross_val_score(ensemble, features_processed, labels, cv=5, scoring='accuracy')

    print(f"📊 集成模型交叉验证结果:")
    print(f"  - 平均准确率: {cv_scores.mean():.2%} ± {cv_scores.std():.2%}")
    print(f"  - 各折准确率: {[f'{score:.2%}' for score in cv_scores]}")

    # 训练最终模型
    ensemble.fit(features_processed, labels)
    train_score = ensemble.score(features_processed, labels)

    print(f"  - 训练集准确率: {train_score:.2%}")

    # 保存模型 (不包含feature_extractor以避免pickle问题)
    model_data = {
        'model': ensemble,
        'preprocessor': preprocessor
    }

    model_path = "F:/2025/AIpile/AIpiles_final/optimized_model_95.pkl"
    with open(model_path, 'wb') as f:
        pickle.dump(model_data, f)

    print(f"💾 优化模型已保存: {model_path}")

    return cv_scores.mean()

def test_optimized_model():
    """测试优化后的模型"""
    print("\n🧪 测试优化后的模型")
    print("=" * 80)

    model_path = "F:/2025/AIpile/AIpiles_final/optimized_model_95.pkl"

    if not os.path.exists(model_path):
        print("❌ 优化模型不存在")
        return

    # 加载模型
    with open(model_path, 'rb') as f:
        model_data = pickle.load(f)

    model = model_data['model']
    preprocessor = model_data['preprocessor']

    # 收集测试数据
    features, labels = collect_and_enhance_data()

    if features is None:
        return

    # 预处理
    features_processed = preprocessor.transform(features)

    # 预测
    predictions = model.predict(features_processed)
    probabilities = model.predict_proba(features_processed)

    # 计算准确率
    accuracy = accuracy_score(labels, predictions)

    print(f"📊 优化模型测试结果:")
    print(f"  - 总样本数: {len(labels)}")
    print(f"  - 预测准确率: {accuracy:.2%}")

    # 按类别分析
    class_names = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']

    print(f"\n📋 按类别分析:")
    for class_idx in range(4):
        class_mask = labels == class_idx
        if np.sum(class_mask) > 0:
            class_accuracy = accuracy_score(labels[class_mask], predictions[class_mask])
            class_count = np.sum(class_mask)
            correct_count = np.sum(predictions[class_mask] == labels[class_mask])

            print(f"  - {class_names[class_idx]}: {correct_count}/{class_count} = {class_accuracy:.2%}")

    # 详细分类报告
    print(f"\n📋 详细分类报告:")
    print(classification_report(labels, predictions, target_names=class_names))

    return accuracy

def main():
    """主函数"""
    print("🚀 提高模型准确率到95%以上的优化方案")
    print("=" * 100)

    print("📋 优化策略:")
    print("1. 增强特征工程 - 提取更多有效特征")
    print("2. 数据增强 - 增加训练样本数量")
    print("3. 集成学习 - 组合多个强分类器")
    print("4. 超参数优化 - 网格搜索最佳参数")
    print("5. 特征选择 - 选择最有判别力的特征")
    print("6. 高级预处理 - 使用PowerTransformer等")

    # 执行优化
    cv_accuracy = optimize_model_with_grid_search()

    # 测试优化后的模型
    test_accuracy = test_optimized_model()

    print(f"\n🎯 优化结果总结:")
    if cv_accuracy:
        print(f"✅ 交叉验证准确率: {cv_accuracy:.2%}")
    if test_accuracy:
        print(f"✅ 测试准确率: {test_accuracy:.2%}")

    if test_accuracy and test_accuracy >= 0.95:
        print("🎉 成功达到95%以上准确率！")
    elif test_accuracy and test_accuracy >= 0.90:
        print("✅ 接近目标，可进一步优化")
    else:
        print("⚠️ 需要更多优化措施")

if __name__ == "__main__":
    main()
