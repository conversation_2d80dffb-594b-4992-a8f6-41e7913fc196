
def robust_save_model(self, file_path):
    """健壮的模型保存方法"""
    try:
        print(f"🔄 开始保存模型到: {file_path}")
        
        # 1. 检查并创建目录
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            print(f"📁 创建目录: {dir_path}")
            os.makedirs(dir_path, exist_ok=True)
        
        # 2. 检查模型是否存在
        if not hasattr(self, 'training_results') or not self.training_results:
            print("❌ 没有训练结果可保存")
            return False
        
        # 3. 根据训练模式选择保存方法
        mode = self.training_results.get('mode', 'unknown')
        model_type = self.training_results.get('model_type', '')
        
        if 'enhanced' in model_type and 'trainer' in self.training_results:
            # 保存增强训练器
            print("💾 保存增强训练模型...")
            trainer = self.training_results['trainer']
            trainer.save_model(file_path)
            return True
            
        elif hasattr(self, 'training_system') and self.training_system:
            # 保存传统训练系统
            print("💾 保存传统训练模型...")
            return self.training_system.save_model(file_path)
        
        else:
            print("❌ 没有可用的训练模型")
            return False
            
    except Exception as e:
        print(f"❌ 保存模型失败: {e}")
        traceback.print_exc()
        return False
