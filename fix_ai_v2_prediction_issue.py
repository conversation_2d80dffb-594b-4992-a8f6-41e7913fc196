#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
深入修复AI V2.0预测问题
直接检查和修复GUI中的AI System V2.0配置
"""

import pandas as pd
import os
import sys

def diagnose_ai_v2_issue():
    """诊断AI V2.0系统问题"""
    print("🔍 深入诊断AI V2.0系统问题")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        # 获取AI分析器V2
        analyzer_v2 = get_ai_analyzer_v2()
        
        print("✅ AI分析器V2.0初始化成功")
        
        # 1. 检查当前模型状态
        print(f"\n📊 当前系统状态:")
        print("-" * 40)
        
        # 获取当前模型信息
        try:
            current_model_key = analyzer_v2.model_manager.current_model_key
            print(f"当前模型键: {current_model_key}")
            
            if current_model_key:
                models = analyzer_v2.model_manager.get_available_models()
                current_model = models.get(current_model_key)
                if current_model:
                    print(f"当前模型名称: {current_model.name}")
                    print(f"当前模型类型: {current_model.model_type}")
                    print(f"当前模型准确率: {current_model.accuracy:.1%}")
                    print(f"模型是否已加载: {analyzer_v2.model_manager.is_model_loaded(current_model_key)}")
        except Exception as e:
            print(f"⚠️ 获取当前模型信息失败: {e}")
        
        # 获取当前特征提取器信息
        try:
            current_extractor = analyzer_v2.feature_manager.current_extractor
            print(f"当前特征提取器: {current_extractor}")
            
            extractors = analyzer_v2.feature_manager.get_available_extractors()
            if current_extractor in extractors:
                extractor_info = extractors[current_extractor]
                print(f"特征提取器特征数: {extractor_info['feature_count']}")
        except Exception as e:
            print(f"⚠️ 获取特征提取器信息失败: {e}")
        
        # 2. 强制设置高精度配置
        print(f"\n🔧 强制设置高精度配置:")
        print("-" * 40)
        
        # 查找并设置高精度模型
        models = analyzer_v2.model_manager.get_available_models()
        optimized_model_key = None
        
        print("可用模型列表:")
        for key, model_info in models.items():
            status = "✅ 已加载" if analyzer_v2.model_manager.is_model_loaded(key) else "⚪ 未加载"
            print(f"  {key}: {model_info.name} ({model_info.accuracy:.1%}) {status}")
            
            if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                optimized_model_key = key
        
        if optimized_model_key:
            print(f"\n🎯 设置高精度模型: {optimized_model_key}")
            
            # 强制加载和设置模型
            success = analyzer_v2.model_manager.load_model(optimized_model_key)
            if success:
                analyzer_v2.set_model(optimized_model_key)
                print(f"✅ 高精度模型设置成功")
            else:
                print(f"❌ 高精度模型加载失败")
                return False
            
            # 强制设置高精度特征提取器
            analyzer_v2.set_feature_extractor('advanced')
            print(f"✅ 高精度特征提取器设置成功")
            
        else:
            print(f"❌ 未找到高精度模型")
            return False
        
        # 3. 验证配置
        print(f"\n✅ 验证最终配置:")
        print("-" * 40)
        
        final_model_key = analyzer_v2.model_manager.current_model_key
        final_extractor = analyzer_v2.feature_manager.current_extractor
        
        print(f"最终模型: {final_model_key}")
        print(f"最终特征提取器: {final_extractor}")
        
        if final_model_key == optimized_model_key and final_extractor == 'advanced':
            print(f"🎉 配置验证成功!")
            return True
        else:
            print(f"❌ 配置验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_corrected_prediction():
    """测试修正后的预测"""
    print(f"\n🧪 测试修正后的预测")
    print("=" * 80)
    
    try:
        from ai_analyzer_v2 import get_ai_analyzer_v2
        
        analyzer_v2 = get_ai_analyzer_v2()
        
        # 确保使用高精度配置
        models = analyzer_v2.model_manager.get_available_models()
        for key, model_info in models.items():
            if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                analyzer_v2.model_manager.load_model(key)
                analyzer_v2.set_model(key)
                analyzer_v2.set_feature_extractor('advanced')
                break
        
        # 测试III类桩文件
        test_file = "training_data/III/1-2.txt"
        
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        print(f"📁 测试文件: {test_file}")
        
        # 读取数据
        df = pd.read_csv(test_file, sep='\t', encoding='utf-8')
        print(f"📊 数据形状: {df.shape}")
        
        # 进行预测
        result = analyzer_v2.predict(df)
        
        if result:
            predicted_class = result.get('完整性类别', -1)
            confidence = result.get('ai_confidence', 0.0)
            
            class_names = {0: 'I类桩', 1: 'II类桩', 2: 'III类桩', 3: 'IV类桩'}
            pred_name = class_names.get(predicted_class, f'未知({predicted_class})')
            
            print(f"🤖 AI V2.0预测结果: {pred_name}")
            print(f"🎯 置信度: {confidence:.2%}")
            print(f"📋 预期结果: III类桩")
            
            if predicted_class == 2:  # III类桩
                print(f"✅ 预测正确!")
                return True
            else:
                print(f"❌ 预测仍然错误!")
                
                # 详细分析错误原因
                print(f"\n🔍 错误分析:")
                print(f"  - 使用的模型: {analyzer_v2.model_manager.current_model_key}")
                print(f"  - 使用的特征提取器: {analyzer_v2.feature_manager.current_extractor}")
                
                # 检查特征提取
                features = analyzer_v2.feature_manager.extract_features(df)
                if hasattr(features, 'shape'):
                    print(f"  - 提取的特征数: {features.shape[1]}")
                elif isinstance(features, tuple):
                    print(f"  - 特征提取返回元组: {len(features)}")
                else:
                    print(f"  - 特征提取异常: {type(features)}")
                
                return False
        else:
            print(f"❌ 预测失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_gui_ai_button_mapping():
    """检查GUI中AI按钮的映射"""
    print(f"\n🔍 检查GUI中AI按钮映射")
    print("=" * 80)
    
    try:
        # 检查GUI模块
        from Pile_analyze_GZ_gui import PileAnalyzerGZGUI
        
        print("✅ GUI模块导入成功")
        
        # 检查AI分析方法
        gui_methods = [method for method in dir(PileAnalyzerGZGUI) if 'ai' in method.lower()]
        print(f"📋 GUI中的AI相关方法:")
        for method in gui_methods:
            print(f"  - {method}")
        
        # 检查关键方法
        key_methods = [
            'run_ai_analysis',
            'run_ai_v2_analysis', 
            'switch_ai_system',
            'on_ai_mode_changed'
        ]
        
        print(f"\n🔍 关键AI方法检查:")
        for method in key_methods:
            if hasattr(PileAnalyzerGZGUI, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} (缺失)")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI检查失败: {e}")
        return False

def create_gui_fix_script():
    """创建GUI修复脚本"""
    print(f"\n🔧 创建GUI修复脚本")
    print("=" * 80)
    
    fix_script = '''
# GUI修复脚本 - 确保AI V2.0正确工作

def fix_ai_v2_in_gui():
    """在GUI中修复AI V2.0配置"""
    try:
        # 1. 确保AI V2.0系统已选择
        if hasattr(self, 'ai_mode_var'):
            self.ai_mode_var.set("🚀 AI System V2.0")
            self.on_ai_mode_changed()
        
        # 2. 强制设置高精度模型
        if hasattr(self, 'ai_analyzer_v2'):
            models = self.ai_analyzer_v2.model_manager.get_available_models()
            for key, model_info in models.items():
                if model_info.model_type == 'optimized' and model_info.accuracy >= 0.94:
                    # 加载模型
                    self.ai_analyzer_v2.model_manager.load_model(key)
                    self.ai_analyzer_v2.set_model(key)
                    self.ai_analyzer_v2.set_feature_extractor('advanced')
                    
                    # 更新GUI显示
                    if hasattr(self, 'selected_model_var'):
                        display_name = f"{model_info.name} ({model_info.accuracy:.1%})"
                        self.selected_model_var.set(display_name)
                    
                    print(f"✅ GUI中已设置高精度模型: {model_info.name}")
                    break
        
        # 3. 确保使用正确的分析方法
        return True
        
    except Exception as e:
        print(f"❌ GUI修复失败: {e}")
        return False

# 在GUI的run_ai_v2_analysis方法中添加此修复
'''
    
    with open('gui_ai_v2_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ GUI修复脚本已创建: gui_ai_v2_fix.py")

def main():
    """主函数"""
    print("🔬 深入修复AI V2.0预测问题")
    print("=" * 100)
    
    # 1. 诊断AI V2.0问题
    diagnosis_success = diagnose_ai_v2_issue()
    
    # 2. 测试修正后的预测
    if diagnosis_success:
        test_success = test_corrected_prediction()
    else:
        test_success = False
    
    # 3. 检查GUI按钮映射
    gui_check_success = check_gui_ai_button_mapping()
    
    # 4. 创建GUI修复脚本
    create_gui_fix_script()
    
    # 5. 总结和建议
    print(f"\n📋 问题诊断总结")
    print("=" * 80)
    
    print(f"AI V2.0系统诊断: {'✅ 成功' if diagnosis_success else '❌ 失败'}")
    print(f"预测测试: {'✅ 正确' if test_success else '❌ 错误'}")
    print(f"GUI检查: {'✅ 成功' if gui_check_success else '❌ 失败'}")
    
    if test_success:
        print(f"\n🎉 AI V2.0系统工作正常!")
        print(f"问题可能在GUI界面的按钮映射或模型选择")
        print(f"\n💡 解决建议:")
        print(f"1. 在GUI中确保选择了'🚀 AI System V2.0'")
        print(f"2. 在模型下拉菜单中选择'高精度AI模型 v1.0 (94.0%)'")
        print(f"3. 点击'🚀 开始AI分析'按钮(不是'🤖 AI Analysis')")
        print(f"4. 如果仍有问题，可能需要修改GUI代码")
    else:
        print(f"\n⚠️ AI V2.0系统本身存在问题")
        print(f"需要检查模型文件和特征提取器配置")
    
    print(f"\n🔧 下一步行动:")
    print(f"1. 检查GUI中实际使用的AI分析方法")
    print(f"2. 确认'🚀 开始AI分析'按钮调用的是AI V2.0系统")
    print(f"3. 如需要，修改GUI代码确保正确调用")

if __name__ == "__main__":
    main()
