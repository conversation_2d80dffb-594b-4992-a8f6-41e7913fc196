@echo off
chcp 65001 >nul
title AI桩基完整性分析系统 v3.0

echo.
echo ========================================
echo    AI桩基完整性分析系统 v3.0
echo    AI Pile Integrity Analysis System
echo ========================================
echo.
echo 正在启动现代化图形界面...
echo Starting modern GUI interface...
echo.

cd /d "%~dp0"

python quick_start.py

if errorlevel 1 (
    echo.
    echo 启动失败，尝试备用启动方式...
    echo Launch failed, trying alternative...
    python start_app.py
)

if errorlevel 1 (
    echo.
    echo 错误：无法启动程序
    echo Error: Failed to start the program
    echo.
    echo 请检查：
    echo Please check:
    echo 1. Python是否已正确安装
    echo    Python is properly installed
    echo 2. 所需依赖包是否已安装
    echo    Required packages are installed
    echo 3. 当前目录是否正确
    echo    Current directory is correct
    echo.
    pause
)
